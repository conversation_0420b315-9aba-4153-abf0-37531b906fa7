/**
 * Delay Transformation Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';

export class DelayExecutor extends BaseExecutor {
  id = 'delay';
  name = 'Delay';
  description = 'Add a delay between steps';
  category = 'transformation' as const;

  configSchema = {
    delayTime: {
      type: 'number',
      label: 'Delay Time (seconds)',
      required: true,
      default: 5,
      min: 1,
      max: 300 // Max 5 minutes
    },
    delayUnit: {
      type: 'string',
      label: 'Delay Unit',
      required: true,
      default: 'seconds',
      options: ['seconds', 'minutes']
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      let delayMs = (config.delayTime || 5) * 1000; // Default 5 seconds
      
      // Convert to milliseconds based on unit
      if (config.delayUnit === 'minutes') {
        delayMs = delayMs * 60;
      }
      
      // Cap at 5 minutes for safety
      delayMs = Math.min(delayMs, 5 * 60 * 1000);
      
      console.log(`Delaying workflow execution for ${delayMs}ms`);
      
      // Wait for the specified time
      await new Promise(resolve => setTimeout(resolve, delayMs));
      
      return {
        success: true,
        data: {
          delayed: true,
          delayTime: config.delayTime,
          delayUnit: config.delayUnit,
          delayMs,
          timestamp: new Date().toISOString(),
          // Pass through the previous result
          previousResult: context.lastResult
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }

  validate(nodeData: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!nodeData) {
      errors.push('Node data is required');
    }
    
    const config = nodeData?.config || {};
    
    if (!config.delayTime || config.delayTime < 1) {
      errors.push('Delay time must be at least 1');
    }
    
    if (config.delayUnit === 'minutes' && config.delayTime > 5) {
      errors.push('Maximum delay is 5 minutes');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
}
