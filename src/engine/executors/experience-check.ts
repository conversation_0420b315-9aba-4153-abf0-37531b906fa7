/**
 * Experience Check Condition Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';
import { supabase } from '@/integrations/supabase/client';
import { generateText } from '@/utils/gemini';

export class ExperienceCheckExecutor extends BaseExecutor {
  id = 'experience-check';
  name = 'Experience Check';
  description = 'Check years of experience';
  category = 'condition' as const;

  configSchema = {
    minYears: {
      type: 'number',
      label: 'Minimum Years of Experience',
      required: true,
      default: 3,
      min: 0,
      max: 50
    },
    useAIAnalysis: {
      type: 'boolean',
      label: 'Use AI-Powered Experience Analysis',
      required: false,
      default: false,
      description: 'Enable intelligent experience evaluation that considers context, relevance, and quality of experience'
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId = context.candidateId || (context.lastResult as any)?.candidateId;
      const minYears = parseInt(config.minYears || '3', 10);
      const useAIAnalysis = config.useAIAnalysis || false;
      
      if (!candidateId && !context.lastResult) {
        return {
          success: true,
          data: { result: false, reason: 'No candidate data available' }
        };
      }

      let yearsOfExperience = 0;

      // Try to get from context first
      if (context.candidateExperience) {
        const match = context.candidateExperience.toString().match(/(\d+)/);
        if (match) {
          yearsOfExperience = parseInt(match[1], 10);
        }
      } else if (candidateId) {
        // Fetch from database
        const { data: candidate, error } = await supabase
          .from('candidates')
          .select('experience, ai_summary, skills')
          .eq('id', candidateId)
          .single();

        if (error) {
          throw new Error(`Failed to fetch candidate: ${error.message}`);
        }

        let aiAnalysis: string | undefined;

        if (useAIAnalysis) {
          // Use AI-powered experience analysis
          try {
            const experienceText = candidate?.experience || '';
            const aiSummary = candidate?.ai_summary || '';
            const skills = candidate?.skills || [];

            const prompt = `
Analyze the following candidate experience data and determine if they meet the minimum experience requirement:

Required Experience: ${minYears} years
Candidate Experience: ${experienceText}
AI Summary: ${aiSummary}
Skills: ${Array.isArray(skills) ? skills.join(', ') : skills}

Consider:
1. Total years of relevant experience
2. Quality and depth of experience
3. Progression and growth in roles
4. Relevance to the position requirements
5. Leadership and project experience

Provide a detailed analysis and determine if the candidate meets the ${minYears} year requirement.

Return in JSON format:
{
  "meetsRequirement": true/false,
  "estimatedYears": number,
  "analysis": "Detailed explanation of experience assessment"
}
`;

            const systemPrompt = "You are an expert HR analyst specializing in experience evaluation. Provide accurate, objective assessments of candidate experience based on comprehensive analysis.";

            const response = await generateText(prompt, systemPrompt);
            const cleanedResponse = response.replace(/```json\s*/, '').replace(/```\s*$/, '').trim();
            const aiResult = JSON.parse(cleanedResponse);

            yearsOfExperience = aiResult.estimatedYears || 0;
            aiAnalysis = aiResult.analysis;

            // Use AI determination if available
            if (aiResult.meetsRequirement !== undefined) {
              const result = aiResult.meetsRequirement;
              return {
                success: true,
                data: {
                  result,
                  yearsOfExperience,
                  minYears,
                  threshold: minYears,
                  aiAnalysis,
                  useAIAnalysis: true
                }
              };
            }
          } catch (error) {
            console.error('AI experience analysis failed, falling back to basic parsing:', error);
            // Fall back to basic parsing
          }
        }

        // Basic experience parsing (fallback or when AI is disabled)
        if (candidate?.experience) {
          const expMatch = candidate.experience.toString().match(/(\d+)\s*(?:years?|yrs?)/i);
          if (expMatch) {
            yearsOfExperience = parseInt(expMatch[1], 10);
          }
        } else if (candidate?.ai_summary) {
          const summaryMatch = candidate.ai_summary.match(/(\d+)\s*(?:years?|yrs?)\s*(?:of\s*)?experience/i);
          if (summaryMatch) {
            yearsOfExperience = parseInt(summaryMatch[1], 10);
          }
        }
      }

      const result = yearsOfExperience >= minYears;

      return {
        success: true,
        data: {
          result,
          yearsOfExperience,
          minYears,
          threshold: minYears,
          useAIAnalysis: false
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }
}
