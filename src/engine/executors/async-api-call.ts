/**
 * Async API Call Executor - Demonstrates async handling with retry and timeout
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';

export class AsyncApiCallExecutor extends BaseExecutor {
  id = 'async-api-call';
  name = 'Async API Call';
  description = 'Make an asynchronous API call with retry and timeout support';
  category = 'action' as const;

  configSchema = {
    url: {
      type: 'string',
      label: 'API URL',
      required: true
    },
    method: {
      type: 'string',
      label: 'HTTP Method',
      required: true,
      default: 'GET',
      options: ['GET', 'POST', 'PUT', 'DELETE']
    },
    headers: {
      type: 'object',
      label: 'Headers',
      required: false
    },
    body: {
      type: 'object',
      label: 'Request Body',
      required: false
    },
    timeout: {
      type: 'number',
      label: 'Timeout (ms)',
      required: false,
      default: 5000,
      min: 100,
      max: 30000
    },
    retries: {
      type: 'number',
      label: 'Number of Retries',
      required: false,
      default: 3,
      min: 0,
      max: 5
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const {
        url,
        method = 'GET',
        headers = {},
        body,
        timeout = 5000,
        retries = 3
      } = config;

      if (!url) {
        throw new Error('API URL is required');
      }

      // Simulate API call with fetch
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      try {
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            ...headers
          },
          body: body ? JSON.stringify(body) : undefined,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        return {
          success: true,
          data: {
            status: response.status,
            statusText: response.statusText,
            data,
            timestamp: new Date().toISOString()
          }
        };
      } catch (error) {
        clearTimeout(timeoutId);
        
        if (error instanceof Error && error.name === 'AbortError') {
          throw new Error(`Request timeout after ${timeout}ms`);
        }
        
        throw error;
      }
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }

  validate(nodeData: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const config = nodeData?.config || {};

    if (!config.url) {
      errors.push('API URL is required');
    } else {
      try {
        new URL(config.url);
      } catch {
        errors.push('Invalid URL format');
      }
    }

    if (config.method && !['GET', 'POST', 'PUT', 'DELETE'].includes(config.method)) {
      errors.push('Invalid HTTP method');
    }

    if (config.timeout && (config.timeout < 100 || config.timeout > 30000)) {
      errors.push('Timeout must be between 100ms and 30000ms');
    }

    if (config.retries && (config.retries < 0 || config.retries > 5)) {
      errors.push('Retries must be between 0 and 5');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  canExecute(context: ExecutionContext): boolean {
    // This node can always execute as long as it has valid configuration
    return true;
  }
}
