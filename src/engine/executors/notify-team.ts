/**
 * Notify Team Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';
import { supabase } from '@/integrations/supabase/client';

export class NotifyTeamExecutor extends BaseExecutor {
  id = 'notify-team';
  name = 'Notify Team';
  description = 'Send notification to team';
  category = 'action' as const;

  configSchema = {
    channel: {
      type: 'string',
      label: 'Notification Channel',
      required: true,
      options: ['email', 'slack', 'in-app', 'all']
    },
    teamMembers: {
      type: 'string',
      label: 'Team Members',
      required: true,
      placeholder: 'all, hiring-managers, recruiters, or specific emails'
    },
    customMessage: {
      type: 'text',
      label: 'Custom Message',
      required: false
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const channel = config.channel || 'email';
      const teamMembers = config.teamMembers || 'all';
      const message = config.customMessage || 'Team notification from workflow';

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Create notification message
      const { data: messageData, error: messageError } = await supabase
        .from('messages')
        .insert({
          sender_name: 'Workflow Notification',
          sender_email: '<EMAIL>',
          sender_role: 'System',
          content: `Team notification: ${message}\nRecipients: ${teamMembers}\nChannel: ${channel}`,
          status: 'unread',
          user_id: user.id
        })
        .select()
        .single();
      
      if (messageError) {
        throw new Error(`Failed to send notification: ${messageError.message}`);
      }

      return {
        success: true,
        data: {
          sent: true,
          channel,
          teamMembers,
          message,
          messageId: messageData?.id,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }
}
