/**
 * Location Check Condition Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';
import { supabase } from '@/integrations/supabase/client';

export class LocationCheckExecutor extends BaseExecutor {
  id = 'location-check';
  name = 'Location Check';
  description = 'Verify location requirements';
  category = 'condition' as const;

  configSchema = {
    locationType: {
      type: 'string',
      label: 'Work Location Type',
      required: true,
      options: ['onsite', 'hybrid', 'remote'],
      default: 'onsite'
    },
    specificLocation: {
      type: 'string',
      label: 'Specific Location (optional)',
      required: false,
      placeholder: 'e.g., San Francisco, CA'
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId = context.candidateId || context.lastResult?.candidateId;
      const locationType = config.locationType || 'onsite';
      const specificLocation = config.specificLocation || '';
      
      if (!candidateId) {
        return {
          success: true,
          data: { result: false, reason: 'No candidate data available' }
        };
      }

      // Fetch candidate location data
      const { data: candidate, error } = await supabase
        .from('candidates')
        .select('location, remote_preference, ai_summary')
        .eq('id', candidateId)
        .single();

      if (error) {
        throw new Error(`Failed to fetch candidate: ${error.message}`);
      }

      let candidateLocationType = 'unknown';
      const candidateLocation = candidate?.location || '';
      
      // Determine candidate's location preference
      if (candidate?.remote_preference !== undefined) {
        candidateLocationType = candidate.remote_preference ? 'remote' : 'onsite';
      } else {
        // Try to infer from AI summary or location field
        const locationText = (candidateLocation + ' ' + (candidate?.ai_summary || '')).toLowerCase();
        
        if (locationText.includes('remote') || locationText.includes('work from home')) {
          candidateLocationType = 'remote';
        } else if (locationText.includes('hybrid')) {
          candidateLocationType = 'hybrid';
        } else if (candidateLocation) {
          candidateLocationType = 'onsite';
        }
      }

      // Check location match
      let result = false;
      let matchReason = '';

      if (locationType === 'remote') {
        // Remote jobs match all candidates
        result = true;
        matchReason = 'Remote position accepts all locations';
      } else if (locationType === 'hybrid') {
        // Hybrid jobs match hybrid and onsite candidates
        result = candidateLocationType !== 'remote';
        matchReason = result ? 'Hybrid position matches non-remote candidates' : 'Candidate prefers remote only';
      } else if (locationType === 'onsite') {
        // Onsite jobs need location match
        if (candidateLocationType === 'remote') {
          result = false;
          matchReason = 'Candidate prefers remote, position is onsite';
        } else if (specificLocation && candidateLocation) {
          // Check if locations match (simple string comparison)
          result = candidateLocation.toLowerCase().includes(specificLocation.toLowerCase()) ||
                  specificLocation.toLowerCase().includes(candidateLocation.toLowerCase());
          matchReason = result ? 'Location match found' : 'Location mismatch';
        } else {
          // If no specific location required, any non-remote candidate matches
          result = candidateLocationType !== 'remote';
          matchReason = result ? 'Candidate available for onsite work' : 'No location match';
        }
      }

      return {
        success: true,
        data: {
          result,
          candidateLocation,
          candidateLocationType,
          requiredLocationType: locationType,
          specificLocation,
          matchReason
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }
}
