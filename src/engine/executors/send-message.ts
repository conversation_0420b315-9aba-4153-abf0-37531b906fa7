/**
 * Send Message Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';
import { supabase } from '@/integrations/supabase/client';
import { MessagingService, CandidatesService, TimelineService } from '@/services';

export class SendMessageExecutor extends BaseExecutor {
  id = 'send-message';
  name = 'Send Message';
  description = 'Send message to candidate';
  category = 'output' as const;

  configSchema = {
    template: {
      type: 'string',
      label: 'Message Template',
      required: true,
      options: ['general', 'follow-up', 'rejection', 'next-steps', 'custom']
    },
    messageContent: {
      type: 'text',
      label: 'Message Content',
      required: false,
      placeholder: 'Leave empty to use template'
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId = context.candidateId || context.lastResult?.candidateId;
      const template = config.template || 'general';
      const messageContent = config.messageContent || '';
      
      if (!candidateId) {
        throw new Error('No candidate ID found in context');
      }

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get candidate details
      const candidate = await CandidatesService.getCandidate(candidateId);
      
      if (!candidate) {
        throw new Error('Candidate not found');
      }

      // Get template if needed
      let templateContent = messageContent;
      if (template && !messageContent) {
        const templateData = await MessagingService.getMessageTemplate(template);
        if (templateData) {
          templateContent = templateData.content;
        }
      }

      // Create message record
      const message = await MessagingService.createWorkflowMessage(
        user.id,
        templateContent || `Message to ${candidate.name} using template: ${template}`
      );

      // Create timeline entry
      await TimelineService.createGeneralEntry(
        candidateId,
        user.id,
        'Message Sent',
        `Message sent to candidate using template: ${template}`
      );

      return {
        success: true,
        data: {
          sent: true,
          candidateId,
          candidateName: candidate.name,
          candidateEmail: candidate.email,
          template,
          messageContent: templateContent,
          messageId: message.id,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }

  canExecute(context: ExecutionContext): boolean {
    return !!(context.candidateId || context.lastResult?.candidateId);
  }
}
