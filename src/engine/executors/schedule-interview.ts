/**
 * Schedule Interview Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';
import { supabase } from '@/integrations/supabase/client';
import { CandidatesService, EventsService, TimelineService } from '@/services';

export class ScheduleInterviewExecutor extends BaseExecutor {
  id = 'schedule-interview';
  name = 'Schedule Interview';
  description = 'Automatically schedule interviews';
  category = 'action' as const;

  configSchema = {
    interviewType: {
      type: 'string',
      label: 'Interview Type',
      required: true,
      options: ['Technical', 'Behavioral', 'Cultural Fit', 'Final']
    },
    duration: {
      type: 'number',
      label: 'Duration (minutes)',
      required: true,
      default: 60
    },
    sendCalendarInvite: {
      type: 'boolean',
      label: 'Send Calendar Invite',
      default: true
    },
    location: {
      type: 'string',
      label: 'Location',
      required: false
    },
    meetingPlatform: {
      type: 'string',
      label: 'Meeting Platform',
      default: 'Zoom'
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId = context.candidateId || context.lastResult?.candidateId;
      
      if (!candidateId) {
        throw new Error('No candidate ID found in context');
      }

      // Get candidate details
      const candidate = await CandidatesService.getCandidate(candidateId);
      
      if (!candidate) {
        throw new Error('Candidate not found');
      }

      // Calculate interview date (3 business days from now)
      const interviewDate = new Date();
      interviewDate.setDate(interviewDate.getDate() + 3);

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Create interview record via an internal EventsService call
      const interview = await EventsService.createInterviewEvent(
        user.id,
        candidate.name,
        config.interviewType || 'Technical Interview',
        interviewDate,
        parseInt(config.duration) || 60,
        config.location || null,
        config.meetingLink || null
      );

      // Create timeline entry
      await TimelineService.createInterviewEntry(
        candidateId,
        user.id,
        `${config.interviewType || 'Technical'} Interview Scheduled`,
        `Interview scheduled for ${new Date(interviewDate).toLocaleDateString()} at ${new Date(interviewDate).toLocaleTimeString()}`,
        interviewDate.toISOString(),
        'upcoming'
      );

      // Create calendar event if requested
      if (config.sendCalendarInvite) {
        await EventsService.createInterviewEvent(
          user.id,
          candidate.name,
          config.interviewType || 'Technical Interview',
          interviewDate,
          parseInt(config.duration) || 60,
          config.location || null,
          config.meetingLink || null
        );
      }

      return {
        success: true,
        data: {
          scheduled: true,
          candidateId,
          candidateName: candidate.name,
          interviewId: interview.id,
          interviewType: config.interviewType || 'Technical Interview',
          duration: config.duration || 60,
          interviewDate: interviewDate.toISOString(),
          calendarInviteSent: config.sendCalendarInvite,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }

  canExecute(context: ExecutionContext): boolean {
    return !!(context.candidateId || context.lastResult?.candidateId);
  }
}
