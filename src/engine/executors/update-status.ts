/**
 * Update Status Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';
import { supabase } from '@/integrations/supabase/client';
import { CandidatesService, TimelineService } from '@/services';

export class UpdateStatusExecutor extends BaseExecutor {
  id = 'update-status';
  name = 'Update Status';
  description = 'Update application status';
  category = 'output' as const;

  configSchema = {
    newStatus: {
      type: 'string',
      label: 'New Status',
      required: true,
      options: ['screening', 'interview', 'assessment', 'reference_check', 'offer', 'hired', 'rejected', 'withdrawn']
    },
    statusNote: {
      type: 'text',
      label: 'Status Note',
      required: false
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId = context.candidateId || context.lastResult?.candidateId;
      
      if (!candidateId) {
        throw new Error('No candidate ID found in context');
      }

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get candidate details
      const candidate = await CandidatesService.getCandidate(candidateId);
      
      if (!candidate) {
        throw new Error('Candidate not found');
      }

      // Create timeline entry
      const timeline = await TimelineService.createTimelineEntry({
        candidate_id: candidateId,
        user_id: user.id,
        event_type: 'status_change',
        title: `Status Updated: ${config.newStatus}`,
        description: config.statusNote || `Candidate status updated to ${config.newStatus}`,
        event_date: new Date().toISOString(),
        status: 'completed'
      });

      return {
        success: true,
        data: {
          updated: true,
          candidateId,
          candidateName: candidate.name,
          newStatus: config.newStatus,
          statusNote: config.statusNote,
          timelineId: timeline.id,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }

  canExecute(context: ExecutionContext): boolean {
    return !!(context.candidateId || context.lastResult?.candidateId);
  }
}
