/**
 * Add to Pool Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';
import { supabase } from '@/integrations/supabase/client';
import { CandidatesService, TimelineService } from '@/services';

export class AddToPoolExecutor extends BaseExecutor {
  id = 'add-to-pool';
  name = 'Add to Talent Pool';
  description = 'Add to specific talent pool';
  category = 'output' as const;

  configSchema = {
    poolName: {
      type: 'string',
      label: 'Talent Pool Name',
      required: true,
      placeholder: 'e.g., Senior Engineers, Future Opportunities'
    },
    tags: {
      type: 'text',
      label: 'Additional Tags (comma-separated)',
      required: false
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId = context.candidateId || context.lastResult?.candidateId;
      const poolName = config.poolName || 'general';
      const tags = config.tags || '';
      
      if (!candidateId) {
        throw new Error('No candidate ID found in context');
      }

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Parse and merge tags
      const tagArray = tags ? tags.split(',').map(t => t.trim()) : [];
      
      // Add candidate to pool using service
      const candidate = await CandidatesService.addToPool(candidateId, poolName, tagArray);

      // Create timeline entry
      await TimelineService.createGeneralEntry(
        candidateId,
        user.id,
        `Added to Talent Pool: ${poolName}`,
        `Candidate added to ${poolName} talent pool${tagArray.length > 0 ? ` with tags: ${tagArray.join(', ')}` : ''}`
      );

      return {
        success: true,
        data: {
          added: true,
          candidateId,
          candidateName: candidate.name,
          poolName,
          tags: candidate.tags || [],
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }

  canExecute(context: ExecutionContext): boolean {
    return !!(context.candidateId || context.lastResult?.candidateId);
  }
}
