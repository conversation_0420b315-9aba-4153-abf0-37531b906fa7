/**
 * Data Filter Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';

export class DataFilterExecutor extends BaseExecutor {
  id = 'data-filter';
  name = 'Data Filter';
  description = 'Filter data between steps';
  category = 'transformation' as const;

  configSchema = {
    filterType: {
      type: 'string',
      label: 'Filter Type',
      required: true,
      options: ['include', 'exclude', 'range', 'custom']
    },
    filterField: {
      type: 'string',
      label: 'Field to Filter',
      required: false,
      placeholder: 'e.g., score, status, skills'
    },
    filterOperator: {
      type: 'string',
      label: 'Operator',
      required: false,
      options: ['equals', 'not_equals', 'contains', 'greater_than', 'less_than', 'in', 'not_in']
    },
    filterValue: {
      type: 'text',
      label: 'Filter Value',
      required: false,
      placeholder: 'Value to compare against'
    },
    customFilter: {
      type: 'text',
      label: 'Custom Filter (JavaScript)',
      required: false,
      placeholder: '// return true to include, false to exclude'
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const filterType = config.filterType || 'include';
      let filteredData: any = null;
      const dataToFilter = context.lastResult;

      // If the data is an array, filter each item
      if (Array.isArray(dataToFilter)) {
        filteredData = dataToFilter.filter(item => 
          this.applyFilter(item, config, filterType)
        );
      } else {
        // For single items, return the item if it passes the filter, null otherwise
        const passes = this.applyFilter(dataToFilter, config, filterType);
        filteredData = passes ? dataToFilter : null;
      }

      return {
        success: true,
        data: {
          filterType,
          filteredData,
          originalData: dataToFilter,
          itemsFiltered: Array.isArray(dataToFilter) ? 
            dataToFilter.length - (Array.isArray(filteredData) ? filteredData.length : 0) : 0,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }

  private applyFilter(item: any, config: any, filterType: string): boolean {
    switch (filterType) {
      case 'include':
      case 'exclude': {
        const matches = this.evaluateCondition(item, config);
        return filterType === 'include' ? matches : !matches;
      }

      case 'range':
        return this.evaluateRange(item, config);

      case 'custom':
        return this.evaluateCustomFilter(item, config.customFilter || '');

      default:
        return true;
    }
  }

  private evaluateCondition(item: any, config: any): boolean {
    const field = config.filterField;
    const operator = config.filterOperator || 'equals';
    const filterValue = config.filterValue;

    if (!field || filterValue === undefined) return true;

    const itemValue = this.getFieldValue(item, field);

    switch (operator) {
      case 'equals':
        return itemValue == filterValue;
      
      case 'not_equals':
        return itemValue != filterValue;
      
      case 'contains': {
        if (typeof itemValue === 'string' && typeof filterValue === 'string') {
          return itemValue.toLowerCase().includes(filterValue.toLowerCase());
        }
        if (Array.isArray(itemValue)) {
          return itemValue.includes(filterValue);
        }
        return false;
      }
      
      case 'greater_than':
        return Number(itemValue) > Number(filterValue);
      
      case 'less_than':
        return Number(itemValue) < Number(filterValue);
      
      case 'in': {
        const values = filterValue.split(',').map(v => v.trim());
        return values.includes(String(itemValue));
      }
      
      case 'not_in': {
        const notValues = filterValue.split(',').map(v => v.trim());
        return !notValues.includes(String(itemValue));
      }
      
      default:
        return true;
    }
  }

  private evaluateRange(item: any, config: any): boolean {
    const field = config.filterField;
    if (!field) return true;

    const itemValue = Number(this.getFieldValue(item, field));
    
    // Parse range from filterValue (e.g., "10-50")
    const rangeMatch = (config.filterValue || '').match(/(\d+)-(\d+)/);
    if (!rangeMatch) return true;

    const min = Number(rangeMatch[1]);
    const max = Number(rangeMatch[2]);

    return itemValue >= min && itemValue <= max;
  }

  private evaluateCustomFilter(item: any, customCode: string): boolean {
    try {
      // Create a safe function that only has access to the item
      const filterFunction = new Function('item', `
        ${customCode}
      `);
      
      return filterFunction(item) === true;
    } catch (error) {
      console.error('Error in custom filter:', error);
      return true; // Default to including the item on error
    }
  }

  private getFieldValue(item: any, field: string): any {
    if (!item || typeof item !== 'object') return item;

    const parts = field.split('.');
    let current = item;

    for (const part of parts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        return undefined;
      }
    }

    return current;
  }
}
