/**
 * AI Screen Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';
import { supabase } from '@/integrations/supabase/client';
import { screenCandidate } from '@/utils/gemini';
import { CandidatesService, TimelineService } from '@/services';

export class AIScreenExecutor extends BaseExecutor {
  id = 'ai-screen';
  name = 'AI Screen';
  description = 'Screen candidate using AI';
  category = 'action' as const;

  configSchema = {
    criteria: {
      type: 'string',
      label: 'Screening Criteria',
      required: true,
      options: ['comprehensive', 'technical', 'cultural_fit', 'document_verification']
    },
    minScore: {
      type: 'number',
      label: 'Minimum Score (%)',
      required: true,
      default: 75,
      min: 0,
      max: 100
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId = context.candidateId || context.lastResult?.candidateId;
      const jobId = context.jobId || context.lastResult?.jobId;
      
      if (!candidateId) {
        throw new Error('No candidate ID found in context');
      }

      // Get candidate details
      const candidate = await CandidatesService.getCandidate(candidateId);
      
      if (!candidate) {
        throw new Error('Candidate not found');
      }

      // Get job details if available
      let jobDescription = '';
      if (jobId) {
        const { data: job, error: jobError } = await supabase
          .from('jobs')
          .select('*')
          .eq('id', jobId)
          .single();
        
        if (!jobError && job) {
          jobDescription = job.description;
        }
      }

      // Perform AI screening
      const screeningResult = await screenCandidate(
        candidate, 
        jobDescription || `${candidate.role} position`
      );

      // Create timeline entry
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await TimelineService.createGeneralEntry(
          candidateId,
          user.id,
          'AI Screening Completed',
          `AI screening completed with score: ${screeningResult.score}%. Criteria: ${config.criteria || 'comprehensive'}`
        );
      }

      const passed = screeningResult.score >= parseInt(config.minScore || '75', 10);

      return {
        success: true,
        data: {
          score: screeningResult.score,
          passed,
          candidateId,
          candidateName: candidate.name,
          criteria: config.criteria || 'comprehensive',
          strengths: screeningResult.strengths,
          gaps: screeningResult.gaps,
          summary: screeningResult.summary,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }

  canExecute(context: ExecutionContext): boolean {
    return !!(context.candidateId || context.lastResult?.candidateId);
  }
}
