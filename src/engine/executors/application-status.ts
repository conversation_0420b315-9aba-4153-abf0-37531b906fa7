/**
 * Application Status Trigger Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';

export class ApplicationStatusTriggerExecutor extends BaseExecutor {
  id = 'application-status';
  name = 'Status Change';
  description = 'Triggers when application status changes';
  category = 'trigger' as const;

  configSchema = {
    fromStatus: {
      type: 'string',
      label: 'From Status (optional)',
      required: false,
      options: ['any', 'new', 'screening', 'interview', 'assessment', 'reference_check', 'offer', 'hired', 'rejected', 'withdrawn']
    },
    toStatus: {
      type: 'string',
      label: 'To Status',
      required: true,
      options: ['any', 'screening', 'interview', 'assessment', 'reference_check', 'offer', 'hired', 'rejected', 'withdrawn']
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      
      // In a real implementation, this would be triggered by a database event
      // For now, it validates the configuration and returns success
      return {
        success: true,
        data: {
          triggered: true,
          type: 'application-status',
          timestamp: new Date().toISOString(),
          fromStatus: config.fromStatus || 'any',
          toStatus: config.toStatus || 'any',
          candidateId: context.candidateId,
          context: { ...context }
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }

  validate(nodeData: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!nodeData) {
      errors.push('Node data is required');
    }
    
    if (!nodeData.config?.toStatus || nodeData.config.toStatus === 'any') {
      errors.push('Target status must be specified');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  canExecute(context: ExecutionContext): boolean {
    // Triggers can always execute
    return true;
  }
}
