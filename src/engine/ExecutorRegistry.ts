/**
 * Executor Registry - Manages all node executors
 */

import { NodeExecutor } from './types';

export class ExecutorRegistry {
  private static instance: ExecutorRegistry;
  private executors: Map<string, NodeExecutor> = new Map();
  private categories: Map<string, Set<string>> = new Map();

  private constructor() {
    // Initialize category sets
    this.categories.set('trigger', new Set());
    this.categories.set('action', new Set());
    this.categories.set('condition', new Set());
    this.categories.set('transformation', new Set());
    this.categories.set('output', new Set());
  }

  public static getInstance(): ExecutorRegistry {
    if (!ExecutorRegistry.instance) {
      ExecutorRegistry.instance = new ExecutorRegistry();
    }
    return ExecutorRegistry.instance;
  }

  /**
   * Register a new executor
   */
  public register(executor: NodeExecutor): void {
    if (this.executors.has(executor.id)) {
      console.warn(`Executor with id "${executor.id}" is already registered. Overwriting.`);
    }

    this.executors.set(executor.id, executor);
    
    // Add to category index
    const categorySet = this.categories.get(executor.category);
    if (categorySet) {
      categorySet.add(executor.id);
    }

    console.log(`Registered executor: ${executor.id} (${executor.category})`);
  }

  /**
   * Get an executor by ID
   */
  public getExecutor(id: string): NodeExecutor | undefined {
    return this.executors.get(id);
  }

  /**
   * Get all executors in a category
   */
  public getExecutorsByCategory(category: string): NodeExecutor[] {
    const categorySet = this.categories.get(category);
    if (!categorySet) return [];

    return Array.from(categorySet)
      .map(id => this.executors.get(id))
      .filter((executor): executor is NodeExecutor => executor !== undefined);
  }

  /**
   * Get all registered executors
   */
  public getAllExecutors(): NodeExecutor[] {
    return Array.from(this.executors.values());
  }

  /**
   * Check if an executor is registered
   */
  public hasExecutor(id: string): boolean {
    return this.executors.has(id);
  }

  /**
   * Unregister an executor
   */
  public unregister(id: string): boolean {
    const executor = this.executors.get(id);
    if (!executor) return false;

    // Remove from category index
    const categorySet = this.categories.get(executor.category);
    if (categorySet) {
      categorySet.delete(id);
    }

    // Remove from executors map
    return this.executors.delete(id);
  }

  /**
   * Clear all registered executors
   */
  public clear(): void {
    this.executors.clear();
    this.categories.forEach(set => set.clear());
  }

  /**
   * Get executor count
   */
  public getCount(): number {
    return this.executors.size;
  }

  /**
   * Get count by category
   */
  public getCountByCategory(category: string): number {
    const categorySet = this.categories.get(category);
    return categorySet ? categorySet.size : 0;
  }
}

// Export singleton instance
export const executorRegistry = ExecutorRegistry.getInstance();
