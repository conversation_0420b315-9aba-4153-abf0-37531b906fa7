/**
 * Core types for the execution engine
 */

import { RealtimeChannel } from '@supabase/supabase-js';

export interface ExecutionContext {
  [key: string]: unknown;
  candidateId?: string;
  jobId?: string;
  userId?: string;
  lastResult?: unknown;
  lastNodeId?: string;
  lastNodeType?: string;
}

export interface NodeExecutorConfig {
  timeout?: number; // milliseconds
  retries?: number;
  retryDelay?: number; // milliseconds
  parallel?: boolean;
}

export interface NodeExecutorResult {
  success: boolean;
  data?: Record<string, unknown> | unknown[] | string | number | boolean | null;
  error?: Error;
  duration?: number;
  retryCount?: number;
}

export interface NodeExecutor {
  id: string;
  name: string;
  description: string;
  category: 'trigger' | 'action' | 'condition' | 'transformation' | 'output';
  
  // Configuration schema for the node
  configSchema?: Record<string, {
    type: string;
    label: string;
    required?: boolean;
    default?: unknown;
    options?: string[] | number[];
    min?: number;
    max?: number;
    placeholder?: string;
  }>;
  
  // Execute the node
  execute(
    nodeData: Record<string, unknown>,
    context: ExecutionContext,
    config?: NodeExecutorConfig
  ): Promise<NodeExecutorResult>;
  
  // Validate node configuration
  validate?(nodeData: Record<string, unknown>): { valid: boolean; errors: string[] };
  
  // Check if node can be executed in current context
  canExecute?(context: ExecutionContext): boolean;
}

export interface ExecutionEvent {
  type: 'started' | 'success' | 'error' | 'progress' | 'retry';
  nodeId: string;
  nodeName: string;
  timestamp: Date;
  data?: Record<string, unknown> | unknown[] | string | number | boolean | null;
  error?: Error;
  retryCount?: number;
  progress?: number;
}

export interface WorkflowExecutionConfig {
  enableRealtime?: boolean;
  realtimeChannel?: string;
  maxParallelNodes?: number;
  globalTimeout?: number; // milliseconds
  defaultNodeTimeout?: number; // milliseconds
  defaultRetries?: number;
  defaultRetryDelay?: number; // milliseconds
}

export interface EdgeExecutionStrategy {
  type: 'sequential' | 'parallel' | 'wait-all' | 'race';
  waitTimeout?: number; // milliseconds for wait strategies
}

export interface ParallelExecutionResult {
  nodeId: string;
  result: NodeExecutorResult;
  completed: boolean;
  startTime: Date;
  endTime?: Date;
}

export interface WaitAggregationStrategy {
  type: 'all-success' | 'any-success' | 'majority-success' | 'custom';
  customHandler?: (results: ParallelExecutionResult[]) => boolean;
  timeout?: number;
}

export interface ExecutionMetrics {
  startTime: Date;
  endTime?: Date;
  totalDuration?: number;
  nodeExecutions: {
    nodeId: string;
    duration: number;
    retries: number;
    success: boolean;
  }[];
  parallelExecutions: number;
  totalRetries: number;
}
