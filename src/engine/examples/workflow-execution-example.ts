/**
 * Example usage of the refactored Workflow Execution Engine
 * Demonstrates async handling, parallel execution, and real-time events
 */

import { WorkflowExecutionEngine } from '@/components/ai/workflow/WorkflowExecutionEngine';
import { realtimePublisher } from '@/engine/RealtimeEventPublisher';
import { supabase } from '@/integrations/supabase/client';

// Example workflow with parallel execution
const exampleWorkflow = {
  nodes: [
    {
      id: 'trigger-1',
      data: {
        id: 'new-application',
        type: 'trigger',
        category: 'triggers',
        label: 'New Application',
        config: {}
      }
    },
    {
      id: 'screen-1',
      data: {
        id: 'ai-screen',
        type: 'action',
        category: 'actions',
        label: 'AI Screen Resume',
        config: {
          screeningCriteria: ['skills', 'experience'],
          threshold: 70
        }
      }
    },
    {
      id: 'parallel-check-1',
      data: {
        id: 'parallel-aggregator',
        type: 'transformation',
        category: 'transformations',
        label: 'Parallel Background Checks',
        config: {
          parallel: true,
          executionStrategy: { type: 'parallel' }
        }
      }
    },
    {
      id: 'education-check',
      data: {
        id: 'education-check',
        type: 'condition',
        category: 'conditions',
        label: 'Verify Education',
        config: {
          timeout: 10000,
          retries: 2
        }
      }
    },
    {
      id: 'experience-check',
      data: {
        id: 'experience-check',
        type: 'condition',
        category: 'conditions',
        label: 'Verify Experience',
        config: {
          timeout: 10000,
          retries: 2
        }
      }
    },
    {
      id: 'api-call-1',
      data: {
        id: 'async-api-call',
        type: 'action',
        category: 'actions',
        label: 'Background Check API',
        config: {
          url: 'https://api.example.com/background-check',
          method: 'POST',
          timeout: 15000,
          retries: 3
        }
      }
    },
    {
      id: 'wait-aggregation',
      data: {
        id: 'parallel-aggregator',
        type: 'transformation',
        category: 'transformations',
        label: 'Wait for All Checks',
        config: {
          aggregationType: 'all-success',
          waitTimeout: 30000,
          executionStrategy: { type: 'wait-all', waitTimeout: 30000 }
        }
      }
    },
    {
      id: 'send-notification',
      data: {
        id: 'send-email',
        type: 'action',
        category: 'actions',
        label: 'Send Notification',
        config: {
          template: 'screening-complete'
        }
      }
    }
  ],
  edges: [
    { id: 'e1', source: 'trigger-1', target: 'screen-1' },
    { id: 'e2', source: 'screen-1', target: 'parallel-check-1' },
    { id: 'e3', source: 'parallel-check-1', target: 'education-check' },
    { id: 'e4', source: 'parallel-check-1', target: 'experience-check' },
    { id: 'e5', source: 'parallel-check-1', target: 'api-call-1' },
    { id: 'e6', source: 'education-check', target: 'wait-aggregation' },
    { id: 'e7', source: 'experience-check', target: 'wait-aggregation' },
    { id: 'e8', source: 'api-call-1', target: 'wait-aggregation' },
    { id: 'e9', source: 'wait-aggregation', target: 'send-notification' }
  ]
};

/**
 * Example: Execute workflow with real-time event monitoring
 */
export async function executeWorkflowWithMonitoring(
  workflowId: string,
  userId: string,
  candidateId: string
) {
  // Subscribe to workflow events
  const channelName = `workflow_progress_${workflowId}`;
  const channel = supabase.channel(channelName);
  
  // Listen for workflow events
  channel.on('broadcast', { event: 'workflow_events' }, (payload) => {
    console.log('Workflow events received:', payload);
    
    // Handle events
    const events = payload.payload.events;
    events.forEach((event: any) => {
      switch (event.type) {
        case 'started':
          console.log(`Node ${event.nodeName} started`);
          break;
        case 'success':
          console.log(`Node ${event.nodeName} completed successfully`);
          break;
        case 'error':
          console.error(`Node ${event.nodeName} failed:`, event.error);
          break;
        case 'progress':
          console.log(`Progress update:`, event.data);
          break;
      }
    });
  });
  
  await channel.subscribe();

  // Create execution engine instance
  const engine = new WorkflowExecutionEngine(
    workflowId,
    userId,
    exampleWorkflow.nodes,
    exampleWorkflow.edges,
    { candidateId }
  );

  try {
    // Execute workflow
    console.log('Starting workflow execution...');
    const result = await engine.execute();
    
    if (result.success) {
      console.log('Workflow executed successfully!');
      console.log('Execution path:', result.executionPath);
      console.log('Final output:', result.output);
    } else {
      console.error('Workflow execution failed:', result.error);
    }
    
    // Show execution logs
    console.log('\nExecution Logs:');
    result.logs.forEach(log => {
      console.log(`[${log.timestamp.toISOString()}] ${log.nodeId}: ${log.status} - ${log.message}`);
    });
    
    return result;
  } finally {
    // Cleanup
    await channel.unsubscribe();
  }
}

/**
 * Example: Execute workflow with custom timeout and retry configuration
 */
export async function executeWorkflowWithCustomConfig(
  workflowId: string,
  userId: string,
  config: {
    globalTimeout?: number;
    maxParallelNodes?: number;
    defaultRetries?: number;
  }
) {
  // Create nodes with custom configurations
  const nodes = exampleWorkflow.nodes.map(node => ({
    ...node,
    data: {
      ...node.data,
      config: {
        ...node.data.config,
        timeout: config.globalTimeout || 30000,
        retries: config.defaultRetries || 2,
        retryDelay: 1000
      }
    }
  }));

  const engine = new WorkflowExecutionEngine(
    workflowId,
    userId,
    nodes,
    exampleWorkflow.edges,
    {}
  );

  return await engine.execute();
}

/**
 * Example: Monitor workflow execution metrics
 */
export async function monitorWorkflowMetrics(workflowId: string) {
  const channelName = `workflow_progress_${workflowId}`;
  
  // Get channel statistics
  const stats = realtimePublisher.getChannelStats(channelName);
  console.log('Channel stats:', stats);
  
  // Monitor all channels
  const allStats = realtimePublisher.getAllChannelStats();
  console.log('All channel stats:', Array.from(allStats.entries()));
}

// Usage example
async function main() {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('User not authenticated');
    return;
  }

  // Execute workflow with monitoring
  await executeWorkflowWithMonitoring(
    'workflow-123',
    user.id,
    'candidate-456'
  );
}

// Run example if executed directly
if (require.main === module) {
  main().catch(console.error);
}
