/**
 * Base Executor - Abstract class for all node executors
 */

import { 
  NodeExecutor, 
  NodeExecutorConfig, 
  NodeExecutorResult, 
  ExecutionContext 
} from './types';

export abstract class BaseExecutor implements NodeExecutor {
  abstract id: string;
  abstract name: string;
  abstract description: string;
  abstract category: 'trigger' | 'action' | 'condition' | 'transformation' | 'output';
  
  configSchema?: Record<string, {
    type: string;
    label: string;
    required?: boolean;
    default?: unknown;
    options?: string[] | number[];
    min?: number;
    max?: number;
    placeholder?: string;
  }>;

  /**
   * Execute with retry and timeout logic
   */
  async execute(
    nodeData: Record<string, unknown>,
    context: ExecutionContext,
    config?: NodeExecutorConfig
  ): Promise<NodeExecutorResult> {
    const startTime = Date.now();
    const timeout = config?.timeout || 30000; // Default 30s timeout
    const retries = config?.retries || 0;
    const retryDelay = config?.retryDelay || 1000;

    let lastError: Error | undefined;
    let retryCount = 0;

    // Attempt execution with retries
    for (let attempt = 0; attempt <= retries; attempt++) {
      if (attempt > 0) {
        retryCount = attempt;
        console.log(`Retrying ${this.id} execution (attempt ${attempt + 1}/${retries + 1})`);
        await this.delay(retryDelay * attempt); // Exponential backoff
      }

      try {
        // Execute with timeout
        const result = await this.executeWithTimeout(
          () => this.executeInternal(nodeData, context),
          timeout
        );

        const duration = Date.now() - startTime;
        return {
          ...result,
          duration,
          retryCount
        };
      } catch (error) {
        lastError = error as Error;
        console.error(`Error in ${this.id} execution (attempt ${attempt + 1}):`, error);

        // Don't retry on timeout errors
        if (error instanceof Error && error.message.includes('timeout')) {
          break;
        }
      }
    }

    // All attempts failed
    const duration = Date.now() - startTime;
    return {
      success: false,
      error: lastError || new Error('Unknown error'),
      duration,
      retryCount
    };
  }

  /**
   * Internal execution logic to be implemented by subclasses
   */
  protected abstract executeInternal(
    nodeData: Record<string, unknown>,
    context: ExecutionContext
  ): Promise<NodeExecutorResult>;

  /**
   * Execute with timeout
   */
  private async executeWithTimeout<T>(
    fn: () => Promise<T>,
    timeoutMs: number
  ): Promise<T> {
    return Promise.race([
      fn(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error(`Execution timeout after ${timeoutMs}ms`)), timeoutMs)
      )
    ]);
  }

  /**
   * Delay helper
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Default validation - can be overridden
   */
  validate(nodeData: Record<string, unknown>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!nodeData) {
      errors.push('Node data is required');
    }

    if (!nodeData.id) {
      errors.push('Node ID is required');
    }

    if (!nodeData.label) {
      errors.push('Node label is required');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Default canExecute - can be overridden
   */
  canExecute(context: ExecutionContext): boolean {
    return true;
  }
}
