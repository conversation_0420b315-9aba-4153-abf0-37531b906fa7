export interface JobMatch {
  id: string;
  title: string;
  company: string;
  location: string;
  match: number;
  department: string;
  salary: string;
  status: string;
  postedDate: string;
  requirements: string[];
  benefits: string[];
  description?: string; // Added optional description property
}

export interface RelationshipFactor {
  name: string;
  score: number;
}

export interface RelationshipAnalysis {
  score: number;
  factors: RelationshipFactor[];
  trend: "increasing" | "stable" | "decreasing";
}

export interface ScreeningQuestion {
  id: string;
  question: string;
  response?: string;
  category: "technical" | "behavioral" | "cultural" | "experience";
}

export interface Requirements {
  workAuthorization: "verified" | "pending" | "not_required";
  backgroundCheck: "verified" | "pending" | "not_required";
  drugScreening: "verified" | "pending" | "not_required";
  references: "verified" | "pending" | "not_required";
}

export interface Evaluation {
  category: string;
  score: number;
  notes: string;
}

export interface CandidateType {
  id: string;
  name: string;
  role: string;
  email: string;
  phone: string;
  location: string;
  avatar: string;
  recruiter: {
    id: string;
    name: string;
    avatar: string;
  };
  tags: string[];
  socialLinks: {
    github?: string;
    linkedin?: string;
    twitter?: string;
  };
  relationshipScore: number;
  relationshipAnalysis?: RelationshipAnalysis;
  aiSummary?: string;
  matchedJobs?: JobMatch[];
  experience: string;
  industry: string;
  remotePreference: string;
  visaStatus: string;
  skills: {
    name: string;
    level: string;
    years: number;
  }[];
  createdAt: string;
  updatedAt: string;
  screening?: {
    notes?: string;
    dayToDay: string;
    skills: string[];
    location: string;
    compensation: {
      current: number;
      expected: number;
    };
    education: string[];
    questions?: ScreeningQuestion[];
    status: "pending" | "in_progress" | "completed";
    lastUpdated: string;
    requirements: Requirements;
    evaluations: Evaluation[];
  };
  activity?: Array<{
    type: string;
    description: string;
    date: string;
  }>;
}
