/**
 * TypeScript Types for Node Specification
 * 
 * Auto-generated from the JSON-schema describing each node category
 * with their configuration requirements, side-effects, and security scopes.
 *
 * Version: 1.0.0
 */

export interface TriggerNode {
  type: string;
  schedule?: string;
  event: string;
  sideEffects: 'External calls to initiate workflows';
  securityScope: 'event:read';
}

export interface ActionNode {
  type: string;
  action: string;
  parameters?: Record<string, unknown>;
  sideEffects: 'Database writes, external API calls';
  securityScope: 'action:write';
}

export interface ConditionNode {
  type: string;
  condition: string;
  onTrue: string;
  onFalse: string;
  sideEffects: 'None';
  securityScope: 'condition:read';
}

export interface TransformationNode {
  type: string;
  transformation: string;
  sideEffects: 'Data modification in memory';
  securityScope: 'data:transform';
}

export interface OutputNode {
  type: string;
  destination: string;
  sideEffects: 'Send data to external systems';
  securityScope: 'output:send';
}
