import { describe, it, expect, vi, beforeEach } from 'vitest';
import { BaseExecutor } from '@/engine/BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '@/engine/types';
import { createMockExecutionContext, createMockNodeData } from '../../utils/mockServices';

// Create a test implementation of BaseExecutor
class TestExecutor extends BaseExecutor {
  id = 'test-executor';
  name = 'Test Executor';
  description = 'Test executor for unit tests';
  category = 'action' as const;

  executeInternalMock = vi.fn();

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    return this.executeInternalMock(nodeData, context);
  }
}

describe('BaseExecutor', () => {
  let executor: TestExecutor;
  let mockContext: ExecutionContext;
  let mockNodeData: any;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    
    executor = new TestExecutor();
    mockContext = createMockExecutionContext();
    mockNodeData = createMockNodeData('test', {});
    
    // Default success response
    executor.executeInternalMock.mockResolvedValue({
      success: true,
      data: { result: 'success' },
    });
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Basic Execution', () => {
    it('should execute successfully without config', async () => {
      const result = await executor.execute(mockNodeData, mockContext);

      expect(result.success).toBe(true);
      expect(result.data).toEqual({ result: 'success' });
      expect(result.duration).toBeDefined();
      expect(result.duration).toBeGreaterThanOrEqual(0);
      expect(result.retryCount).toBe(0);
    });

    it('should pass through nodeData and context to executeInternal', async () => {
      await executor.execute(mockNodeData, mockContext);

      expect(executor.executeInternalMock).toHaveBeenCalledWith(
        mockNodeData,
        mockContext
      );
    });

    it('should handle execution errors', async () => {
      const error = new Error('Execution failed');
      executor.executeInternalMock.mockRejectedValue(error);

      const result = await executor.execute(mockNodeData, mockContext);

      expect(result.success).toBe(false);
      expect(result.error).toBe(error);
      expect(result.duration).toBeDefined();
    });
  });

  describe('Timeout Behavior', () => {
    it('should timeout after specified duration', async () => {
      // Make executeInternal never resolve
      executor.executeInternalMock.mockImplementation(
        () => new Promise(() => {}) // Never resolves
      );

      const config = { timeout: 1000 }; // 1 second timeout
      const resultPromise = executor.execute(mockNodeData, mockContext, config);

      // Fast-forward time
      vi.advanceTimersByTime(1000);

      const result = await resultPromise;

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('timeout after 1000ms');
      expect(result.retryCount).toBe(0);
    });

    it('should use default timeout of 30 seconds', async () => {
      executor.executeInternalMock.mockImplementation(
        () => new Promise(() => {})
      );

      const resultPromise = executor.execute(mockNodeData, mockContext);

      // Fast-forward default timeout
      vi.advanceTimersByTime(30000);

      const result = await resultPromise;

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('timeout after 30000ms');
    });

    it('should complete before timeout', async () => {
      executor.executeInternalMock.mockImplementation(
        () => new Promise(resolve => 
          setTimeout(() => resolve({ success: true, data: 'quick' }), 500)
        )
      );

      const config = { timeout: 2000 };
      const resultPromise = executor.execute(mockNodeData, mockContext, config);

      // Fast-forward less than timeout
      vi.advanceTimersByTime(600);

      const result = await resultPromise;

      expect(result.success).toBe(true);
      expect(result.data).toBe('quick');
    });
  });

  describe('Retry Behavior', () => {
    it('should retry on failure', async () => {
      executor.executeInternalMock
        .mockRejectedValueOnce(new Error('First attempt failed'))
        .mockResolvedValueOnce({ success: true, data: 'success on retry' });

      const config = { retries: 1, retryDelay: 100 };
      const result = await executor.execute(mockNodeData, mockContext, config);

      expect(result.success).toBe(true);
      expect(result.data).toBe('success on retry');
      expect(result.retryCount).toBe(1);
      expect(executor.executeInternalMock).toHaveBeenCalledTimes(2);
    });

    it('should retry multiple times with exponential backoff', async () => {
      executor.executeInternalMock
        .mockRejectedValueOnce(new Error('Attempt 1 failed'))
        .mockRejectedValueOnce(new Error('Attempt 2 failed'))
        .mockResolvedValueOnce({ success: true, data: 'success on third try' });

      const config = { retries: 2, retryDelay: 100 };
      const startTime = Date.now();
      
      const resultPromise = executor.execute(mockNodeData, mockContext, config);
      
      // First retry after 100ms
      vi.advanceTimersByTime(100);
      // Second retry after 200ms (exponential backoff)
      vi.advanceTimersByTime(200);
      
      const result = await resultPromise;

      expect(result.success).toBe(true);
      expect(result.data).toBe('success on third try');
      expect(result.retryCount).toBe(2);
      expect(executor.executeInternalMock).toHaveBeenCalledTimes(3);
    });

    it('should fail after all retries are exhausted', async () => {
      const error = new Error('Persistent failure');
      executor.executeInternalMock.mockRejectedValue(error);

      const config = { retries: 3, retryDelay: 50 };
      const resultPromise = executor.execute(mockNodeData, mockContext, config);
      
      // Advance through all retry delays
      vi.advanceTimersByTime(50); // First retry
      vi.advanceTimersByTime(100); // Second retry
      vi.advanceTimersByTime(150); // Third retry
      
      const result = await resultPromise;

      expect(result.success).toBe(false);
      expect(result.error).toBe(error);
      expect(result.retryCount).toBe(3);
      expect(executor.executeInternalMock).toHaveBeenCalledTimes(4); // Initial + 3 retries
    });

    it('should not retry on timeout errors', async () => {
      executor.executeInternalMock.mockImplementation(
        () => new Promise(() => {})
      );

      const config = { timeout: 100, retries: 3, retryDelay: 50 };
      const resultPromise = executor.execute(mockNodeData, mockContext, config);
      
      // Trigger timeout
      vi.advanceTimersByTime(100);
      
      const result = await resultPromise;

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('timeout');
      expect(result.retryCount).toBe(0); // No retries for timeout
      expect(executor.executeInternalMock).toHaveBeenCalledTimes(1);
    });

    it('should handle immediate success without retries', async () => {
      const config = { retries: 3 };
      const result = await executor.execute(mockNodeData, mockContext, config);

      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(0);
      expect(executor.executeInternalMock).toHaveBeenCalledTimes(1);
    });
  });

  describe('Validation', () => {
    it('should validate node data successfully', () => {
      const validation = executor.validate(mockNodeData);

      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should fail validation for null node data', () => {
      const validation = executor.validate(null);

      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Node data is required');
    });

    it('should fail validation for missing node ID', () => {
      const invalidData = { label: 'Test Node' };
      const validation = executor.validate(invalidData);

      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Node ID is required');
    });

    it('should fail validation for missing node label', () => {
      const invalidData = { id: 'node-1' };
      const validation = executor.validate(invalidData);

      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Node label is required');
    });

    it('should collect multiple validation errors', () => {
      const invalidData = { id: null, label: null };
      const validation = executor.validate(invalidData);

      expect(validation.valid).toBe(false);
      expect(validation.errors).toHaveLength(2);
      expect(validation.errors).toContain('Node ID is required');
      expect(validation.errors).toContain('Node label is required');
    });
  });

  describe('canExecute', () => {
    it('should return true by default', () => {
      const canExecute = executor.canExecute(mockContext);
      expect(canExecute).toBe(true);
    });

    it('should allow override in subclass', () => {
      class RestrictedExecutor extends TestExecutor {
        canExecute(context: ExecutionContext): boolean {
          return !!context.candidateId;
        }
      }

      const restrictedExecutor = new RestrictedExecutor();
      
      expect(restrictedExecutor.canExecute(mockContext)).toBe(true);
      expect(restrictedExecutor.canExecute({ ...mockContext, candidateId: undefined })).toBe(false);
    });
  });

  describe('Edge Cases', () => {
    it('should handle very short retry delays', async () => {
      executor.executeInternalMock
        .mockRejectedValueOnce(new Error('Quick fail'))
        .mockResolvedValueOnce({ success: true });

      const config = { retries: 1, retryDelay: 1 };
      const resultPromise = executor.execute(mockNodeData, mockContext, config);
      
      vi.advanceTimersByTime(1);
      
      const result = await resultPromise;

      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(1);
    });

    it('should handle zero retries config', async () => {
      executor.executeInternalMock.mockRejectedValue(new Error('Fail'));

      const config = { retries: 0 };
      const result = await executor.execute(mockNodeData, mockContext, config);

      expect(result.success).toBe(false);
      expect(result.retryCount).toBe(0);
      expect(executor.executeInternalMock).toHaveBeenCalledTimes(1);
    });

    it('should track duration accurately', async () => {
      executor.executeInternalMock.mockImplementation(
        () => new Promise(resolve => 
          setTimeout(() => resolve({ success: true }), 250)
        )
      );

      const resultPromise = executor.execute(mockNodeData, mockContext);
      vi.advanceTimersByTime(250);
      
      const result = await resultPromise;

      expect(result.duration).toBeGreaterThanOrEqual(250);
      expect(result.duration).toBeLessThan(300);
    });
  });
});
