# Automated Test Suite

This directory contains comprehensive tests for the workflow engine, including unit tests, integration tests, and end-to-end tests.

## Test Structure

```
src/__tests__/
├── unit/                    # Unit tests for individual components
│   └── executors/          # Tests for workflow node executors
├── integration/            # Integration tests
│   └── workflow-execution.test.ts  # Tests workflow execution with mocked DB
├── utils/                  # Test utilities and helpers
│   └── mockServices.ts     # Mock service implementations
└── setup.ts               # Global test setup
```

## Running Tests

### Unit Tests

Run all unit tests:
```bash
npm test
```

Run tests in watch mode:
```bash
npm test -- --watch
```

Run tests with coverage:
```bash
npm run test:coverage
```

View test UI:
```bash
npm run test:ui
```

### E2E Tests

Start the development server first:
```bash
npm run dev
```

Then run Cypress tests:
```bash
# Run in headless mode
npm run test:e2e

# Open Cypress UI
npm run test:e2e:open
```

## Test Coverage

### Unit Tests

Each executor has comprehensive unit tests covering:
- Basic properties and metadata
- Successful execution paths
- Error handling and edge cases
- Timeout and retry behavior
- Configuration validation
- Context requirements

Example executors tested:
- `SendEmailExecutor` - Action executor that sends emails
- `SkillsMatchExecutor` - Condition executor that checks candidate skills
- (Similar pattern for all other executors)

### Integration Tests

The `WorkflowExecutionEngine` integration tests cover:
- Basic workflow execution
- Complex workflow patterns (conditional branching, parallel execution)
- Error handling and recovery
- Database side effects (execution records, status updates)
- Real-time event publishing
- Alert triggering

### E2E Tests

Cypress tests simulate user interactions for:
- Creating new workflows
- Adding and configuring nodes
- Connecting nodes with edges
- Executing workflows
- Viewing execution logs and history
- Using workflow templates
- Handling errors gracefully

## Mocking Strategy

### Services
All external services are mocked using Vitest's mocking capabilities:
- `CandidatesService` - Returns mock candidate data
- `MessagingService` - Simulates message sending
- `TimelineService` - Tracks timeline entries
- `WorkflowLogger` - Logs workflow events
- `WorkflowAlertService` - Handles alerts

### Supabase
Supabase client is mocked globally in `setup.ts` with common operations:
- Database queries (select, insert, update, delete)
- Authentication
- Real-time subscriptions
- Function invocations

### Test Data
Mock data is centralized in `mockServices.ts`:
- `mockCandidateData` - Sample candidate information
- `mockJobData` - Sample job posting
- `mockMessageTemplate` - Email templates
- `createMockWorkflow()` - Complete workflow configuration

## Writing New Tests

### Unit Test Template

```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { YourExecutor } from '@/engine/executors/your-executor';
import { createMockServices, createMockExecutionContext } from '../../utils/mockServices';

describe('YourExecutor', () => {
  let executor: YourExecutor;
  
  beforeEach(() => {
    vi.clearAllMocks();
    executor = new YourExecutor();
  });
  
  it('should execute successfully', async () => {
    const nodeData = createMockNodeData('your-type', { /* config */ });
    const context = createMockExecutionContext();
    
    const result = await executor.execute(nodeData, context);
    
    expect(result.success).toBe(true);
    // Add more assertions
  });
});
```

### E2E Test Template

```typescript
describe('Your Feature E2E', () => {
  beforeEach(() => {
    cy.interceptSupabase();
    cy.login();
    cy.visit('/your-page');
  });
  
  it('should perform user action', () => {
    cy.get('[data-testid="element"]').click();
    // Add interactions and assertions
  });
});
```

## Best Practices

1. **Isolation**: Each test should be independent and not rely on others
2. **Mocking**: Mock all external dependencies to ensure fast, reliable tests
3. **Data-testid**: Use data-testid attributes for E2E test selectors
4. **Descriptive Names**: Use clear, descriptive test names that explain what is being tested
5. **Arrange-Act-Assert**: Follow the AAA pattern in unit tests
6. **Cleanup**: Always clean up after tests (clear mocks, logout, etc.)

## Debugging Tests

### Unit/Integration Tests
- Use `console.log` for quick debugging
- Run specific tests: `npm test -- --testNamePattern="should send email"`
- Use VS Code's debugger with the Vitest extension

### E2E Tests
- Use `cy.pause()` to pause execution
- Take screenshots: `cy.screenshot('debug-state')`
- Check Cypress command log in the UI
- Use `cy.debug()` to pause and inspect state

## CI/CD Integration

The test suite is designed to run in CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
test:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-node@v3
    - run: npm ci
    - run: npm run test:coverage
    - run: npm run build
    - run: npm run test:e2e
```

## Troubleshooting

### Common Issues

1. **Tests timing out**: Increase timeout in test config or specific test
2. **Flaky E2E tests**: Add explicit waits for elements or API calls
3. **Mock not working**: Ensure mocks are set up before imports
4. **Coverage gaps**: Check coverage report and add missing test cases

### Getting Help

- Check test output for specific error messages
- Review mock implementations in `mockServices.ts`
- Ensure all dependencies are installed: `npm install`
- Clear cache if needed: `rm -rf node_modules/.cache`
