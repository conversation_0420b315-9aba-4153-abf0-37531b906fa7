import { jsPDF } from 'jspdf';
import * as XLSX from 'xlsx';

/**
 * Represents the structure of metrics data
 */
export interface MetricsData {
  title?: string;
  headers?: string[];
  rows?: Array<Array<string | number | boolean>>;
  metadata?: Record<string, any>;
  [key: string]: any;
}

/**
 * Options for PDF generation
 */
export interface PDFOptions {
  orientation?: 'portrait' | 'landscape';
  unit?: 'mm' | 'pt' | 'cm' | 'in';
  format?: 'a4' | 'a3' | 'letter' | 'legal';
  title?: string;
  fontSize?: number;
  marginLeft?: number;
  marginTop?: number;
  includeTimestamp?: boolean;
}

/**
 * Options for Excel generation
 */
export interface ExcelOptions {
  sheetName?: string;
  includeHeaders?: boolean;
  autoWidth?: boolean;
  includeTimestamp?: boolean;
  numberFormat?: string;
  dateFormat?: string;
}

/**
 * Options for CSV generation
 */
export interface CSVOptions {
  delimiter?: string;
  includeHeaders?: boolean;
  quoteStrings?: boolean;
  lineEnding?: '\n' | '\r\n';
}

/**
 * Generates a PDF report from metrics data
 * @param data - The metrics data to include in the PDF
 * @param options - Configuration options for PDF generation
 * @returns A Blob containing the PDF data
 */
export function generatePDF(data: MetricsData, options: PDFOptions = {}): Blob {
  const {
    orientation = 'portrait',
    unit = 'mm',
    format = 'a4',
    title = data.title || 'Metrics Report',
    fontSize = 12,
    marginLeft = 20,
    marginTop = 20,
    includeTimestamp = true
  } = options;

  // Create a new PDF document
  const doc = new jsPDF({
    orientation,
    unit,
    format
  });

  let yPosition = marginTop;

  // Add title
  doc.setFontSize(fontSize + 4);
  doc.text(title, marginLeft, yPosition);
  yPosition += 10;

  // Add timestamp if requested
  if (includeTimestamp) {
    doc.setFontSize(fontSize - 2);
    doc.text(`Generated: ${new Date().toLocaleString()}`, marginLeft, yPosition);
    yPosition += 10;
  }

  // Reset font size for content
  doc.setFontSize(fontSize);

  // Add metadata if present
  if (data.metadata) {
    yPosition += 5;
    doc.text('Metadata:', marginLeft, yPosition);
    yPosition += 5;
    
    Object.entries(data.metadata).forEach(([key, value]) => {
      doc.text(`${key}: ${String(value)}`, marginLeft + 5, yPosition);
      yPosition += 5;
    });
    yPosition += 5;
  }

  // Add table data if present
  if (data.headers && data.rows) {
    // Simple table rendering
    const cellWidth = 40;
    const cellHeight = 7;
    
    // Draw headers
    doc.setFontSize(fontSize - 1);
    doc.setFont(undefined, 'bold');
    data.headers.forEach((header, index) => {
      doc.text(String(header), marginLeft + (index * cellWidth), yPosition);
    });
    doc.setFont(undefined, 'normal');
    yPosition += cellHeight;

    // Draw rows
    data.rows.forEach((row) => {
      // Check if we need a new page
      if (yPosition > 250) {
        doc.addPage();
        yPosition = marginTop;
      }

      row.forEach((cell, index) => {
        doc.text(String(cell), marginLeft + (index * cellWidth), yPosition);
      });
      yPosition += cellHeight;
    });
  }

  // Convert to Blob
  const pdfOutput = doc.output('blob');
  return new Blob([pdfOutput], { type: 'application/pdf' });
}

/**
 * Generates an Excel report from metrics data
 * @param data - The metrics data to include in the Excel file
 * @param options - Configuration options for Excel generation
 * @returns A Blob containing the Excel data
 */
export function generateExcel(data: MetricsData, options: ExcelOptions = {}): Blob {
  const {
    sheetName = 'Metrics',
    includeHeaders = true,
    autoWidth = true,
    includeTimestamp = true,
    numberFormat = '0.00',
    dateFormat = 'yyyy-mm-dd'
  } = options;

  // Create a new workbook
  const wb = XLSX.utils.book_new();

  // Prepare the data for the worksheet
  const wsData: any[][] = [];

  // Add title and timestamp if requested
  if (data.title) {
    wsData.push([data.title]);
    wsData.push([]); // Empty row
  }

  if (includeTimestamp) {
    wsData.push([`Generated: ${new Date().toLocaleString()}`]);
    wsData.push([]); // Empty row
  }

  // Add metadata if present
  if (data.metadata) {
    wsData.push(['Metadata']);
    Object.entries(data.metadata).forEach(([key, value]) => {
      wsData.push([key, value]);
    });
    wsData.push([]); // Empty row
  }

  // Add headers if requested and available
  if (includeHeaders && data.headers) {
    wsData.push(data.headers);
  }

  // Add data rows
  if (data.rows) {
    data.rows.forEach(row => {
      wsData.push(row);
    });
  }

  // Create worksheet from data
  const ws = XLSX.utils.aoa_to_sheet(wsData);

  // Apply auto-width if requested
  if (autoWidth && data.headers) {
    const colWidths = data.headers.map((header, index) => {
      const headerLength = String(header).length;
      const maxDataLength = Math.max(
        ...data.rows!.map(row => String(row[index] || '').length)
      );
      return { wch: Math.max(headerLength, maxDataLength) + 2 };
    });
    ws['!cols'] = colWidths;
  }

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(wb, ws, sheetName);

  // Generate the Excel file as an array buffer
  const excelBuffer = XLSX.write(wb, { 
    bookType: 'xlsx', 
    type: 'array',
    cellDates: true,
    dateNF: dateFormat,
    bookSST: true
  });

  // Convert to Blob
  return new Blob([excelBuffer], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  });
}

/**
 * Generates a CSV report from metrics data
 * @param data - The metrics data to include in the CSV
 * @param options - Configuration options for CSV generation
 * @returns A Blob containing the CSV data
 */
export function generateCSV(data: MetricsData, options: CSVOptions = {}): Blob {
  const {
    delimiter = ',',
    includeHeaders = true,
    quoteStrings = true,
    lineEnding = '\n'
  } = options;

  const csvRows: string[] = [];

  // Helper function to escape and quote values
  const formatValue = (value: any): string => {
    if (value === null || value === undefined) {
      return '';
    }

    const stringValue = String(value);

    // Check if the value needs to be quoted
    if (quoteStrings && (
      typeof value === 'string' || 
      stringValue.includes(delimiter) || 
      stringValue.includes('"') || 
      stringValue.includes('\n') || 
      stringValue.includes('\r')
    )) {
      // Escape double quotes by doubling them
      return `"${stringValue.replace(/"/g, '""')}"`;
    }

    return stringValue;
  };

  // Add title as a comment if present
  if (data.title) {
    csvRows.push(`# ${data.title}`);
    csvRows.push(`# Generated: ${new Date().toISOString()}`);
    csvRows.push('');
  }

  // Add metadata as comments if present
  if (data.metadata) {
    csvRows.push('# Metadata:');
    Object.entries(data.metadata).forEach(([key, value]) => {
      csvRows.push(`# ${key}: ${value}`);
    });
    csvRows.push('');
  }

  // Add headers if requested and available
  if (includeHeaders && data.headers) {
    const headerRow = data.headers.map(formatValue).join(delimiter);
    csvRows.push(headerRow);
  }

  // Add data rows
  if (data.rows) {
    data.rows.forEach(row => {
      const csvRow = row.map(formatValue).join(delimiter);
      csvRows.push(csvRow);
    });
  }

  // Join all rows with the specified line ending
  const csvContent = csvRows.join(lineEnding);

  // Convert to Blob with UTF-8 BOM for better Excel compatibility
  const BOM = '\uFEFF';
  return new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8' });
}

/**
 * Example usage:
 * 
 * const metricsData: MetricsData = {
 *   title: 'Monthly Sales Report',
 *   headers: ['Month', 'Revenue', 'Orders', 'Average Order Value'],
 *   rows: [
 *     ['January', 125000, 450, 277.78],
 *     ['February', 132000, 480, 275.00],
 *     ['March', 141000, 510, 276.47]
 *   ],
 *   metadata: {
 *     department: 'Sales',
 *     year: 2024,
 *     currency: 'USD'
 *   }
 * };
 * 
 * // Generate PDF
 * const pdfBlob = generatePDF(metricsData, {
 *   orientation: 'landscape',
 *   includeTimestamp: true
 * });
 * 
 * // Generate Excel
 * const excelBlob = generateExcel(metricsData, {
 *   sheetName: 'Sales Data',
 *   autoWidth: true
 * });
 * 
 * // Generate CSV
 * const csvBlob = generateCSV(metricsData, {
 *   delimiter: ',',
 *   includeHeaders: true
 * });
 * 
 * // Upload the blob (example)
 * // await uploadFile(pdfBlob, 'report.pdf');
 */
