import { supabase } from "@/integrations/supabase/client";
import { Job } from "@/hooks/useJobs";
import { calculateRelevanceScore } from "./searchUtils";

interface SearchFilters {
  location?: {
    address: string;
    radius: number;
  };
  remoteOnly?: boolean;
}

export const searchJobs = async (
  query: string,
  filters: SearchFilters = {}
): Promise<Job[]> => {
  try {
    let supabaseQuery = supabase
      .from('jobs')
      .select('*')
      .eq('is_active', true);

    // Apply full-text search if query is provided
    if (query.trim()) {
      // Clean and prepare query for full-text search
      const cleanQuery = query.trim().replace(/[^\w\s]/g, '').split(/\s+/).join(' & ');
      
      // Use full-text search
      supabaseQuery = supabaseQuery.filter('search_vector', 'fts', cleanQuery);
    }

    // Apply remote preference filter
    if (filters.remoteOnly) {
      supabaseQuery = supabaseQuery.or('location.ilike.%Remote%,job_type.ilike.%Remote%');
    }

    const { data, error } = await supabaseQuery
      .limit(50);

    if (error) {
      console.error('Job search error:', error);
      throw error;
    }

    // Sort results by relevance to the search query
    const results = data || [];
    if (query.trim()) {
      results.sort((a, b) => {
        const scoreA = calculateRelevanceScore(
          `${a.title} ${a.department} ${a.description}`, 
          query
        );
        const scoreB = calculateRelevanceScore(
          `${b.title} ${b.department} ${b.description}`, 
          query
        );
        return scoreB - scoreA;
      });
    } else {
      // If no query, sort by created_at
      results.sort((a, b) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    }
    
    return results;
  } catch (error) {
    console.error('Error searching jobs:', error);
    return [];
  }
};