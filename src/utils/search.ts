
import { supabase } from "@/integrations/supabase/client";
import { searchCandidates } from "./searchCandidates";
import { searchJobs } from "./searchJobs";
import { calculateRelevanceScore } from "./searchUtils";

export interface SearchFilters {
  department?: string;
  location?: {
    address: string;
    radius: number;
  };
  experience?: string;
  remote?: boolean;
  urgent?: boolean;
  remoteOnly?: boolean;
  visaSponsor?: boolean;
  skills?: {
    name: string;
    required: boolean;
    proficiency?: 'beginner' | 'intermediate' | 'expert';
  }[];
}

export interface SearchResult {
  id: string;
  title: string;
  type: 'candidate' | 'job';
  description: string;
  location?: string;
  match?: number;
  avatar?: string;
  tags?: string[];
}

export const highlightSearchResults = (text: string, query: string): string => {
  if (!query || !text) return text;
  
  // Handle multiple words in query
  const words = query.trim().split(/\s+/);
  let highlightedText = text;
  
  words.forEach(word => {
    const regex = new RegExp(`(${word})`, 'gi');
    highlightedText = highlightedText.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800">$1</mark>');
  });
  
  return highlightedText;
};

export const searchAll = async (query: string, filters: SearchFilters = {}) => {
  const candidates = await searchCandidates(query, filters);
  const jobs = await searchJobs(query, filters);
  
  return {
    candidates,
    jobs,
    total: candidates.length + jobs.length
  };
};

// Re-export individual search functions
export { searchCandidates, searchJobs };
