import { SearchFilters } from "@/types/search";

/**
 * Calculate a relevance score for an item based on how well it matches the query
 * @param text The text to search in
 * @param query The search query
 * @returns A relevance score between 0 and 100
 */
export const calculateRelevanceScore = (text: string, query: string): number => {
  if (!query.trim() || !text) return 0;
  
  // Convert to lowercase for case-insensitive matching
  const lowerText = text.toLowerCase();
  const queryTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 0);
  
  if (queryTerms.length === 0) return 0;
  
  let score = 0;
  const maxScore = 100;
  
  // Calculate matches for each term
  queryTerms.forEach(term => {
    // Exact match of the term
    if (lowerText.includes(term)) {
      // Longer terms get higher scores
      score += 10 * (1 + term.length / 10);
      
      // Bonus for word boundary matches (more precise matches)
      const wordBoundaryRegex = new RegExp(`\\b${term}\\b`, 'i');
      if (wordBoundaryRegex.test(text)) {
        score += 15;
      }
      
      // Bonus for multiple occurrences
      const occurrences = (lowerText.match(new RegExp(term, 'g')) || []).length;
      if (occurrences > 1) {
        score += Math.min(occurrences * 5, 20); // Cap at 20 points
      }
    }
    // Partial match (at least 3 characters)
    else if (term.length >= 3 && lowerText.includes(term.substring(0, term.length - 1))) {
      score += 5;
    }
  });
  
  // Normalize score to be between 0 and 100
  return Math.min(Math.round(score), maxScore);
};

export const parseBooleanQuery = (query: string) => {
  // Basic boolean parser
  const operators = /(AND|OR|NOT|\(|\))/g;
  const terms = query.split(operators).filter(term => term.trim());
  return terms;
};

export const calculateSearchScore = (item: any, query: string, filters: SearchFilters) => {
  let score = 0;
  
  // Basic text matching
  if (item.name?.toLowerCase().includes(query.toLowerCase())) {
    score += 10;
  }
  
  // Skills matching
  if (filters.skills && item.skills) {
    filters.skills.forEach(skill => {
      if (item.skills.includes(skill.name)) {
        score += skill.required ? 5 : 3;
      }
    });
  }
  
  // Location matching
  if (filters.location && item.location) {
    // Simple distance check - in real app, use proper geo-distance calculation
    if (item.location.includes(filters.location.address)) {
      score += 5;
    }
  }
  
  return score;
};