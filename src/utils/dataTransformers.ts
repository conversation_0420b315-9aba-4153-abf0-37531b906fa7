/**
 * Data Transformation Utilities
 * Convert between database (snake_case) and frontend (camelCase) formats
 */

/**
 * Optimized transformation function with improved pattern matching
 */
export function toCamelCase(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}

/**
 * Optimized snake_case conversion with cached regex
 */
const SNAKE_CASE_REGEX = /([a-z])([A-Z])/g;
export function toSnakeCase(str: string): string {
  return str.replace(SNAKE_CASE_REGEX, '$1_$2').toLowerCase();
}

/**
 * Optimized object key transformation with caching
 */
const transformationCache = new Map<string, string>();

function getCachedTransformation(key: string, transformer: (str: string) => string): string {
  if (transformationCache.has(key)) {
    return transformationCache.get(key)!;
  }
  
  const transformed = transformer(key);
  transformationCache.set(key, transformed);
  
  // Keep cache size reasonable
  if (transformationCache.size > 200) {
    const firstKey = transformationCache.keys().next().value;
    transformationCache.delete(firstKey);
  }
  
  return transformed;
}

/**
 * High-performance object key transformation using cached patterns
 */
export function transformKeysToCamelCase<T = Record<string, unknown>>(obj: unknown): T {
  if (!obj || typeof obj !== 'object' || obj instanceof Date) {
    return obj as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => transformKeysToCamelCase(item)) as T;
  }

  const transformed = {} as Record<string, unknown>;
  const entries = Object.entries(obj as Record<string, unknown>);
  
  // Use for loop for better performance than Object.entries + reduce
  for (let i = 0; i < entries.length; i++) {
    const [key, value] = entries[i];
    const camelKey = getCachedTransformation(key, toCamelCase);
    
    if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
      transformed[camelKey] = transformKeysToCamelCase(value);
    } else if (Array.isArray(value)) {
      transformed[camelKey] = value.map(item => 
        typeof item === 'object' && item !== null ? transformKeysToCamelCase(item) : item
      );
    } else {
      transformed[camelKey] = value;
    }
  }

  return transformed as T;
}

/**
 * High-performance snake_case transformation
 */
export function transformKeysToSnakeCase<T = Record<string, unknown>>(obj: unknown): T {
  if (!obj || typeof obj !== 'object' || obj instanceof Date) {
    return obj as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => transformKeysToSnakeCase(item)) as T;
  }

  const transformed = {} as Record<string, unknown>;
  const entries = Object.entries(obj as Record<string, unknown>);
  
  for (let i = 0; i < entries.length; i++) {
    const [key, value] = entries[i];
    const snakeKey = getCachedTransformation(key, toSnakeCase);
    
    if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
      transformed[snakeKey] = transformKeysToSnakeCase(value);
    } else if (Array.isArray(value)) {
      transformed[snakeKey] = value.map(item => 
        typeof item === 'object' && item !== null ? transformKeysToSnakeCase(item) : item
      );
    } else {
      transformed[snakeKey] = value;
    }
  }

  return transformed as T;
}

/**
 * Transform database candidate data to frontend format
 * Converts flat recruiter fields to nested object structure
 */
export function transformCandidateFromDatabase(dbCandidate: Record<string, unknown>): Record<string, unknown> {
  if (!dbCandidate) return {};

  // First convert all keys to camelCase
  const camelCaseCandidate = transformKeysToCamelCase(dbCandidate);

  // Then transform flat recruiter fields to nested object
  const transformed = {
    ...camelCaseCandidate,
    recruiter: {
      id: (camelCaseCandidate as Record<string, unknown>).recruiterId || '',
      name: (camelCaseCandidate as Record<string, unknown>).recruiterName || '',
      avatar: (camelCaseCandidate as Record<string, unknown>).recruiterAvatar || '/placeholder.svg'
    },
    socialLinks: {
      github: (camelCaseCandidate as Record<string, unknown>).githubUrl,
      linkedin: (camelCaseCandidate as Record<string, unknown>).linkedinUrl,
      twitter: (camelCaseCandidate as Record<string, unknown>).twitterUrl
    }
  };

  // Remove the flat fields since we've nested them
  delete (transformed as Record<string, unknown>).recruiterId;
  delete (transformed as Record<string, unknown>).recruiterName;
  delete (transformed as Record<string, unknown>).recruiterAvatar;
  delete (transformed as Record<string, unknown>).githubUrl;
  delete (transformed as Record<string, unknown>).linkedinUrl;
  delete (transformed as Record<string, unknown>).twitterUrl;

  return transformed;
}

/**
 * Transform frontend candidate data to database format
 * Converts nested recruiter object to flat fields
 */
export function transformCandidateToDatabase(frontendCandidate: Record<string, unknown>): Record<string, unknown> {
  if (!frontendCandidate) return {};

  const candidateData = frontendCandidate as Record<string, unknown> & {
    recruiter?: Record<string, unknown>;
    socialLinks?: Record<string, unknown>;
  };

  const recruiterData = candidateData.recruiter;
  const socialLinksData = candidateData.socialLinks;

  // Create transformed object without nested objects
  const { recruiter, socialLinks, ...baseData } = candidateData;
  
  const transformed = {
    ...baseData,
    // Flatten nested recruiter object
    recruiter_id: recruiterData?.id,
    recruiter_name: recruiterData?.name,
    recruiter_avatar: recruiterData?.avatar,
    // Flatten social links
    github_url: socialLinksData?.github,
    linkedin_url: socialLinksData?.linkedin,
    twitter_url: socialLinksData?.twitter
  };

  // Convert all keys to snake_case
  return transformKeysToSnakeCase(transformed);
}

/**
 * Transform database job data to frontend format
 */
export function transformJobFromDatabase(dbJob: Record<string, unknown>): Record<string, unknown> {
  if (!dbJob) return {};

  const camelCaseJob = transformKeysToCamelCase(dbJob);
  
  return {
    ...camelCaseJob,
    isUrgent: (camelCaseJob as Record<string, unknown>).isUrgent || false,
    isActive: (camelCaseJob as Record<string, unknown>).isActive || false,
    applicantCount: (camelCaseJob as Record<string, unknown>).applicantCount || 0
  };
}

/**
 * Transform frontend job data to database format
 */
export function transformJobToDatabase(frontendJob: Record<string, unknown>): Record<string, unknown> {
  if (!frontendJob) return {};

  return transformKeysToSnakeCase(frontendJob);
}

/**
 * Transform database event data to frontend format
 */
export function transformEventFromDatabase(dbEvent: Record<string, unknown>): Record<string, unknown> {
  if (!dbEvent) return {};

  return transformKeysToCamelCase(dbEvent);
}

/**
 * Transform frontend event data to database format
 */
export function transformEventToDatabase(frontendEvent: Record<string, unknown>): Record<string, unknown> {
  if (!frontendEvent) return {};

  return transformKeysToSnakeCase(frontendEvent);
}

/**
 * Transform database message data to frontend format
 */
export function transformMessageFromDatabase(dbMessage: Record<string, unknown>): Record<string, unknown> {
  if (!dbMessage) return {};

  return transformKeysToCamelCase(dbMessage);
}

/**
 * Transform frontend message data to database format
 */
export function transformMessageToDatabase(frontendMessage: Record<string, unknown>): Record<string, unknown> {
  if (!frontendMessage) return {};

  return transformKeysToSnakeCase(frontendMessage);
}

/**
 * Optimized array transformations for database operations
 */
export function transformArrayFromDatabase<T>(
  dbArray: unknown[],
  transformer: (item: unknown) => T
): T[] {
  if (!Array.isArray(dbArray)) return [];
  if (dbArray.length === 0) return [];
  
  return batchTransform(dbArray, transformer);
}

export function transformArrayToDatabase<T>(
  frontendArray: unknown[],
  transformer: (item: unknown) => T
): T[] {
  if (!Array.isArray(frontendArray)) return [];
  if (frontendArray.length === 0) return [];
  
  return batchTransform(frontendArray, transformer);
}

/**
 * Optimized JSON parsing with error recovery and caching
 */
const jsonParseCache = new Map<string, unknown>();

export function safeJsonParse<T = unknown>(jsonString: unknown, fallback: T): T {
  if (typeof jsonString !== 'string') {
    return (jsonString as T) || fallback;
  }

  // Check cache first
  if (jsonParseCache.has(jsonString)) {
    return jsonParseCache.get(jsonString) as T;
  }

  try {
    const parsed = JSON.parse(jsonString) as T;
    
    // Cache the result for future use
    jsonParseCache.set(jsonString, parsed);
    
    // Keep cache size reasonable
    if (jsonParseCache.size > 100) {
      const firstKey = jsonParseCache.keys().next().value;
      jsonParseCache.delete(firstKey);
    }
    
    return parsed;
  } catch (error) {
    console.warn('Failed to parse JSON:', jsonString, error);
    return fallback;
  }
}

/**
 * Transform database analytics data to frontend format
 */
export function transformAnalyticsFromDatabase(dbAnalytics: Record<string, unknown>): Record<string, unknown> {
  if (!dbAnalytics) return {};

  const camelCase = transformKeysToCamelCase(dbAnalytics);
  
  // Ensure proper number types for analytics
  return {
    ...camelCase,
    metricValue: Number((camelCase as Record<string, unknown>).metricValue) || 0,
    changePercentage: Number((camelCase as Record<string, unknown>).changePercentage) || 0
  };
}

/**
 * Optimized batch transformation with error recovery
 */
export function batchTransform<TInput, TOutput>(
  items: TInput[],
  transformer: (item: TInput) => TOutput
): TOutput[] {
  if (!Array.isArray(items)) {
    console.warn('batchTransform: items is not an array', items);
    return [];
  }

  if (items.length === 0) return [];

  const results: TOutput[] = [];
  let errorCount = 0;
  const maxErrors = Math.ceil(items.length * 0.1); // Allow up to 10% errors

  // Use for loop for better performance
  for (let i = 0; i < items.length; i++) {
    try {
      const result = transformer(items[i]);
      results.push(result);
    } catch (error) {
      errorCount++;
      console.error('Error transforming item at index', i, ':', items[i], error);
      
      // Stop processing if too many errors
      if (errorCount > maxErrors) {
        console.error('Too many transformation errors, stopping batch transform');
        break;
      }
    }
  }

  return results;
}

/**
 * Optimized bulk operations for large datasets
 */
export function processBulkData<TInput, TOutput>(
  items: TInput[],
  transformer: (item: TInput) => TOutput,
  batchSize: number = 100
): TOutput[] {
  const results: TOutput[] = [];
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = batchTransform(batch, transformer);
    results.push(...batchResults);
    
    // Allow event loop to process other tasks between batches
    if (i + batchSize < items.length) {
      setTimeout(() => {}, 0);
    }
  }
  
  return results;
}

/**
 * Clear transformation caches for memory management
 */
export function clearTransformationCaches() {
  transformationCache.clear();
  jsonParseCache.clear();
}

/**
 * Type-safe field mapping for common transformations
 */
export const FIELD_MAPPINGS = {
  // Database to Frontend
  DB_TO_FRONTEND: {
    'created_at': 'createdAt',
    'updated_at': 'updatedAt',
    'user_id': 'userId',
    'is_active': 'isActive',
    'is_urgent': 'isUrgent',
    'is_starred': 'isStarred',
    'recruiter_id': 'recruiterId',
    'recruiter_name': 'recruiterName',
    'recruiter_avatar': 'recruiterAvatar',
    'github_url': 'githubUrl',
    'linkedin_url': 'linkedinUrl',
    'twitter_url': 'twitterUrl',
    'job_type': 'jobType',
    'salary_range': 'salaryRange',
    'applicant_count': 'applicantCount',
    'experience_required': 'experienceRequired',
    'remote_preference': 'remotePreference',
    'visa_status': 'visaStatus',
    'ai_summary': 'aiSummary',
    'relationship_score': 'relationshipScore'
  },
  
  // Frontend to Database (reverse mapping)
  FRONTEND_TO_DB: {
    'createdAt': 'created_at',
    'updatedAt': 'updated_at',
    'userId': 'user_id',
    'isActive': 'is_active',
    'isUrgent': 'is_urgent',
    'isStarred': 'is_starred',
    'recruiterId': 'recruiter_id',
    'recruiterName': 'recruiter_name',
    'recruiterAvatar': 'recruiter_avatar',
    'githubUrl': 'github_url',
    'linkedinUrl': 'linkedin_url',
    'twitterUrl': 'twitter_url',
    'jobType': 'job_type',
    'salaryRange': 'salary_range',
    'applicantCount': 'applicant_count',
    'experienceRequired': 'experience_required',
    'remotePreference': 'remote_preference',
    'visaStatus': 'visa_status',
    'aiSummary': 'ai_summary',
    'relationshipScore': 'relationship_score'
  }
} as const;