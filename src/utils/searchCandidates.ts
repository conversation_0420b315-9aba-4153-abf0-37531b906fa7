import { supabase } from "@/integrations/supabase/client";
import { CandidateType } from "@/types/candidate";
import { calculateRelevanceScore } from "./searchUtils";

interface SearchFilters {
  location?: {
    address: string;
    radius: number;
  };
  skills?: {
    name: string;
    required: boolean;
    proficiency?: 'beginner' | 'intermediate' | 'expert';
  }[];
  remoteOnly?: boolean;
  visaSponsor?: boolean;
}

export const searchCandidates = async (
  query: string,
  filters: SearchFilters = {}
): Promise<CandidateType[]> => {
  try {
    console.log('🔍 Searching candidates with query:', query);
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.log('❌ No authenticated user found');
      return [];
    }
    
    console.log('👤 Current user ID:', user.id);
    
    let supabaseQuery = supabase
      .from('candidates')
      .select('*')
      .eq('user_id', user.id);

    // Apply search if query is provided
    if (query.trim()) {
      const searchTerm = `%${query.trim()}%`;
      
      // Search across multiple columns using OR conditions
      supabaseQuery = supabaseQuery.or(`name.ilike.${searchTerm},role.ilike.${searchTerm},email.ilike.${searchTerm},location.ilike.${searchTerm},industry.ilike.${searchTerm},ai_summary.ilike.${searchTerm}`);
    }

    // Apply location filter
    if (filters.location?.address) {
      supabaseQuery = supabaseQuery.ilike('location', `%${filters.location.address}%`);
    }

    // Apply skills filter
    if (filters.skills && filters.skills.length > 0) {
      const skillNames = filters.skills.map(skill => skill.name);
      // Search in the skills JSONB array
      supabaseQuery = supabaseQuery.containedBy('skills', skillNames);
    }

    // Apply remote preference filter
    if (filters.remoteOnly) {
      supabaseQuery = supabaseQuery.in('remote_preference', ['Remote', 'Hybrid']);
    }

    // Apply visa sponsorship filter
    if (filters.visaSponsor) {
      supabaseQuery = supabaseQuery.not('visa_status', 'in', '("US Citizen","Green Card Holder")');
    }

    const { data, error } = await supabaseQuery
      .limit(50);

    if (error) {
      console.error('❌ Search error:', error);
      throw error;
    }
    
    console.log('📊 Raw search results:', data?.length || 0, 'candidates found');
    
    if (!data || data.length === 0) {
      console.log('⚠️ No candidates found in search');
      return [];
    }

    // Transform and sort the database data to match the CandidateType interface
    const results = (data || []).map(candidate => ({
      id: candidate.id,
      name: candidate.name,
      role: candidate.role,
      email: candidate.email,
      phone: candidate.phone || '',
      location: candidate.location || '',
      avatar: candidate.avatar || '/placeholder.svg',
      recruiter: {
        id: candidate.recruiter_id || '',
        name: candidate.recruiter_name || '',
        avatar: candidate.recruiter_avatar || '/placeholder.svg'
      },
      tags: candidate.tags || [],
      socialLinks: {
        github: candidate.github_url || '',
        linkedin: candidate.linkedin_url || '',
        twitter: candidate.twitter_url || ''
      },
      relationshipScore: candidate.relationship_score || 0,
      experience: candidate.experience || '',
      industry: candidate.industry || '',
      remotePreference: candidate.remote_preference || '',
      visaStatus: candidate.visa_status || '',
      skills: Array.isArray(candidate.skills) ? candidate.skills as { name: string; level: string; years: number; }[] : [],
      aiSummary: candidate.ai_summary || '',
      matchedJobs: [],
      createdAt: candidate.created_at,
      updatedAt: candidate.updated_at
    }));
    
    // Sort results by relevance to the search query
    if (query.trim()) {
      results.sort((a, b) => {
        const scoreA = calculateRelevanceScore(
          `${a.name} ${a.role} ${a.location} ${a.skills.map(s => typeof s === 'string' ? s : s.name).join(' ')}`, 
          query
        );
        const scoreB = calculateRelevanceScore(
          `${b.name} ${b.role} ${b.location} ${b.skills.map(s => typeof s === 'string' ? s : s.name).join(' ')}`, 
          query
        );
        return scoreB - scoreA;
      });
    } else {
      // If no query, sort by created_at
      results.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    }
    
    return results;
  } catch (error) {
    console.error('Error searching candidates:', error);
    return [];
  }
};