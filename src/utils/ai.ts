import { supabase } from "@/integrations/supabase/client";
import { generateInterviewQuestions as geminiGenerateInterviewQuestions, generateJobDescription as geminiGenerateJobDescription, screenCandidate, parseResumeText } from "./gemini";
import { toast } from "sonner";

// Interface for job match results
interface Question {
  id: string;
  question: string;
  category: 'technical' | 'behavioral' | 'cultural' | 'experience';
  difficulty: 'easy' | 'medium' | 'hard';
  rationale: string;
}

interface JobMatch {
  id: string;
  name: string;
  match: number;
  role?: string;
  location?: string;
  experience?: string;
  skills?: string[];
  avatar?: string;
  email?: string;
}

interface ParsedResume {
  name?: string;
  email?: string;
  phone?: string;
  skills: string[];
  experience: {
    title?: string;
    company?: string;
    duration?: string;
    description?: string;
  }[];
  education: {
    degree?: string;
    institution?: string;
    year?: string;
  }[];
  summary?: string;
}

/**
 * Match candidates to a job using AI-driven comparison
 * @param jobId The ID of the job to match candidates against
 * @returns Array of candidates with match scores
 */
export const matchCandidatesToJob = async (jobId: string): Promise<JobMatch[]> => {
  try {
    // Get the job details
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('*')
      .eq('id', jobId)
      .single();

    if (jobError) {
      console.error('Error fetching job for matching:', jobError);
      return [];
    }

    // Get candidates from the database
    const { data: candidates, error: candidatesError } = await supabase
      .from('candidates')
      .select('*');

    if (candidatesError) {
      console.error('Error fetching candidates for matching:', candidatesError);
      return [];
    }

    if (!candidates || candidates.length === 0) {
      return [];
    }

    // Create a job description for AI matching
    const jobDescription = `
Job Title: ${job.title}
Department: ${job.department}
Location: ${job.location}
Job Type: ${job.job_type}
Experience Required: ${job.experience_required || 'Not specified'}
Description: ${job.description}
Requirements: ${job.requirements ? job.requirements.join(', ') : 'Not specified'}
`;

    // Process candidates in batches to avoid overwhelming the AI service
    const batchSize = 5;
    const matches: JobMatch[] = [];
    
    for (let i = 0; i < candidates.length; i += batchSize) {
      const batch = candidates.slice(i, i + batchSize);
      const batchPromises = batch.map(async (candidate) => {
        try {
          // Use AI to screen the candidate against the job
          const screeningResult = await screenCandidate(candidate, jobDescription);
          
          return {
            id: candidate.id,
            name: candidate.name,
            match: screeningResult.score, // AI-generated match score
            role: candidate.role,
            location: candidate.location || 'Not specified',
            experience: candidate.experience || 'Not specified',
            skills: candidate.tags || [],
            avatar: candidate.avatar || '/placeholder.svg',
            email: candidate.email
          };
        } catch (error) {
          console.error(`Error screening candidate ${candidate.id}:`, error);
          // Fallback to a basic match if AI screening fails
          return {
            id: candidate.id,
            name: candidate.name,
            match: calculateBasicMatchScore(candidate, job), // Fallback scoring
            role: candidate.role,
            location: candidate.location || 'Not specified',
            experience: candidate.experience || 'Not specified',
            skills: candidate.tags || [],
            avatar: candidate.avatar || '/placeholder.svg',
            email: candidate.email
          };
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      matches.push(...batchResults);
      
      // Add a small delay between batches to avoid rate limiting
      if (i + batchSize < candidates.length) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // Sort by match score (highest first)
    return matches.sort((a, b) => b.match - a.match);
  } catch (error) {
    console.error('Error in matchCandidatesToJob:', error);
    return [];
  }
};

/**
 * Calculate a basic match score between a candidate and job
 * This is used as a fallback when AI screening fails
 */
function calculateBasicMatchScore(candidate: any, job: any): number {
  let score = 70; // Base score
  
  // Check for role match
  if (candidate.role && job.title && 
      candidate.role.toLowerCase().includes(job.title.toLowerCase())) {
    score += 10;
  }
  
  // Check for location match
  if (candidate.location && job.location && 
      candidate.location.toLowerCase().includes(job.location.toLowerCase())) {
    score += 5;
  }
  
  // Check for skills match
  if (job.requirements && candidate.tags) {
    const jobRequirements = job.requirements.map((req: string) => req.toLowerCase());
    const candidateSkills = candidate.tags.map((tag: string) => tag.toLowerCase());
    
    // Count matching skills
    const matchingSkills = jobRequirements.filter(req => 
      candidateSkills.some(skill => skill.includes(req) || req.includes(skill))
    ).length;
    
    // Add points based on matching skills percentage
    if (jobRequirements.length > 0) {
      const matchPercentage = (matchingSkills / jobRequirements.length) * 100;
      score += Math.min(15, matchPercentage / 7); // Max 15 points for skills
    }
  }
  
  // Ensure score is between 0-100
  return Math.min(100, Math.max(0, score));
}

/**
 * Executes a workflow with the given ID and context
 */
export const executeWorkflow = async (workflowId: string, context: Record<string, any> = {}): Promise<any> => {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Get the workflow configuration
    const { data: workflow, error: workflowError } = await supabase
      .from('workflow_configurations')
      .select('*')
      .eq('id', workflowId)
      .single();

    if (workflowError || !workflow) {
      throw new Error('Workflow not found');
    }

    // Create a workflow execution record
    const { data: execution, error: executionError } = await supabase
      .from('workflow_executions')
      .insert({
        workflow_id: workflowId,
        status: 'in_progress',
        started_at: new Date().toISOString(),
        execution_log: {
          context,
          trigger: 'manual',
          started: new Date().toISOString()
        },
        created_by: user.id
      })
      .select()
      .single();

    if (executionError) {
      throw new Error('Failed to create workflow execution record');
    }

    // In a real implementation, this would use the WorkflowExecutionEngine
    // For now, we'll just simulate the execution
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Update the execution record
    const { error: updateError } = await supabase
      .from('workflow_executions')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString(),
        execution_log: {
          context,
          trigger: 'manual',
          started: new Date().toISOString(),
          completed: new Date().toISOString(),
          success: true
        }
      })
      .eq('id', execution.id);

    if (updateError) {
      console.error('Error updating workflow execution:', updateError);
    }

    return {
      success: true,
      executionId: execution.id
    };
  } catch (error) {
    console.error('Error executing workflow:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Generate interview questions based on job title and description
 */
export const generateInterviewQuestions = async (
  jobTitle: string,
  jobDescription: string = ""
): Promise<Question[]> => {
  try {
    // Use Gemini to generate interview questions
    const questions = await geminiGenerateInterviewQuestions(jobTitle, jobDescription);
    
    // Ensure each question has a unique ID
    return questions.map(q => ({
      ...q,
      id: q.id || crypto.randomUUID()
    }));
  } catch (error) {
    console.error('Error generating interview questions:', error);
    throw new Error('Failed to generate interview questions');
  }
};

export const generateJobDescription = async (title: string, department: string): Promise<string> => {
  try {
    // Use Gemini to generate a job description
    return await geminiGenerateJobDescription(title, department);
  } catch (error) {
    console.error('Error generating job description:', error);
    throw new Error('Failed to generate job description');
  }
};

/**
 * Parse a resume file and extract structured information
 */
export async function parseResume(file: File): Promise<ParsedResume | null> {
  try {
    // Read the file content
    const fileContent = await file.text();
    
    // Use Gemini to parse the resume
    const parsedData = await parseResumeText(fileContent);
    
    return parsedData;
  } catch (error) {
    console.error('Error parsing resume:', error);
    toast.error('Failed to parse resume. Please try again.');
    return null;
  }
}