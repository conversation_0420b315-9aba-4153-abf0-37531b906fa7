/**
 * Optimized Data Transformations with Performance Monitoring
 * Enhanced transformations that combine performance tracking with data processing
 */

import { 
  transformKeysToCamelCase, 
  transformKeysToSnakeCase,
  safeJsonParse,
  batchTransform,
  clearTransformationCaches
} from '@/utils/dataTransformers';
import { 
  splitTrimFilter, 
  filterMap, 
  uniqueFilter, 
  groupBy,
  arrayIntersection,
  ArrayPatterns
} from '@/utils/arrayOptimizations';
import { performanceCaches, withPerformanceMonitoring } from './performanceCache';

/**
 * Enhanced transformation pipeline for high-performance data processing
 */
export class OptimizedTransformationPipeline<T = any> {
  private data: T[];
  private transformations: Array<(data: T[]) => T[]> = [];

  constructor(data: T[]) {
    this.data = data;
  }

  /**
   * Add a transformation to the pipeline
   */
  transform<U = T>(fn: (data: T[]) => U[]): OptimizedTransformationPipeline<U> {
    this.transformations.push(fn as any);
    return this as any;
  }

  /**
   * Filter data with performance monitoring
   */
  filter(predicate: (item: T) => boolean): OptimizedTransformationPipeline<T> {
    return this.transform(data => 
      withPerformanceMonitoring('Pipeline-Filter', () => 
        data.filter(predicate)
      )
    );
  }

  /**
   * Map data with performance monitoring
   */
  map<U>(mapper: (item: T) => U): OptimizedTransformationPipeline<U> {
    return this.transform(data => 
      withPerformanceMonitoring('Pipeline-Map', () => 
        data.map(mapper)
      )
    );
  }

  /**
   * Optimized filter + map combination
   */
  filterMap<U>(predicate: (item: T) => boolean, mapper: (item: T) => U): OptimizedTransformationPipeline<U> {
    return this.transform(data => 
      withPerformanceMonitoring('Pipeline-FilterMap', () => 
        filterMap(data, predicate, mapper)
      )
    );
  }

  /**
   * Group data by key with performance monitoring
   */
  groupBy<K extends string | number>(keySelector: (item: T) => K): Record<K, T[]> {
    const result = withPerformanceMonitoring('Pipeline-GroupBy', () => 
      groupBy(this.execute(), keySelector)
    );
    return result;
  }

  /**
   * Get unique items
   */
  unique(): OptimizedTransformationPipeline<T> {
    return this.transform(data => 
      withPerformanceMonitoring('Pipeline-Unique', () => 
        uniqueFilter(data)
      )
    );
  }

  /**
   * Execute the transformation pipeline
   */
  execute(): T[] {
    return withPerformanceMonitoring('Pipeline-Execute', () => {
      let result = this.data;
      
      for (const transformation of this.transformations) {
        result = transformation(result);
      }
      
      return result;
    });
  }

  /**
   * Execute with caching
   */
  executeWithCache(cacheKey: string): T[] {
    const cached = performanceCaches.analytics.get(cacheKey);
    if (cached) {
      return cached;
    }

    const result = this.execute();
    performanceCaches.analytics.set(cacheKey, result);
    return result;
  }
}

/**
 * Enhanced data transformation utilities with performance tracking
 */
export const OptimizedTransformations = {
  /**
   * Process candidate data with optimization and caching
   */
  processCandidates: (candidates: any[], options: { useCache?: boolean; cacheKey?: string } = {}) => {
    const pipeline = new OptimizedTransformationPipeline(candidates)
      .filter(candidate => candidate && typeof candidate === 'object')
      .map(candidate => transformKeysToCamelCase(candidate))
      .filter(candidate => {
        const typed = candidate as any;
        return Boolean(typed?.id && typed?.name);
      });

    if (options.useCache && options.cacheKey) {
      return pipeline.executeWithCache(options.cacheKey);
    }

    return pipeline.execute();
  },

  /**
   * Process job data with optimization
   */
  processJobs: (jobs: any[], options: { useCache?: boolean; cacheKey?: string } = {}) => {
    const pipeline = new OptimizedTransformationPipeline(jobs)
      .filter(job => job && typeof job === 'object')
      .map(job => transformKeysToCamelCase(job))
      .filter(job => {
        const typed = job as any;
        return Boolean(typed?.id && typed?.title);
      });

    if (options.useCache && options.cacheKey) {
      return pipeline.executeWithCache(options.cacheKey);
    }

    return pipeline.execute();
  },

  /**
   * Process analytics data with aggregation
   */
  processAnalytics: (analytics: any[], groupByField: string = 'type') => {
    return new OptimizedTransformationPipeline(analytics)
      .filter(item => item && typeof item === 'object')
      .map(item => transformKeysToCamelCase(item))
      .groupBy(item => {
        const typed = item as any;
        const value = typed?.[groupByField];
        return (typeof value === 'string' || typeof value === 'number') ? value : 'unknown';
      });
  },

  /**
   * Parse and process JSON fields safely
   */
  processJsonFields: <T = any>(data: any[], jsonFields: string[]): T[] => {
    return withPerformanceMonitoring('ProcessJsonFields', () => {
      return data.map(item => {
        const processed = { ...item };
        
        for (const field of jsonFields) {
          if (processed[field] && typeof processed[field] === 'string') {
            processed[field] = safeJsonParse(processed[field], {});
          }
        }
        
        return processed;
      });
    });
  },

  /**
   * Process search results with relevance scoring
   */
  processSearchResults: (results: any[], query: string) => {
    return withPerformanceMonitoring('ProcessSearchResults', () => {
      const queryTerms = splitTrimFilter(query.toLowerCase(), ' ');
      
      return results
        .map(result => {
          let relevanceScore = 0;
          const searchableText = `${result.title || ''} ${result.name || ''} ${result.description || ''}`.toLowerCase();
          
          // Calculate relevance based on query term matches
          for (const term of queryTerms) {
            if (searchableText.includes(term)) {
              relevanceScore += 1;
              
              // Boost score for exact title/name matches
              if ((result.title || '').toLowerCase().includes(term) || 
                  (result.name || '').toLowerCase().includes(term)) {
                relevanceScore += 2;
              }
            }
          }
          
          return { ...result, relevanceScore };
        })
        .filter(result => result.relevanceScore > 0)
        .sort((a, b) => b.relevanceScore - a.relevanceScore);
    });
  },

  /**
   * Process workflow execution data
   */
  processWorkflowExecutions: (executions: any[]) => {
    return new OptimizedTransformationPipeline(executions)
      .filter(execution => execution && execution.id)
      .map(execution => ({
        ...transformKeysToCamelCase(execution),
        executionLog: safeJsonParse(execution.execution_log, {}),
        duration: execution.completed_at && execution.started_at 
          ? new Date(execution.completed_at).getTime() - new Date(execution.started_at).getTime()
          : null
      }))
      .execute();
  },

  /**
   * Process notification data with grouping
   */
  processNotifications: (notifications: any[]) => {
    const processed = new OptimizedTransformationPipeline(notifications)
      .filter(notification => notification && notification.id)
      .map(notification => transformKeysToCamelCase(notification))
      .execute();

    // Group by read status and type with type safety
    const grouped = groupBy(processed, notification => {
      const typed = notification as any;
      return Boolean(typed?.read) ? 'read' : 'unread';
    });

    return {
      all: processed,
      read: grouped.read || [],
      unread: grouped.unread || [],
      byType: groupBy(processed, notification => {
        const typed = notification as any;
        const type = typed?.type;
        return (typeof type === 'string') ? type : 'general';
      })
    };
  }
};

/**
 * Batch processing utilities for large datasets
 */
export const BatchProcessor = {
  /**
   * Process large arrays in chunks to prevent UI blocking
   */
  async processInChunks<T, U>(
    data: T[], 
    processor: (item: T) => U, 
    chunkSize: number = 100,
    onProgress?: (processed: number, total: number) => void
  ): Promise<U[]> {
    const results: U[] = [];
    
    for (let i = 0; i < data.length; i += chunkSize) {
      const chunk = data.slice(i, i + chunkSize);
      const chunkResults = await Promise.all(chunk.map(processor));
      results.push(...chunkResults);
      
      // Report progress
      if (onProgress) {
        onProgress(Math.min(i + chunkSize, data.length), data.length);
      }
      
      // Yield control to prevent UI blocking
      await new Promise(resolve => setTimeout(resolve, 0));
    }
    
    return results;
  },

  /**
   * Transform database records to frontend format in batches
   */
  async transformFromDatabase<T>(
    records: any[], 
    transformer: (record: any) => T,
    batchSize: number = 50
  ): Promise<T[]> {
    return withPerformanceMonitoring('BatchTransformFromDatabase', async () => {
      return this.processInChunks(records, transformer, batchSize);
    });
  }
};

/**
 * Performance optimization utilities
 */
export const PerformanceOptimizer = {
  /**
   * Clear all transformation caches
   */
  clearAllCaches: () => {
    clearTransformationCaches();
    performanceCaches.analytics.clear();
    performanceCaches.searchResults.clear();
    performanceCaches.userData.clear();
    performanceCaches.systemMetrics.clear();
  },

  /**
   * Get cache performance metrics
   */
  getCacheMetrics: () => {
    return {
      analytics: performanceCaches.analytics.getMetrics(),
      searchResults: performanceCaches.searchResults.getMetrics(),
      userData: performanceCaches.userData.getMetrics(),
      systemMetrics: performanceCaches.systemMetrics.getMetrics(),
    };
  },

  /**
   * Warm caches for common operations
   */
  warmCaches: async (userId: string) => {
    // Warm user data cache
    performanceCaches.userData.set(`user-${userId}`, { userId, warmed: true });
    
    // Warm analytics cache with placeholder
    performanceCaches.analytics.set(`user-analytics-${userId}`, []);
    
    console.log('Performance caches warmed for user:', userId);
  }
};