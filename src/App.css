#root {
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card {
    padding: 1rem;
  }
  
  /* Prevent horizontal overflow on mobile */
  body {
    overflow-x: hidden;
  }
  
  /* Ensure all cards and components respect container width */
  .card, .grid, .flex {
    min-width: 0;
    max-width: 100%;
  }
  
  /* Fix table overflow on mobile */
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Responsive text sizing */
  h1 {
    font-size: 1.5rem;
  }
  
  h2 {
    font-size: 1.25rem;
  }
  
  h3 {
    font-size: 1.125rem;
  }
}</media>

/* Base responsive utilities */
.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 300px), 1fr));
}

.responsive-flex {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

/* Ensure proper spacing on mobile */
@media (max-width: 640px) {
  .responsive-grid,
  .responsive-flex {
    gap: 0.75rem;
  }
  
  .responsive-padding {
    padding: 0.75rem;
  }
}