/**
 * UserFeedbackService
 * Centralized service for all user feedback-related database operations
 */

import { supabase } from '@/integrations/supabase/client';
import { Tables, TablesInsert, TablesUpdate, Json } from '@/integrations/supabase/types';

export type UserFeedback = Tables<'user_feedback'>;
export type CreateUserFeedbackData = TablesInsert<'user_feedback'>;
export type UpdateUserFeedbackData = TablesUpdate<'user_feedback'>;

export interface FeedbackFilters {
  feedback_type?: 'bug' | 'feature_request' | 'improvement' | 'praise' | 'complaint';
  feature_name?: string;
  status?: 'pending' | 'reviewing' | 'in_progress' | 'resolved' | 'dismissed';
  rating_min?: number;
  rating_max?: number;
  date_from?: string;
  date_to?: string;
}

export interface FeedbackStats {
  total: number;
  by_type: Record<string, number>;
  by_status: Record<string, number>;
  average_rating: number;
  recent_count: number;
}

export class UserFeedbackService {
  /**
   * Get all feedback for a user
   */
  static async getUserFeedback(userId: string, filters?: FeedbackFilters): Promise<UserFeedback[]> {
    let query = supabase
      .from('user_feedback')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    // Apply filters
    if (filters?.feedback_type) {
      query = query.eq('feedback_type', filters.feedback_type);
    }
    
    if (filters?.feature_name) {
      query = query.ilike('feature_name', `%${filters.feature_name}%`);
    }
    
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    
    if (filters?.rating_min !== undefined) {
      query = query.gte('rating', filters.rating_min);
    }
    
    if (filters?.rating_max !== undefined) {
      query = query.lte('rating', filters.rating_max);
    }
    
    if (filters?.date_from) {
      query = query.gte('created_at', filters.date_from);
    }
    
    if (filters?.date_to) {
      query = query.lte('created_at', filters.date_to);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching user feedback:', error);
      throw new Error(`Failed to fetch user feedback: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get all feedback (admin view)
   */
  static async getAllFeedback(filters?: FeedbackFilters, limit: number = 100): Promise<UserFeedback[]> {
    let query = supabase
      .from('user_feedback')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    // Apply filters (same as getUserFeedback but without user_id filter)
    if (filters?.feedback_type) {
      query = query.eq('feedback_type', filters.feedback_type);
    }
    
    if (filters?.feature_name) {
      query = query.ilike('feature_name', `%${filters.feature_name}%`);
    }
    
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    
    if (filters?.rating_min !== undefined) {
      query = query.gte('rating', filters.rating_min);
    }
    
    if (filters?.rating_max !== undefined) {
      query = query.lte('rating', filters.rating_max);
    }
    
    if (filters?.date_from) {
      query = query.gte('created_at', filters.date_from);
    }
    
    if (filters?.date_to) {
      query = query.lte('created_at', filters.date_to);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching all feedback:', error);
      throw new Error(`Failed to fetch all feedback: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get a single feedback by ID
   */
  static async getFeedback(feedbackId: string): Promise<UserFeedback | null> {
    const { data, error } = await supabase
      .from('user_feedback')
      .select('*')
      .eq('id', feedbackId)
      .single();

    if (error) {
      console.error('Error fetching feedback:', error);
      throw new Error(`Failed to fetch feedback: ${error.message}`);
    }

    return data;
  }

  /**
   * Create new feedback
   */
  static async createFeedback(feedbackData: CreateUserFeedbackData): Promise<UserFeedback> {
    // Validate required fields
    if (!feedbackData.feature_name?.trim()) {
      throw new Error('Feature name is required');
    }

    if (!feedbackData.message?.trim()) {
      throw new Error('Feedback message is required');
    }

    if (!feedbackData.feedback_type) {
      throw new Error('Feedback type is required');
    }

    if (!feedbackData.user_id) {
      throw new Error('User ID is required');
    }

    const { data, error } = await supabase
      .from('user_feedback')
      .insert({
        ...feedbackData,
        status: feedbackData.status || 'pending',
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating feedback:', error);
      throw new Error(`Failed to create feedback: ${error.message}`);
    }

    return data;
  }

  /**
   * Update feedback (mainly for admin status updates)
   */
  static async updateFeedback(feedbackId: string, updateData: UpdateUserFeedbackData): Promise<UserFeedback> {
    const { data, error } = await supabase
      .from('user_feedback')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', feedbackId)
      .select()
      .single();

    if (error) {
      console.error('Error updating feedback:', error);
      throw new Error(`Failed to update feedback: ${error.message}`);
    }

    return data;
  }

  /**
   * Update feedback status
   */
  static async updateFeedbackStatus(
    feedbackId: string, 
    status: 'pending' | 'reviewing' | 'in_progress' | 'resolved' | 'dismissed'
  ): Promise<UserFeedback> {
    return this.updateFeedback(feedbackId, { 
      status,
      updated_at: new Date().toISOString()
    });
  }

  /**
   * Delete feedback
   */
  static async deleteFeedback(feedbackId: string): Promise<void> {
    const { error } = await supabase
      .from('user_feedback')
      .delete()
      .eq('id', feedbackId);

    if (error) {
      console.error('Error deleting feedback:', error);
      throw new Error(`Failed to delete feedback: ${error.message}`);
    }
  }

  /**
   * Get feedback statistics
   */
  static async getFeedbackStats(
    userId?: string,
    dateRange?: { start: string; end: string }
  ): Promise<FeedbackStats> {
    let query = supabase
      .from('user_feedback')
      .select('feedback_type, status, rating, created_at');

    if (userId) {
      query = query.eq('user_id', userId);
    }

    if (dateRange) {
      query = query.gte('created_at', dateRange.start).lte('created_at', dateRange.end);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching feedback stats:', error);
      throw new Error(`Failed to fetch feedback stats: ${error.message}`);
    }

    const feedback = data || [];
    
    // Calculate statistics
    const stats: FeedbackStats = {
      total: feedback.length,
      by_type: {},
      by_status: {},
      average_rating: 0,
      recent_count: 0
    };

    // Group by type and status
    feedback.forEach(item => {
      stats.by_type[item.feedback_type] = (stats.by_type[item.feedback_type] || 0) + 1;
      if (item.status) {
        stats.by_status[item.status] = (stats.by_status[item.status] || 0) + 1;
      }
    });

    // Calculate average rating
    const ratingsWithValues = feedback.filter(item => item.rating !== null);
    if (ratingsWithValues.length > 0) {
      stats.average_rating = ratingsWithValues.reduce((sum, item) => sum + (item.rating || 0), 0) / ratingsWithValues.length;
    }

    // Count recent feedback (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    stats.recent_count = feedback.filter(item => 
      new Date(item.created_at) >= sevenDaysAgo
    ).length;

    return stats;
  }

  /**
   * Get feedback for a specific feature
   */
  static async getFeedbackForFeature(featureName: string, limit: number = 50): Promise<UserFeedback[]> {
    const { data, error } = await supabase
      .from('user_feedback')
      .select('*')
      .eq('feature_name', featureName)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching feature feedback:', error);
      throw new Error(`Failed to fetch feature feedback: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Submit bug report
   */
  static async submitBugReport(
    userId: string,
    featureName: string,
    message: string,
    metadata?: Json
  ): Promise<UserFeedback> {
    return this.createFeedback({
      user_id: userId,
      feature_name: featureName,
      feedback_type: 'bug',
      message,
      metadata: metadata || {},
      status: 'pending'
    });
  }

  /**
   * Submit feature request
   */
  static async submitFeatureRequest(
    userId: string,
    featureName: string,
    message: string,
    priority?: number,
    metadata?: Json
  ): Promise<UserFeedback> {
    return this.createFeedback({
      user_id: userId,
      feature_name: featureName,
      feedback_type: 'feature_request',
      message,
      rating: priority,
      metadata: metadata || {},
      status: 'pending'
    });
  }

  /**
   * Submit general feedback with rating
   */
  static async submitGeneralFeedback(
    userId: string,
    featureName: string,
    message: string,
    rating: number,
    feedbackType: 'improvement' | 'praise' | 'complaint' = 'improvement'
  ): Promise<UserFeedback> {
    return this.createFeedback({
      user_id: userId,
      feature_name: featureName,
      feedback_type: feedbackType,
      message,
      rating,
      status: 'pending'
    });
  }

  /**
   * Get trending feedback topics
   */
  static async getTrendingTopics(daysBack: number = 30, limit: number = 10): Promise<Array<{
    feature_name: string;
    count: number;
    latest_feedback: string;
  }>> {
    const dateLimit = new Date();
    dateLimit.setDate(dateLimit.getDate() - daysBack);

    const { data, error } = await supabase
      .from('user_feedback')
      .select('feature_name, message, created_at')
      .gte('created_at', dateLimit.toISOString())
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching trending topics:', error);
      throw new Error(`Failed to fetch trending topics: ${error.message}`);
    }

    // Group by feature name and count
    const grouped = (data || []).reduce((acc, item) => {
      if (!acc[item.feature_name]) {
        acc[item.feature_name] = {
          feature_name: item.feature_name,
          count: 0,
          latest_feedback: item.message
        };
      }
      acc[item.feature_name].count++;
      return acc;
    }, {} as Record<string, { feature_name: string; count: number; latest_feedback: string }>);

    // Sort by count and return top items
    return Object.values(grouped)
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }
}