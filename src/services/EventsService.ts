/**
 * EventsService
 * Centralized service for all event-related database operations
 */

import { supabase } from '@/integrations/supabase/client';

export interface Event {
  id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  location?: string;
  meeting_link?: string;
  event_type: 'meeting' | 'interview' | 'call' | 'presentation' | 'other';
  category: 'general' | 'recruitment' | 'client' | 'internal';
  priority: 'low' | 'medium' | 'high';
  user_id: string;
  created_at: string;
  updated_at: string;
}

export interface CreateEventData {
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  location?: string;
  meeting_link?: string;
  event_type: 'meeting' | 'interview' | 'call' | 'presentation' | 'other';
  category: 'general' | 'recruitment' | 'client' | 'internal';
  priority: 'low' | 'medium' | 'high';
  user_id: string;
}

export interface UpdateEventData {
  title?: string;
  description?: string;
  start_time?: string;
  end_time?: string;
  location?: string;
  meeting_link?: string;
  event_type?: 'meeting' | 'interview' | 'call' | 'presentation' | 'other';
  category?: 'general' | 'recruitment' | 'client' | 'internal';
  priority?: 'low' | 'medium' | 'high';
}

export class EventsService {
  /**
   * Get all events for a user
   */
  static async getEvents(userId: string): Promise<Event[]> {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .eq('user_id', userId)
      .order('start_time', { ascending: true });

    if (error) {
      console.error('Error fetching events:', error);
      throw new Error(`Failed to fetch events: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get events within a date range
   */
  static async getEventsByDateRange(
    userId: string,
    startDate: string,
    endDate: string
  ): Promise<Event[]> {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .eq('user_id', userId)
      .gte('start_time', startDate)
      .lte('end_time', endDate)
      .order('start_time', { ascending: true });

    if (error) {
      console.error('Error fetching events by date range:', error);
      throw new Error(`Failed to fetch events: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get a single event by ID
   */
  static async getEvent(eventId: string): Promise<Event | null> {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .eq('id', eventId)
      .single();

    if (error) {
      console.error('Error fetching event:', error);
      throw new Error(`Failed to fetch event: ${error.message}`);
    }

    return data;
  }

  /**
   * Create a new event
   */
  static async createEvent(eventData: CreateEventData): Promise<Event> {
    // Validate required fields
    if (!eventData.title) {
      throw new Error('Event title is required');
    }
    
    if (!eventData.start_time || !eventData.end_time) {
      throw new Error('Event start and end times are required');
    }
    
    // Ensure end time is after start time
    if (new Date(eventData.end_time) <= new Date(eventData.start_time)) {
      throw new Error('End time must be after start time');
    }

    if (!eventData.user_id) {
      throw new Error('User ID is required');
    }

    const { data, error } = await supabase
      .from('events')
      .insert(eventData)
      .select()
      .single();

    if (error) {
      console.error('Error creating event:', error);
      throw new Error(`Failed to create event: ${error.message}`);
    }

    return data;
  }

  /**
   * Update an existing event
   */
  static async updateEvent(eventId: string, updateData: UpdateEventData): Promise<Event> {
    // Validate dates if both are provided
    if (updateData.start_time && updateData.end_time) {
      if (new Date(updateData.end_time) <= new Date(updateData.start_time)) {
        throw new Error('End time must be after start time');
      }
    }

    const { data, error } = await supabase
      .from('events')
      .update(updateData)
      .eq('id', eventId)
      .select()
      .single();

    if (error) {
      console.error('Error updating event:', error);
      throw new Error(`Failed to update event: ${error.message}`);
    }

    return data;
  }

  /**
   * Delete an event
   */
  static async deleteEvent(eventId: string): Promise<void> {
    const { error } = await supabase
      .from('events')
      .delete()
      .eq('id', eventId);

    if (error) {
      console.error('Error deleting event:', error);
      throw new Error(`Failed to delete event: ${error.message}`);
    }
  }

  /**
   * Create an interview event
   */
  static async createInterviewEvent(
    userId: string,
    candidateName: string,
    interviewType: string,
    startTime: Date,
    durationMinutes: number,
    location?: string,
    meetingLink?: string
  ): Promise<Event> {
    const endTime = new Date(startTime.getTime() + durationMinutes * 60000);

    return this.createEvent({
      title: `Interview: ${candidateName} - ${interviewType}`,
      description: `Interview with ${candidateName} for ${interviewType} assessment`,
      start_time: startTime.toISOString(),
      end_time: endTime.toISOString(),
      location: location || null,
      meeting_link: meetingLink || null,
      event_type: 'interview',
      priority: 'high',
      category: 'recruitment',
      user_id: userId
    });
  }

  /**
   * Get upcoming events
   */
  static async getUpcomingEvents(userId: string, limit: number = 10): Promise<Event[]> {
    const now = new Date().toISOString();
    
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .eq('user_id', userId)
      .gte('start_time', now)
      .order('start_time', { ascending: true })
      .limit(limit);

    if (error) {
      console.error('Error fetching upcoming events:', error);
      throw new Error(`Failed to fetch upcoming events: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get events by type
   */
  static async getEventsByType(
    userId: string,
    eventType: Event['event_type']
  ): Promise<Event[]> {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .eq('user_id', userId)
      .eq('event_type', eventType)
      .order('start_time', { ascending: true });

    if (error) {
      console.error('Error fetching events by type:', error);
      throw new Error(`Failed to fetch events: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get events by category
   */
  static async getEventsByCategory(
    userId: string,
    category: Event['category']
  ): Promise<Event[]> {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .eq('user_id', userId)
      .eq('category', category)
      .order('start_time', { ascending: true });

    if (error) {
      console.error('Error fetching events by category:', error);
      throw new Error(`Failed to fetch events: ${error.message}`);
    }

    return data || [];
  }
}
