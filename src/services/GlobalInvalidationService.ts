/**
 * Global Real-time Invalidation Service
 * 
 * This service listens to real-time events from various tables and broadcasts
 * invalidation signals to ensure cross-component data consistency.
 * 
 * When data changes in one component, this service automatically invalidates
 * related data in other components to keep everything in sync.
 */

import { supabase } from "@/integrations/supabase/client";
import { RealtimeChannel } from "@supabase/supabase-js";

interface RealtimePayload {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new?: Record<string, unknown>;
  old?: Record<string, unknown>;
  schema: string;
  table: string;
  errors?: unknown[];
}

interface InvalidationEvent {
  type: 'invalidate';
  scope: string[];
  reason: string;
  metadata?: Record<string, unknown>;
  timestamp: Date;
}

type InvalidationSubscriber = (event: InvalidationEvent) => void;

class GlobalInvalidationService {
  private static instance: GlobalInvalidationService;
  private subscribers: Map<string, InvalidationSubscriber[]> = new Map();
  private channels: Map<string, RealtimeChannel> = new Map();
  private isInitialized = false;

  private constructor() {}

  static getInstance(): GlobalInvalidationService {
    if (!GlobalInvalidationService.instance) {
      GlobalInvalidationService.instance = new GlobalInvalidationService();
    }
    return GlobalInvalidationService.instance;
  }

  /**
   * Initialize the service - sets up real-time listeners for all relevant tables
   */
  async initialize(userId: string) {
    if (this.isInitialized) return;

    // Initializing Global Invalidation Service

    // Set up listeners for each table that could affect other components
    await this.setupTableListener('candidates', userId, this.handleCandidateChange.bind(this));
    await this.setupTableListener('jobs', userId, this.handleJobChange.bind(this));
    await this.setupTableListener('events', userId, this.handleEventChange.bind(this));
    await this.setupTableListener('notifications', userId, this.handleNotificationChange.bind(this));
    await this.setupTableListener('messages', userId, this.handleMessageChange.bind(this));
    await this.setupTableListener('candidate_interviews', userId, this.handleInterviewChange.bind(this));
    await this.setupTableListener('workflow_executions', userId, this.handleWorkflowChange.bind(this));

    this.isInitialized = true;
    // Global Invalidation Service initialized
  }

  /**
   * Set up a real-time listener for a specific table
   */
  private async setupTableListener(
    tableName: string, 
    userId: string, 
    handler: (payload: RealtimePayload) => void
  ) {
    const channelName = `global_invalidation_${tableName}_${userId}`;
    
    const channel = supabase.channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: tableName,
          filter: `user_id=eq.${userId}`
        },
        handler
      )
      .subscribe((status) => {
        // Invalidation listener setup complete
      });

    this.channels.set(tableName, channel);
  }

  /**
   * Handle candidate data changes
   */
  private handleCandidateChange(payload: RealtimePayload) {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    // When candidates change, invalidate related components
    this.broadcastInvalidation({
      type: 'invalidate',
      scope: [
        'candidates',
        'search-results',
        'dashboard-stats',
        'analytics',
        'job-matches'
      ],
      reason: `Candidate ${eventType}`,
      metadata: { 
        candidateId: newRecord?.id || oldRecord?.id,
        eventType 
      },
      timestamp: new Date()
    });
  }

  /**
   * Handle job data changes
   */
  private handleJobChange(payload: RealtimePayload) {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    this.broadcastInvalidation({
      type: 'invalidate',
      scope: [
        'jobs',
        'search-results',
        'dashboard-stats',
        'analytics',
        'candidate-matches'
      ],
      reason: `Job ${eventType}`,
      metadata: { 
        jobId: newRecord?.id || oldRecord?.id,
        eventType 
      },
      timestamp: new Date()
    });
  }

  /**
   * Handle event/calendar data changes
   */
  private handleEventChange(payload: RealtimePayload) {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    this.broadcastInvalidation({
      type: 'invalidate',
      scope: [
        'calendar',
        'events',
        'dashboard-upcoming',
        'timeline'
      ],
      reason: `Event ${eventType}`,
      metadata: { 
        eventId: newRecord?.id || oldRecord?.id,
        eventType 
      },
      timestamp: new Date()
    });
  }

  /**
   * Handle notification data changes
   */
  private handleNotificationChange(payload: RealtimePayload) {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    this.broadcastInvalidation({
      type: 'invalidate',
      scope: [
        'notifications',
        'notification-center',
        'dashboard-notifications',
        'unread-count'
      ],
      reason: `Notification ${eventType}`,
      metadata: { 
        notificationId: newRecord?.id || oldRecord?.id,
        eventType 
      },
      timestamp: new Date()
    });
  }

  /**
   * Handle message data changes
   */
  private handleMessageChange(payload: RealtimePayload) {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    this.broadcastInvalidation({
      type: 'invalidate',
      scope: [
        'messages',
        'dashboard-messages',
        'candidate-communications'
      ],
      reason: `Message ${eventType}`,
      metadata: { 
        messageId: newRecord?.id || oldRecord?.id,
        eventType 
      },
      timestamp: new Date()
    });
  }

  /**
   * Handle interview data changes
   */
  private handleInterviewChange(payload: RealtimePayload) {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    this.broadcastInvalidation({
      type: 'invalidate',
      scope: [
        'interviews',
        'candidate-interviews',
        'calendar',
        'timeline'
      ],
      reason: `Interview ${eventType}`,
      metadata: { 
        interviewId: newRecord?.id || oldRecord?.id,
        candidateId: newRecord?.candidate_id || oldRecord?.candidate_id,
        eventType 
      },
      timestamp: new Date()
    });
  }

  /**
   * Handle workflow execution changes
   */
  private handleWorkflowChange(payload: RealtimePayload) {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    this.broadcastInvalidation({
      type: 'invalidate',
      scope: [
        'workflows',
        'workflow-history',
        'dashboard-workflows'
      ],
      reason: `Workflow ${eventType}`,
      metadata: { 
        executionId: newRecord?.id || oldRecord?.id,
        workflowId: newRecord?.workflow_id || oldRecord?.workflow_id,
        eventType 
      },
      timestamp: new Date()
    });
  }

  /**
   * Subscribe to invalidation events for specific scopes
   */
  subscribe(scopes: string[], callback: InvalidationSubscriber): () => void {
    scopes.forEach(scope => {
      if (!this.subscribers.has(scope)) {
        this.subscribers.set(scope, []);
      }
      this.subscribers.get(scope)!.push(callback);
    });

    // Return unsubscribe function
    return () => {
      scopes.forEach(scope => {
        const scopeSubscribers = this.subscribers.get(scope);
        if (scopeSubscribers) {
          const index = scopeSubscribers.indexOf(callback);
          if (index > -1) {
            scopeSubscribers.splice(index, 1);
          }
        }
      });
    };
  }

  /**
   * Broadcast invalidation event to all relevant subscribers
   */
  private broadcastInvalidation(event: InvalidationEvent) {
    // Broadcasting invalidation event

    event.scope.forEach(scope => {
      const scopeSubscribers = this.subscribers.get(scope);
      if (scopeSubscribers) {
        scopeSubscribers.forEach(callback => {
          try {
            callback(event);
          } catch (error) {
            // Log error but don't break the invalidation chain
            console.error(`Error in invalidation subscriber for scope ${scope}:`, error);
          }
        });
      }
    });
  }

  /**
   * Manually trigger invalidation for specific scopes
   */
  invalidate(scopes: string[], reason: string, metadata?: Record<string, any>) {
    this.broadcastInvalidation({
      type: 'invalidate',
      scope: scopes,
      reason,
      metadata,
      timestamp: new Date()
    });
  }

  /**
   * Cleanup - unsubscribe from all channels
   */
  cleanup() {
    // Cleaning up Global Invalidation Service
    
    this.channels.forEach((channel, tableName) => {
      channel.unsubscribe();
      // Unsubscribed from invalidation listener
    });
    
    this.channels.clear();
    this.subscribers.clear();
    this.isInitialized = false;
  }
}

export const globalInvalidationService = GlobalInvalidationService.getInstance();
export type { InvalidationEvent, InvalidationSubscriber }; 