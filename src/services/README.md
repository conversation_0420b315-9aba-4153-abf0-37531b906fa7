# Service Modules

This directory contains centralized service modules that wrap all Supabase database operations. These services provide a clean, consistent API for interacting with the database tables.

## Services

### MessagingService
Handles all operations related to the `messages` table:
- `getMessages(userId)` - Get all messages for a user
- `getMessage(messageId)` - Get a single message
- `createMessage(messageData)` - Create a new message
- `updateMessage(messageId, updateData)` - Update an existing message
- `deleteMessage(messageId)` - Delete a message
- `markAsRead(messageId)` - Mark message as read
- `markAsUnread(messageId)` - Mark message as unread
- `archiveMessage(messageId)` - Archive a message
- `toggleStar(messageId, isStarred)` - Toggle star status
- `getMessageTemplate(category)` - Get message template by category
- `createWorkflowMessage(userId, content, additionalData?)` - Create a workflow-generated message

### EventsService
Handles all operations related to the `events` table:
- `getEvents(userId)` - Get all events for a user
- `getEventsByDateRange(userId, startDate, endDate)` - Get events within a date range
- `getEvent(eventId)` - Get a single event
- `createEvent(eventData)` - Create a new event
- `updateEvent(eventId, updateData)` - Update an existing event
- `deleteEvent(eventId)` - Delete an event
- `createInterviewEvent(...)` - Create an interview event with specific parameters
- `getUpcomingEvents(userId, limit?)` - Get upcoming events
- `getEventsByType(userId, eventType)` - Get events by type
- `getEventsByCategory(userId, category)` - Get events by category

### CandidatesService
Handles all operations related to the `candidates` table:
- `getCandidates(userId)` - Get all candidates for a user
- `getCandidate(candidateId)` - Get a single candidate
- `createCandidate(candidateData)` - Create a new candidate
- `updateCandidate(candidateId, updateData)` - Update an existing candidate
- `deleteCandidate(candidateId)` - Delete a candidate
- `addToPool(candidateId, poolName, tags?)` - Add candidate to talent pool

### TimelineService
Handles all operations related to the `candidate_timeline` table:
- `createTimelineEntry(entryData)` - Create a timeline entry
- `getCandidateTimeline(candidateId)` - Get timeline entries for a candidate
- `createGeneralEntry(...)` - Create a general timeline entry
- `createInterviewEntry(...)` - Create an interview timeline entry
- `createEmailEntry(...)` - Create an email timeline entry

## Usage

Import the services in your components or executors:

```typescript
import { MessagingService, EventsService, CandidatesService, TimelineService } from '@/services';

// Example: Get all messages for a user
const messages = await MessagingService.getMessages(userId);

// Example: Create a new candidate
const candidate = await CandidatesService.createCandidate({
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'Software Engineer',
  user_id: userId
});

// Example: Create a timeline entry
await TimelineService.createGeneralEntry(
  candidateId,
  userId,
  'Status Update',
  'Candidate moved to interview stage'
);
```

## Benefits

1. **Centralized Logic**: All database operations are in one place
2. **Consistent Error Handling**: Services handle errors consistently
3. **Type Safety**: All methods are strongly typed
4. **Reusability**: No duplicate code across executors and hooks
5. **Maintainability**: Easy to update database operations in one place
6. **Validation**: Built-in validation for required fields and business rules
