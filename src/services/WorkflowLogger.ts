import * as Sentry from "@sentry/react";
import { supabase } from "@/integrations/supabase/client";

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal'
}

export interface WorkflowLogEntry {
  level: LogLevel;
  message: string;
  workflowId?: string;
  executionId?: string;
  nodeId?: string;
  userId?: string;
  metadata?: Record<string, unknown>;
  error?: Error;
  timestamp: Date;
}

export interface WorkflowMetrics {
  workflowId: string;
  executionId: string;
  executionDurationMs: number;
  startTime: Date;
  endTime: Date;
  totalNodesExecuted: number;
  successfulNodes: number;
  failedNodes: number;
  skippedNodes: number;
  nodeErrors: Array<{
    nodeId: string;
    nodeName: string;
    error: string;
    timestamp: Date;
  }>;
  contextData?: Record<string, unknown>;
  errorDetails?: string;
}

class WorkflowLogger {
  private static instance: WorkflowLogger;
  private logs: WorkflowLogEntry[] = [];
  private metricsBuffer: WorkflowMetrics[] = [];
  private flushInterval: NodeJS.Timeout | null = null;
  private sentryEnabled: boolean = false;
  private adaptiveFlushThreshold = 50; // Adaptive threshold for immediate flushing

  private constructor() {
    // Initialize Sentry if not already done
    this.initializeSentry();
    
    // Start the flush interval
    this.startFlushInterval();
  }

  public static getInstance(): WorkflowLogger {
    if (!WorkflowLogger.instance) {
      WorkflowLogger.instance = new WorkflowLogger();
    }
    return WorkflowLogger.instance;
  }

  private initializeSentry() {
    // Check if Sentry DSN is available
    const sentryDsn = import.meta.env.VITE_SENTRY_DSN;
    
    if (sentryDsn && !this.sentryEnabled) {
      Sentry.init({
        dsn: sentryDsn,
        integrations: [
          // Remove BrowserTracing and Replay for now due to import issues
        ],
        tracesSampleRate: import.meta.env.PROD ? 0.1 : 1.0,
        environment: import.meta.env.VITE_ENVIRONMENT || 'development',
        beforeSend(event, hint) {
          // Filter out sensitive data
          if (event.request?.cookies) {
            delete event.request.cookies;
          }
          return event;
        },
      });
      
      this.sentryEnabled = true;
    }
  }

  private startFlushInterval() {
    // Smart batching: Flush logs every 30 seconds or immediately for critical logs
    this.flushInterval = setInterval(() => {
      this.flush();
    }, 30000);
    
    // Also flush when browser is about to close
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.flush();
      });
      
      // Flush on visibility change (when user switches tabs)
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          this.flush();
        }
      });
    }
  }

  public async log(entry: Omit<WorkflowLogEntry, 'timestamp'>): Promise<void> {
    const logEntry: WorkflowLogEntry = {
      ...entry,
      timestamp: new Date()
    };

    // Add to local buffer
    this.logs.push(logEntry);

    // Console log for development
    const logMethod = this.getConsoleMethod(entry.level);
    console[logMethod](`[Workflow ${entry.level.toUpperCase()}]`, entry.message, entry.metadata || '');

    // Send to Sentry for warnings and errors
    if (this.sentryEnabled && [LogLevel.WARN, LogLevel.ERROR, LogLevel.FATAL].includes(entry.level)) {
      const sentryLevel = this.mapToSentryLevel(entry.level);
      
      Sentry.captureMessage(entry.message, {
        level: sentryLevel,
        tags: {
          workflowId: entry.workflowId,
          executionId: entry.executionId,
          nodeId: entry.nodeId,
          userId: entry.userId,
        },
        extra: entry.metadata,
      });

      if (entry.error) {
        Sentry.captureException(entry.error, {
          tags: {
            workflowId: entry.workflowId,
            executionId: entry.executionId,
            nodeId: entry.nodeId,
          },
          extra: entry.metadata,
        });
      }
    }

    // Immediate write to database for errors
    if ([LogLevel.ERROR, LogLevel.FATAL].includes(entry.level)) {
      await this.writeLogsToDatabase([logEntry]);
    }
    
    // Adaptive flushing: If buffer is getting large, flush immediately
    if (this.logs.length >= this.adaptiveFlushThreshold) {
      await this.flush();
    }
  }

  public async logMetrics(metrics: WorkflowMetrics): Promise<void> {
    // Add to buffer
    this.metricsBuffer.push(metrics);

    // Log summary
    await this.log({
      level: LogLevel.INFO,
      message: `Workflow execution completed: ${metrics.successfulNodes}/${metrics.totalNodesExecuted} nodes successful`,
      workflowId: metrics.workflowId,
      executionId: metrics.executionId,
      metadata: {
        duration: metrics.executionDurationMs,
        successRate: metrics.totalNodesExecuted > 0 
          ? (metrics.successfulNodes / metrics.totalNodesExecuted * 100).toFixed(2) + '%'
          : '0%',
        failedNodes: metrics.failedNodes,
        skippedNodes: metrics.skippedNodes
      }
    });

    // Immediate write if there were failures
    if (metrics.failedNodes > 0) {
      await this.writeMetricsToDatabase([metrics]);
    }
  }

  public async flush(): Promise<void> {
    const logsToWrite = [...this.logs];
    const metricsToWrite = [...this.metricsBuffer];

    // Clear buffers
    this.logs = [];
    this.metricsBuffer = [];

    // Write to database
    if (logsToWrite.length > 0) {
      await this.writeLogsToDatabase(logsToWrite);
    }

    if (metricsToWrite.length > 0) {
      await this.writeMetricsToDatabase(metricsToWrite);
    }
  }

  private async writeLogsToDatabase(logs: WorkflowLogEntry[]): Promise<void> {
    try {
      // Update workflow_executions with enriched logs
      const executionUpdates = new Map<string, any[]>();

      for (const log of logs) {
        if (log.executionId) {
          if (!executionUpdates.has(log.executionId)) {
            executionUpdates.set(log.executionId, []);
          }
          executionUpdates.get(log.executionId)!.push({
            level: log.level,
            message: log.message,
            nodeId: log.nodeId,
            timestamp: log.timestamp.toISOString(),
            metadata: log.metadata,
            error: log.error?.message
          });
        }
      }

      // Update each execution with its logs
      for (const [executionId, executionLogs] of executionUpdates) {
        const { data: execution } = await supabase
          .from('workflow_executions')
          .select('execution_log')
          .eq('id', executionId)
          .single();

        if (execution) {
          const currentLog = execution.execution_log || { logs: [] };
          currentLog.logs = [...(currentLog.logs || []), ...executionLogs];

          await supabase
            .from('workflow_executions')
            .update({ 
              execution_log: currentLog,
              updated_at: new Date().toISOString()
            })
            .eq('id', executionId);
        }
      }
    } catch (error) {
      console.error('Failed to write logs to database:', error);
      if (this.sentryEnabled) {
        Sentry.captureException(error);
      }
    }
  }

  private async writeMetricsToDatabase(metrics: WorkflowMetrics[]): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const metricsData = metrics.map(m => ({
        workflow_id: m.workflowId,
        execution_id: m.executionId,
        execution_duration_ms: m.executionDurationMs,
        start_time: m.startTime.toISOString(),
        end_time: m.endTime.toISOString(),
        total_nodes_executed: m.totalNodesExecuted,
        successful_nodes: m.successfulNodes,
        failed_nodes: m.failedNodes,
        skipped_nodes: m.skippedNodes,
        node_errors: m.nodeErrors,
        context_data: m.contextData,
        error_details: m.errorDetails,
        created_by: user.id
      }));

      const { error } = await supabase
        .from('workflow_metrics')
        .insert(metricsData);

      if (error) {
        console.error('Failed to write metrics to database:', error);
        if (this.sentryEnabled) {
          Sentry.captureException(error);
        }
      }
    } catch (error) {
      console.error('Failed to write metrics to database:', error);
      if (this.sentryEnabled) {
        Sentry.captureException(error);
      }
    }
  }

  private getConsoleMethod(level: LogLevel): 'log' | 'info' | 'warn' | 'error' {
    switch (level) {
      case LogLevel.DEBUG:
        return 'log';
      case LogLevel.INFO:
        return 'info';
      case LogLevel.WARN:
        return 'warn';
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        return 'error';
      default:
        return 'log';
    }
  }

  private mapToSentryLevel(level: LogLevel): Sentry.SeverityLevel {
    switch (level) {
      case LogLevel.DEBUG:
        return 'debug';
      case LogLevel.INFO:
        return 'info';
      case LogLevel.WARN:
        return 'warning';
      case LogLevel.ERROR:
        return 'error';
      case LogLevel.FATAL:
        return 'fatal';
      default:
        return 'info';
    }
  }

  public destroy(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }
    this.flush();
  }
}

// Export singleton instance
export const workflowLogger = WorkflowLogger.getInstance();

// Ensure logs are flushed on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    workflowLogger.flush();
  });
}
