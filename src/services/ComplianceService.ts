import { supabase } from "@/integrations/supabase/client";

export interface ComplianceData {
  id: string;
  user_id: string;
  overall_score: number;
  last_updated: string;
  documentation_compliance: number;
  equal_opportunity: number;
  interview_process: number;
  created_at: string;
  updated_at: string;
}

export interface CreateComplianceData {
  user_id: string;
  overall_score: number;
  documentation_compliance: number;
  equal_opportunity: number;
  interview_process: number;
}

export interface UpdateComplianceData {
  id: string;
  overall_score?: number;
  documentation_compliance?: number;
  equal_opportunity?: number;
  interview_process?: number;
}

export class ComplianceService {
  static async getComplianceData(userId: string): Promise<ComplianceData | null> {
    const { data, error } = await supabase
      .from('compliance_data')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) throw error;
    return data;
  }

  static async createComplianceData(complianceData: CreateComplianceData): Promise<ComplianceData> {
    const { data, error } = await supabase
      .from('compliance_data')
      .insert([complianceData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateComplianceData(updateData: UpdateComplianceData): Promise<ComplianceData> {
    const { id, ...updates } = updateData;
    
    const { data, error } = await supabase
      .from('compliance_data')
      .update({ 
        ...updates, 
        last_updated: new Date().toISOString(),
        updated_at: new Date().toISOString() 
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteComplianceData(id: string): Promise<void> {
    const { error } = await supabase
      .from('compliance_data')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }
} 