/**
 * CandidatesService
 * Centralized service for all candidate-related database operations
 */

import { supabase } from '@/integrations/supabase/client';
import { CandidateType } from '@/types/candidate';
import { 
  transformCandidateFromDatabase, 
  transformCandidateToDatabase,
  batchTransform,
  safeJsonParse
} from '@/utils/dataTransformers';

interface SocialLinks {
  linkedin?: string;
  github?: string;
  twitter?: string;
}

interface RecruiterInfo {
  id?: string;
  name?: string;
  avatar?: string;
}

interface Skill {
  name: string;
  level: string;
  years: number;
}

export interface CreateCandidateData {
  name: string;
  role: string;
  email: string;
  phone?: string;
  location?: string;
  tags?: string[];
  experience?: string;
  industry?: string;
  remotePreference?: string;
  visaStatus?: string;
  socialLinks?: SocialLinks;
  recruiter?: RecruiterInfo;
  skills?: Skill[];
  aiSummary?: string;
  userId: string;
}

export interface UpdateCandidateData {
  name?: string;
  role?: string;
  email?: string;
  phone?: string;
  location?: string;
  tags?: string[];
  experience?: string;
  industry?: string;
  remotePreference?: string;
  visaStatus?: string;
  socialLinks?: SocialLinks;
  recruiter?: RecruiterInfo;
  skills?: Skill[];
  aiSummary?: string;
}

export class CandidatesService {
  /**
   * Transform raw database candidate to frontend format
   */
  private static transformCandidate(dbCandidate: Record<string, unknown>): CandidateType {
    if (!dbCandidate) {
      throw new Error('Invalid candidate data');
    }

    // Transform to frontend format
    const transformed = transformCandidateFromDatabase(dbCandidate);
    
    // Ensure proper data types and parse JSON fields
    return {
      ...transformed,
      id: String(transformed.id || ''),
      name: String(transformed.name || ''),
      role: String(transformed.role || ''),
      email: String(transformed.email || ''),
      phone: String(transformed.phone || ''),
      location: String(transformed.location || ''),
      avatar: String(transformed.avatar || '/placeholder.svg'),
      tags: Array.isArray(transformed.tags) ? transformed.tags as string[] : [],
      relationshipScore: Number(transformed.relationshipScore) || 0,
      experience: String(transformed.experience || ''),
      industry: String(transformed.industry || ''),
      remotePreference: String(transformed.remotePreference || ''),
      visaStatus: String(transformed.visaStatus || ''),
      skills: safeJsonParse(transformed.skills, [] as Skill[]),
      screening: safeJsonParse(transformed.screening, undefined),
      createdAt: String(transformed.createdAt || new Date().toISOString()),
      updatedAt: String(transformed.updatedAt || new Date().toISOString()),
      // Ensure recruiter object is properly structured
      recruiter: {
        id: String((transformed.recruiter as RecruiterInfo)?.id || ''),
        name: String((transformed.recruiter as RecruiterInfo)?.name || 'Unknown'),
        avatar: String((transformed.recruiter as RecruiterInfo)?.avatar || '/placeholder.svg')
      },
      // Ensure socialLinks object is properly structured
      socialLinks: {
        github: (transformed.socialLinks as SocialLinks)?.github || undefined,
        linkedin: (transformed.socialLinks as SocialLinks)?.linkedin || undefined,
        twitter: (transformed.socialLinks as SocialLinks)?.twitter || undefined
      }
    } as CandidateType;
  }

  /**
   * Transform frontend candidate data to database format
   */
  private static prepareForDatabase(candidateData: CreateCandidateData | UpdateCandidateData): Record<string, unknown> {
    // Transform to database format (camelCase to snake_case + flatten nested objects)
    const dbData = transformCandidateToDatabase(candidateData as Record<string, unknown>);
    
    // Ensure arrays are properly formatted for database
    if (dbData.tags && Array.isArray(dbData.tags)) {
      // Tags array is already in the correct format for PostgreSQL
    }
    
    if (dbData.skills) {
      dbData.skills = JSON.stringify(dbData.skills);
    }

    return dbData;
  }

  /**
   * Get all candidates for a user
   */
  static async getCandidates(userId: string): Promise<CandidateType[]> {
    const { data, error } = await supabase
      .from('candidates')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching candidates:', error);
      throw new Error(`Failed to fetch candidates: ${error.message}`);
    }

    // Transform all candidates from database format to frontend format
    return batchTransform(data || [], this.transformCandidate);
  }

  /**
   * Get a single candidate by ID
   */
  static async getCandidate(candidateId: string): Promise<CandidateType | null> {
    const { data, error } = await supabase
      .from('candidates')
      .select('*')
      .eq('id', candidateId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No candidate found
      }
      console.error('Error fetching candidate:', error);
      throw new Error(`Failed to fetch candidate: ${error.message}`);
    }

    return data ? this.transformCandidate(data as Record<string, unknown>) : null;
  }

  /**
   * Create a new candidate
   */
  static async createCandidate(candidateData: CreateCandidateData): Promise<CandidateType> {
    // Validate required fields
    if (!candidateData.name?.trim()) {
      throw new Error('Candidate name is required');
    }
    
    if (!candidateData.email?.trim()) {
      throw new Error('Candidate email is required');
    }
    
    if (!candidateData.role?.trim()) {
      throw new Error('Candidate role is required');
    }

    if (!candidateData.userId) {
      throw new Error('User ID is required');
    }

    // Prepare database-format data
    const dbData = this.prepareForDatabase(candidateData);
    
    // Add default values
    const insertData = {
      ...dbData,
      relationship_score: Math.floor(Math.random() * 30) + 70,
      avatar: dbData.avatar || '/placeholder.svg',
      recruiter_name: dbData.recruiter_name || candidateData.email?.split('@')[0] || 'Unknown',
      recruiter_avatar: dbData.recruiter_avatar || '/placeholder.svg',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('candidates')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error('Error creating candidate:', error);
      throw new Error(`Failed to create candidate: ${error.message}`);
    }

    return this.transformCandidate(data as Record<string, unknown>);
  }

  /**
   * Update an existing candidate
   */
  static async updateCandidate(candidateId: string, updateData: UpdateCandidateData): Promise<CandidateType> {
    // Prepare database-format data
    const dbData = this.prepareForDatabase(updateData);
    
    // Add updated timestamp
    dbData.updated_at = new Date().toISOString();

    const { data, error } = await supabase
      .from('candidates')
      .update(dbData)
      .eq('id', candidateId)
      .select()
      .single();

    if (error) {
      console.error('Error updating candidate:', error);
      throw new Error(`Failed to update candidate: ${error.message}`);
    }

    return this.transformCandidate(data as Record<string, unknown>);
  }

  /**
   * Delete a candidate
   */
  static async deleteCandidate(candidateId: string): Promise<void> {
    const { error } = await supabase
      .from('candidates')
      .delete()
      .eq('id', candidateId);

    if (error) {
      console.error('Error deleting candidate:', error);
      throw new Error(`Failed to delete candidate: ${error.message}`);
    }
  }

  /**
   * Add candidate to talent pool (update tags)
   */
  static async addToPool(candidateId: string, poolName: string, tags: string[] = []): Promise<CandidateType> {
    const candidate = await this.getCandidate(candidateId);

    if (!candidate) {
      throw new Error('Candidate not found');
    }

    const updatedTags = [...new Set([...(candidate.tags || []), ...tags, poolName])];

    return this.updateCandidate(candidateId, { tags: updatedTags });
  }

  /**
   * Search candidates by text
   */
  static async searchCandidates(userId: string, searchQuery: string): Promise<CandidateType[]> {
    const { data, error } = await supabase
      .from('candidates')
      .select('*')
      .eq('user_id', userId)
      .or(`name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%,role.ilike.%${searchQuery}%`)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error searching candidates:', error);
      throw new Error(`Failed to search candidates: ${error.message}`);
    }

    return batchTransform(data || [], this.transformCandidate);
  }

  /**
   * Get candidates by tags
   */
  static async getCandidatesByTags(userId: string, tags: string[]): Promise<CandidateType[]> {
    const { data, error } = await supabase
      .from('candidates')
      .select('*')
      .eq('user_id', userId)
      .overlaps('tags', tags)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching candidates by tags:', error);
      throw new Error(`Failed to fetch candidates by tags: ${error.message}`);
    }

    return batchTransform(data || [], this.transformCandidate);
  }

  /**
   * Update candidate's recruiter information
   */
  static async updateCandidateRecruiter(
    candidateId: string,
    recruiter: RecruiterInfo
  ): Promise<CandidateType> {
    return this.updateCandidate(candidateId, { recruiter });
  }

  /**
   * Update candidate's social links
   */
  static async updateCandidateSocialLinks(
    candidateId: string,
    socialLinks: SocialLinks
  ): Promise<CandidateType> {
    return this.updateCandidate(candidateId, { socialLinks });
  }

  /**
   * Update candidate's screening information
   */
  static async updateCandidateScreening(
    candidateId: string,
    screening: Record<string, unknown>
  ): Promise<CandidateType> {
    const candidate = await this.getCandidate(candidateId);
    if (!candidate) {
      throw new Error('Candidate not found');
    }

    const updatedScreening = {
      ...candidate.screening,
      ...screening,
      lastUpdated: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('candidates')
      .update({ 
        screening: JSON.stringify(updatedScreening),
        updated_at: new Date().toISOString()
      })
      .eq('id', candidateId)
      .select()
      .single();

    if (error) {
      console.error('Error updating candidate screening:', error);
      throw new Error(`Failed to update candidate screening: ${error.message}`);
    }

    return this.transformCandidate(data as Record<string, unknown>);
  }

  /**
   * Get candidates with filtering options
   */
  static async getCandidatesWithFilters(
    userId: string,
    filters: {
      tags?: string[];
      experience?: string;
      industry?: string;
      remotePreference?: string;
      visaStatus?: string;
      minRelationshipScore?: number;
      search?: string;
    }
  ): Promise<CandidateType[]> {
    let query = supabase
      .from('candidates')
      .select('*')
      .eq('user_id', userId);

    if (filters.tags && filters.tags.length > 0) {
      query = query.overlaps('tags', filters.tags);
    }

    if (filters.experience) {
      query = query.ilike('experience', `%${filters.experience}%`);
    }

    if (filters.industry) {
      query = query.ilike('industry', `%${filters.industry}%`);
    }

    if (filters.remotePreference) {
      query = query.eq('remote_preference', filters.remotePreference);
    }

    if (filters.visaStatus) {
      query = query.eq('visa_status', filters.visaStatus);
    }

    if (filters.minRelationshipScore) {
      query = query.gte('relationship_score', filters.minRelationshipScore);
    }

    if (filters.search) {
      query = query.or(`name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,role.ilike.%${filters.search}%`);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching candidates with filters:', error);
      throw new Error(`Failed to fetch candidates with filters: ${error.message}`);
    }

    return batchTransform(data || [], this.transformCandidate);
  }
}