import { supabase } from "@/integrations/supabase/client";

export interface HiringTrendsData {
  id: string;
  user_id: string;
  period: string;
  predicted_hires: number;
  actual_hires: number | null;
  created_at: string;
  updated_at: string;
}

export interface CreateHiringTrendsData {
  user_id: string;
  period: string;
  predicted_hires: number;
  actual_hires?: number | null;
}

export interface UpdateHiringTrendsData {
  id: string;
  period?: string;
  predicted_hires?: number;
  actual_hires?: number | null;
}

export class HiringTrendsService {
  static async getHiringTrends(userId: string): Promise<HiringTrendsData[]> {
    const { data, error } = await supabase
      .from('hiring_trends')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  static async createHiringTrend(trendData: CreateHiringTrendsData): Promise<HiringTrendsData> {
    const { data, error } = await supabase
      .from('hiring_trends')
      .insert([trendData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateHiringTrend(updateData: UpdateHiringTrendsData): Promise<HiringTrendsData> {
    const { id, ...updates } = updateData;
    
    const { data, error } = await supabase
      .from('hiring_trends')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteHiringTrend(id: string): Promise<void> {
    const { error } = await supabase
      .from('hiring_trends')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  static async getRecentTrends(userId: string, limit = 6): Promise<HiringTrendsData[]> {
    const { data, error } = await supabase
      .from('hiring_trends')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  static async getPredictionAccuracy(userId: string): Promise<{ accuracy: number; totalPredictions: number }> {
    const { data, error } = await supabase
      .from('hiring_trends')
      .select('predicted_hires, actual_hires')
      .eq('user_id', userId)
      .not('actual_hires', 'is', null);

    if (error) throw error;
    
    if (!data || data.length === 0) {
      return { accuracy: 0, totalPredictions: 0 };
    }

    const accurateCount = data.filter(trend => 
      Math.abs((trend.predicted_hires - (trend.actual_hires || 0)) / trend.predicted_hires) <= 0.1
    ).length;

    return {
      accuracy: (accurateCount / data.length) * 100,
      totalPredictions: data.length
    };
  }
} 