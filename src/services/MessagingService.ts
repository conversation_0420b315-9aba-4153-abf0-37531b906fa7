/**
 * MessagingService
 * Centralized service for all message-related database operations
 */

import { supabase } from '@/integrations/supabase/client';

export interface Message {
  id: string;
  sender_name: string;
  sender_email: string;
  sender_role?: string;
  sender_avatar?: string;
  content: string;
  status: 'unread' | 'read' | 'archived';
  is_starred: boolean;
  follow_up: boolean;
  reminder: boolean;
  user_id: string;
  created_at: string;
  updated_at: string;
}

export interface CreateMessageData {
  sender_name: string;
  sender_email: string;
  sender_role?: string;
  sender_avatar?: string;
  content: string;
  status?: 'unread' | 'read' | 'archived';
  is_starred?: boolean;
  follow_up?: boolean;
  reminder?: boolean;
  user_id: string;
}

export interface UpdateMessageData {
  sender_name?: string;
  sender_email?: string;
  sender_role?: string;
  sender_avatar?: string;
  content?: string;
  status?: 'unread' | 'read' | 'archived';
  is_starred?: boolean;
  follow_up?: boolean;
  reminder?: boolean;
}

export class MessagingService {
  /**
   * Get all messages for a user
   */
  static async getMessages(userId: string): Promise<Message[]> {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching messages:', error);
      throw new Error(`Failed to fetch messages: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get a single message by ID
   */
  static async getMessage(messageId: string): Promise<Message | null> {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('id', messageId)
      .single();

    if (error) {
      console.error('Error fetching message:', error);
      throw new Error(`Failed to fetch message: ${error.message}`);
    }

    return data;
  }

  /**
   * Create a new message
   */
  static async createMessage(messageData: CreateMessageData): Promise<Message> {
    // Validate required fields
    if (!messageData.sender_name?.trim()) {
      throw new Error('Sender name is required');
    }
    
    if (!messageData.sender_email?.trim()) {
      throw new Error('Sender email is required');
    }
    
    if (!messageData.content?.trim()) {
      throw new Error('Message content is required');
    }

    if (!messageData.user_id) {
      throw new Error('User ID is required');
    }

    const { data, error } = await supabase
      .from('messages')
      .insert({
        ...messageData,
        status: messageData.status || 'unread',
        is_starred: messageData.is_starred || false,
        follow_up: messageData.follow_up || false,
        reminder: messageData.reminder || false,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating message:', error);
      throw new Error(`Failed to create message: ${error.message}`);
    }

    return data;
  }

  /**
   * Update an existing message
   */
  static async updateMessage(messageId: string, updateData: UpdateMessageData): Promise<Message> {
    const { data, error } = await supabase
      .from('messages')
      .update(updateData)
      .eq('id', messageId)
      .select()
      .single();

    if (error) {
      console.error('Error updating message:', error);
      throw new Error(`Failed to update message: ${error.message}`);
    }

    return data;
  }

  /**
   * Delete a message
   */
  static async deleteMessage(messageId: string): Promise<void> {
    const { error } = await supabase
      .from('messages')
      .delete()
      .eq('id', messageId);

    if (error) {
      console.error('Error deleting message:', error);
      throw new Error(`Failed to delete message: ${error.message}`);
    }
  }

  /**
   * Mark message as read
   */
  static async markAsRead(messageId: string): Promise<Message> {
    return this.updateMessage(messageId, { status: 'read' });
  }

  /**
   * Mark message as unread
   */
  static async markAsUnread(messageId: string): Promise<Message> {
    return this.updateMessage(messageId, { status: 'unread' });
  }

  /**
   * Archive a message
   */
  static async archiveMessage(messageId: string): Promise<Message> {
    return this.updateMessage(messageId, { status: 'archived' });
  }

  /**
   * Toggle star status
   */
  static async toggleStar(messageId: string, isStarred: boolean): Promise<Message> {
    return this.updateMessage(messageId, { is_starred: isStarred });
  }

  /**
   * Get message templates by category
   */
  static async getMessageTemplate(category: string): Promise<{ content: string; subject?: string } | null> {
    const { data, error } = await supabase
      .from('message_templates')
      .select('content, subject')
      .eq('template_category', category)
      .limit(1);

    if (error) {
      console.error('Error fetching message template:', error);
      return null;
    }

    return data && data.length > 0 ? data[0] : null;
  }

  /**
   * Create a workflow message
   */
  static async createWorkflowMessage(
    userId: string,
    content: string,
    additionalData?: Partial<CreateMessageData>
  ): Promise<Message> {
    return this.createMessage({
      sender_name: 'Workflow Automation',
      sender_email: '<EMAIL>',
      sender_role: 'System',
      content,
      user_id: userId,
      ...additionalData
    });
  }
}
