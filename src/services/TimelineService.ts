/**
 * TimelineService
 * Centralized service for all candidate timeline-related database operations
 */

import { supabase } from '@/integrations/supabase/client';

export interface TimelineEntry {
  id: string;
  candidate_id: string;
  user_id: string;
  event_type: 'general' | 'interview' | 'email' | 'note' | 'status_change';
  title: string;
  description: string;
  event_date: string;
  status: 'completed' | 'upcoming' | 'cancelled';
  created_at: string;
  updated_at: string;
}

export interface CreateTimelineEntryData {
  candidate_id: string;
  user_id: string;
  event_type: 'general' | 'interview' | 'email' | 'note' | 'status_change';
  title: string;
  description: string;
  event_date: string;
  status: 'completed' | 'upcoming' | 'cancelled';
}

export class TimelineService {
  /**
   * Create a timeline entry
   */
  static async createTimelineEntry(entryData: CreateTimelineEntryData): Promise<TimelineEntry> {
    const { data, error } = await supabase
      .from('candidate_timeline')
      .insert(entryData)
      .select()
      .single();

    if (error) {
      console.error('Error creating timeline entry:', error);
      throw new Error(`Failed to create timeline entry: ${error.message}`);
    }

    return data;
  }

  /**
   * Get timeline entries for a candidate
   */
  static async getCandidateTimeline(candidateId: string): Promise<TimelineEntry[]> {
    const { data, error } = await supabase
      .from('candidate_timeline')
      .select('*')
      .eq('candidate_id', candidateId)
      .order('event_date', { ascending: false });

    if (error) {
      console.error('Error fetching candidate timeline:', error);
      throw new Error(`Failed to fetch timeline: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Create a general timeline entry
   */
  static async createGeneralEntry(
    candidateId: string,
    userId: string,
    title: string,
    description: string
  ): Promise<TimelineEntry> {
    return this.createTimelineEntry({
      candidate_id: candidateId,
      user_id: userId,
      event_type: 'general',
      title,
      description,
      event_date: new Date().toISOString(),
      status: 'completed'
    });
  }

  /**
   * Create an interview timeline entry
   */
  static async createInterviewEntry(
    candidateId: string,
    userId: string,
    title: string,
    description: string,
    eventDate: string,
    status: 'completed' | 'upcoming' | 'cancelled' = 'upcoming'
  ): Promise<TimelineEntry> {
    return this.createTimelineEntry({
      candidate_id: candidateId,
      user_id: userId,
      event_type: 'interview',
      title,
      description,
      event_date: eventDate,
      status
    });
  }

  /**
   * Create an email timeline entry
   */
  static async createEmailEntry(
    candidateId: string,
    userId: string,
    title: string,
    description: string
  ): Promise<TimelineEntry> {
    return this.createTimelineEntry({
      candidate_id: candidateId,
      user_id: userId,
      event_type: 'email',
      title,
      description,
      event_date: new Date().toISOString(),
      status: 'completed'
    });
  }
}
