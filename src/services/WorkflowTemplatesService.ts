import { supabase } from "@/integrations/supabase/client";

export interface WorkflowTemplateData {
  id: string;
  user_id: string;
  name: string;
  description: string | null;
  icon: string;
  config: Record<string, unknown>;
  category: string;
  created_at: string;
  updated_at: string;
}

export interface CreateWorkflowTemplateData {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  icon: string;
  config: Record<string, unknown>;
  category: string;
}

export interface UpdateWorkflowTemplateData {
  id: string;
  name?: string;
  description?: string;
  icon?: string;
  config?: Record<string, unknown>;
  category?: string;
}

export class WorkflowTemplatesService {
  static async getWorkflowTemplates(userId: string): Promise<WorkflowTemplateData[]> {
    const { data, error } = await supabase
      .from('workflow_templates_data')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async createWorkflowTemplate(templateData: CreateWorkflowTemplateData): Promise<WorkflowTemplateData> {
    const { data, error } = await supabase
      .from('workflow_templates_data')
      .insert([templateData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateWorkflowTemplate(updateData: UpdateWorkflowTemplateData): Promise<WorkflowTemplateData> {
    const { id, ...updates } = updateData;
    
    const { data, error } = await supabase
      .from('workflow_templates_data')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteWorkflowTemplate(id: string): Promise<void> {
    const { error } = await supabase
      .from('workflow_templates_data')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  static async getWorkflowTemplatesByCategory(userId: string, category: string): Promise<WorkflowTemplateData[]> {
    const { data, error } = await supabase
      .from('workflow_templates_data')
      .select('*')
      .eq('user_id', userId)
      .eq('category', category)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  // Initialize with default workflow templates
  static async initializeWorkflowTemplates(userId: string): Promise<WorkflowTemplateData[]> {
    const defaultTemplates: CreateWorkflowTemplateData[] = [
      {
        id: "candidate-screening",
        user_id: userId,
        name: "Candidate Screening",
        description: "Screen candidates and schedule interviews for qualified ones",
        icon: "Users",
        category: "hiring",
        config: {
          nodes: {
            "new-application-1": {
              id: "new-application-1",
              type: "workflowNode",
              position: { x: 100, y: 100 },
              data: {
                id: "new-application",
                originalId: "new-application",
                category: "triggers",
                type: "trigger",
                label: "New Application",
                description: "Triggers when a new candidate applies",
                config: {
                  jobTypes: "all",
                  notifyTeam: true
                }
              }
            },
            "ai-screen-1": {
              id: "ai-screen-1",
              type: "workflowNode",
              position: { x: 100, y: 250 },
              data: {
                id: "ai-screen",
                originalId: "ai-screen",
                category: "actions",
                type: "action",
                label: "AI Screening",
                description: "Screen resume with AI",
                config: {
                  criteria: "comprehensive",
                  minScore: "75"
                }
              }
            }
          },
          edges: [
            {
              id: "edge-1",
              source: "new-application-1",
              target: "ai-screen-1",
              type: "smoothstep"
            }
          ]
        }
      },
      {
        id: "interview-scheduling",
        user_id: userId,
        name: "Interview Scheduling",
        description: "Automatically schedule interviews with qualified candidates",
        icon: "Calendar",
        category: "scheduling",
        config: {
          nodes: {
            "qualified-candidate-1": {
              id: "qualified-candidate-1",
              type: "workflowNode",
              position: { x: 100, y: 100 },
              data: {
                id: "qualified-candidate",
                originalId: "qualified-candidate",
                category: "triggers",
                type: "trigger",
                label: "Qualified Candidate",
                description: "Triggers when a candidate passes screening",
                config: {
                  minScore: "80"
                }
              }
            },
            "schedule-interview-1": {
              id: "schedule-interview-1",
              type: "workflowNode",
              position: { x: 100, y: 250 },
              data: {
                id: "schedule-interview",
                originalId: "schedule-interview",
                category: "actions",
                type: "action",
                label: "Schedule Interview",
                description: "Schedule interview with candidate",
                config: {
                  duration: "60",
                  type: "video",
                  notifyCandidate: true
                }
              }
            }
          },
          edges: [
            {
              id: "edge-1",
              source: "qualified-candidate-1",
              target: "schedule-interview-1",
              type: "smoothstep"
            }
          ]
        }
      },
      {
        id: "follow-up-automation",
        user_id: userId,
        name: "Follow-up Automation",
        description: "Automatically follow up with candidates after interviews",
        icon: "Mail",
        category: "communication",
        config: {
          nodes: {
            "interview-completed-1": {
              id: "interview-completed-1",
              type: "workflowNode",
              position: { x: 100, y: 100 },
              data: {
                id: "interview-completed",
                originalId: "interview-completed",
                category: "triggers",
                type: "trigger",
                label: "Interview Completed",
                description: "Triggers when an interview is marked as completed",
                config: {
                  waitTime: "24"
                }
              }
            },
            "send-followup-1": {
              id: "send-followup-1",
              type: "workflowNode",
              position: { x: 100, y: 250 },
              data: {
                id: "send-followup",
                originalId: "send-followup",
                category: "actions",
                type: "action",
                label: "Send Follow-up",
                description: "Send follow-up email to candidate",
                config: {
                  template: "interview-followup",
                  delay: "1"
                }
              }
            }
          },
          edges: [
            {
              id: "edge-1",
              source: "interview-completed-1",
              target: "send-followup-1",
              type: "smoothstep"
            }
          ]
        }
      },
      {
        id: "onboarding-automation",
        user_id: userId,
        name: "Onboarding Automation",
        description: "Automate the onboarding process for new hires",
        icon: "UserPlus",
        category: "onboarding",
        config: {
          nodes: {
            "offer-accepted-1": {
              id: "offer-accepted-1",
              type: "workflowNode",
              position: { x: 100, y: 100 },
              data: {
                id: "offer-accepted",
                originalId: "offer-accepted",
                category: "triggers",
                type: "trigger",
                label: "Offer Accepted",
                description: "Triggers when a candidate accepts an offer",
                config: {}
              }
            },
            "create-onboarding-1": {
              id: "create-onboarding-1",
              type: "workflowNode",
              position: { x: 100, y: 250 },
              data: {
                id: "create-onboarding",
                originalId: "create-onboarding",
                category: "actions",
                type: "action",
                label: "Create Onboarding Tasks",
                description: "Create onboarding checklist and tasks",
                config: {
                  includeDocuments: true,
                  includeEquipment: true,
                  includeTraining: true
                }
              }
            }
          },
          edges: [
            {
              id: "edge-1",
              source: "offer-accepted-1",
              target: "create-onboarding-1",
              type: "smoothstep"
            }
          ]
        }
      }
    ];

    const createdData = await Promise.all(
      defaultTemplates.map(template => this.createWorkflowTemplate(template))
    );

    return createdData;
  }
} 