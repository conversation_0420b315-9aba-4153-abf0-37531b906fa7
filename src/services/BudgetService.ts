import { supabase } from "@/integrations/supabase/client";

export interface BudgetData {
  id: string;
  user_id: string;
  total_budget: number;
  spent_amount: number;
  category: string;
  amount: number;
  percentage: number;
  created_at: string;
  updated_at: string;
}

export interface CreateBudgetData {
  user_id: string;
  total_budget: number;
  spent_amount: number;
  category: string;
  amount: number;
  percentage: number;
}

export interface UpdateBudgetData {
  id: string;
  total_budget?: number;
  spent_amount?: number;
  amount?: number;
  percentage?: number;
}

export class BudgetService {
  static async getBudgetData(userId: string): Promise<BudgetData[]> {
    const { data, error } = await supabase
      .from('budget_data')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async createBudgetData(budgetData: CreateBudgetData): Promise<BudgetData> {
    const { data, error } = await supabase
      .from('budget_data')
      .insert([budgetData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateBudgetData(updateData: UpdateBudgetData): Promise<BudgetData> {
    const { id, ...updates } = updateData;
    
    const { data, error } = await supabase
      .from('budget_data')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteBudgetData(id: string): Promise<void> {
    const { error } = await supabase
      .from('budget_data')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  static async getBudgetSummary(userId: string): Promise<{ total_budget: number; spent_amount: number; categories: BudgetData[] }> {
    const budgetData = await this.getBudgetData(userId);
    
    if (budgetData.length === 0) {
      return { total_budget: 0, spent_amount: 0, categories: [] };
    }

    const totalBudget = budgetData[0]?.total_budget || 0;
    const spentAmount = budgetData[0]?.spent_amount || 0;

    return {
      total_budget: totalBudget,
      spent_amount: spentAmount,
      categories: budgetData
    };
  }
} 