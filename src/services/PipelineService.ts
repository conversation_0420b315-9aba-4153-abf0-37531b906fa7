import { supabase } from "@/integrations/supabase/client";

export interface PipelineStageData {
  id: string;
  user_id: string;
  name: string;
  count: number;
  color: string;
  stage_order: number;
  created_at: string;
  updated_at: string;
}

export interface CreatePipelineStageData {
  user_id: string;
  name: string;
  count: number;
  color: string;
  stage_order: number;
}

export interface UpdatePipelineStageData {
  id: string;
  name?: string;
  count?: number;
  color?: string;
  stage_order?: number;
}

export interface PipelineCandidateData {
  id: string;
  user_id: string;
  candidate_name: string;
  role: string;
  stage: string;
  rating: number;
  last_activity: string;
  created_at: string;
  updated_at: string;
}

export interface CreatePipelineCandidateData {
  user_id: string;
  candidate_name: string;
  role: string;
  stage: string;
  rating: number;
  last_activity: string;
}

export interface UpdatePipelineCandidateData {
  id: string;
  candidate_name?: string;
  role?: string;
  stage?: string;
  rating?: number;
  last_activity?: string;
}

export class PipelineService {
  // Pipeline Stages
  static async getPipelineStages(userId: string): Promise<PipelineStageData[]> {
    const { data, error } = await supabase
      .from('pipeline_stages')
      .select('*')
      .eq('user_id', userId)
      .order('stage_order', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  static async createPipelineStage(stageData: CreatePipelineStageData): Promise<PipelineStageData> {
    const { data, error } = await supabase
      .from('pipeline_stages')
      .insert([stageData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updatePipelineStage(updateData: UpdatePipelineStageData): Promise<PipelineStageData> {
    const { id, ...updates } = updateData;
    
    const { data, error } = await supabase
      .from('pipeline_stages')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deletePipelineStage(id: string): Promise<void> {
    const { error } = await supabase
      .from('pipeline_stages')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Pipeline Candidates
  static async getPipelineCandidates(userId: string): Promise<PipelineCandidateData[]> {
    const { data, error } = await supabase
      .from('pipeline_candidates')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async createPipelineCandidate(candidateData: CreatePipelineCandidateData): Promise<PipelineCandidateData> {
    const { data, error } = await supabase
      .from('pipeline_candidates')
      .insert([candidateData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updatePipelineCandidate(updateData: UpdatePipelineCandidateData): Promise<PipelineCandidateData> {
    const { id, ...updates } = updateData;
    
    const { data, error } = await supabase
      .from('pipeline_candidates')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deletePipelineCandidate(id: string): Promise<void> {
    const { error } = await supabase
      .from('pipeline_candidates')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  static async getCandidatesByStage(userId: string, stage: string): Promise<PipelineCandidateData[]> {
    const { data, error } = await supabase
      .from('pipeline_candidates')
      .select('*')
      .eq('user_id', userId)
      .eq('stage', stage)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }
} 