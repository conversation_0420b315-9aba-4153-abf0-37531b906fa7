import { supabase } from "@/integrations/supabase/client";

export interface CandidateMonthlyStatsData {
  id: string;
  user_id: string;
  month: string;
  candidates_count: number;
  placements_count: number;
  year: number;
  created_at: string;
  updated_at: string;
}

export interface CreateCandidateMonthlyStatsData {
  user_id: string;
  month: string;
  candidates_count: number;
  placements_count: number;
  year: number;
}

export interface UpdateCandidateMonthlyStatsData {
  id: string;
  candidates_count?: number;
  placements_count?: number;
}

export interface CandidateSkillsStatsData {
  id: string;
  user_id: string;
  skill_name: string;
  candidate_count: number;
  color: string;
  created_at: string;
  updated_at: string;
}

export interface CreateCandidateSkillsStatsData {
  user_id: string;
  skill_name: string;
  candidate_count: number;
  color: string;
}

export interface UpdateCandidateSkillsStatsData {
  id: string;
  candidate_count?: number;
  color?: string;
}

// Transform data for charts
export interface MonthlyChartData {
  month: string;
  candidates: number;
  placements: number;
}

export interface SkillsChartData {
  name: string;
  value: number;
  color: string;
}

export class CandidateAnalyticsService {
  // Monthly Stats
  static async getCandidateMonthlyStats(userId: string, year?: number): Promise<CandidateMonthlyStatsData[]> {
    const currentYear = year || new Date().getFullYear();
    
    const { data, error } = await supabase
      .from('candidate_monthly_stats')
      .select('*')
      .eq('user_id', userId)
      .eq('year', currentYear)
      .order('month', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  static async createCandidateMonthlyStats(statsData: CreateCandidateMonthlyStatsData): Promise<CandidateMonthlyStatsData> {
    const { data, error } = await supabase
      .from('candidate_monthly_stats')
      .insert([statsData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateCandidateMonthlyStats(updateData: UpdateCandidateMonthlyStatsData): Promise<CandidateMonthlyStatsData> {
    const { id, ...updates } = updateData;
    
    const { data, error } = await supabase
      .from('candidate_monthly_stats')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteCandidateMonthlyStats(id: string): Promise<void> {
    const { error } = await supabase
      .from('candidate_monthly_stats')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Skills Stats
  static async getCandidateSkillsStats(userId: string): Promise<CandidateSkillsStatsData[]> {
    const { data, error } = await supabase
      .from('candidate_skills_stats')
      .select('*')
      .eq('user_id', userId)
      .order('candidate_count', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async createCandidateSkillsStats(skillData: CreateCandidateSkillsStatsData): Promise<CandidateSkillsStatsData> {
    const { data, error } = await supabase
      .from('candidate_skills_stats')
      .insert([skillData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateCandidateSkillsStats(updateData: UpdateCandidateSkillsStatsData): Promise<CandidateSkillsStatsData> {
    const { id, ...updates } = updateData;
    
    const { data, error } = await supabase
      .from('candidate_skills_stats')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteCandidateSkillsStats(id: string): Promise<void> {
    const { error } = await supabase
      .from('candidate_skills_stats')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Chart Data Transformers
  static transformMonthlyDataForChart(data: CandidateMonthlyStatsData[]): MonthlyChartData[] {
    return data.map(item => ({
      month: item.month,
      candidates: item.candidates_count,
      placements: item.placements_count
    }));
  }

  static transformSkillsDataForChart(data: CandidateSkillsStatsData[]): SkillsChartData[] {
    return data.map(item => ({
      name: item.skill_name,
      value: item.candidate_count,
      color: item.color
    }));
  }

  // Bulk operations for easy setup
  static async initializeMonthlyData(userId: string): Promise<CandidateMonthlyStatsData[]> {
    const currentYear = new Date().getFullYear();
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    const defaultData: CreateCandidateMonthlyStatsData[] = months.map(month => ({
      user_id: userId,
      month,
      candidates_count: Math.floor(Math.random() * 30) + 40, // 40-70 candidates
      placements_count: Math.floor(Math.random() * 10) + 5,  // 5-15 placements
      year: currentYear
    }));

    const createdData = await Promise.all(
      defaultData.map(data => this.createCandidateMonthlyStats(data))
    );

    return createdData;
  }

  static async initializeSkillsData(userId: string): Promise<CandidateSkillsStatsData[]> {
    const skillsWithColors = [
      { skill: 'React', count: 234, color: '#8884d8' },
      { skill: 'TypeScript', count: 189, color: '#82ca9d' },
      { skill: 'Node.js', count: 156, color: '#ffc658' },
      { skill: 'Python', count: 142, color: '#ff7300' },
      { skill: 'Java', count: 98, color: '#8dd1e1' }
    ];

    const defaultData: CreateCandidateSkillsStatsData[] = skillsWithColors.map(item => ({
      user_id: userId,
      skill_name: item.skill,
      candidate_count: item.count,
      color: item.color
    }));

    const createdData = await Promise.all(
      defaultData.map(data => this.createCandidateSkillsStats(data))
    );

    return createdData;
  }
} 