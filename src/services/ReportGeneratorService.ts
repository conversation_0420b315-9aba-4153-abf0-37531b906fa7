import { supabase } from "@/integrations/supabase/client";

export interface ReportTemplateData {
  id: string;
  user_id: string;
  name: string;
  description: string | null;
  type: 'candidate' | 'analytics' | 'job' | 'performance';
  format: 'pdf' | 'excel' | 'csv';
  metrics: string[];
  last_generated: string | null;
  created_at: string;
  updated_at: string;
}

export interface CreateReportTemplateData {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  type: 'candidate' | 'analytics' | 'job' | 'performance';
  format: 'pdf' | 'excel' | 'csv';
  metrics: string[];
  last_generated?: string;
}

export interface UpdateReportTemplateData {
  id: string;
  name?: string;
  description?: string;
  metrics?: string[];
  last_generated?: string;
}

export interface GeneratedReportData {
  id: string;
  user_id: string;
  name: string;
  type: string;
  format: string;
  generated_at: string;
  file_size: string;
  download_url: string;
  created_at: string;
  updated_at: string;
}

export interface CreateGeneratedReportData {
  id: string;
  user_id: string;
  name: string;
  type: string;
  format: string;
  file_size: string;
  download_url: string;
}

export interface UpdateGeneratedReportData {
  id: string;
  name?: string;
  download_url?: string;
  file_size?: string;
}

export class ReportGeneratorService {
  // Report Templates
  static async getReportTemplates(userId: string): Promise<ReportTemplateData[]> {
    const { data, error } = await supabase
      .from('report_templates_data')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async createReportTemplate(templateData: CreateReportTemplateData): Promise<ReportTemplateData> {
    const { data, error } = await supabase
      .from('report_templates_data')
      .insert([{
        ...templateData,
        metrics: JSON.stringify(templateData.metrics)
      }])
      .select()
      .single();

    if (error) throw error;
    return {
      ...data,
      metrics: typeof data.metrics === 'string' ? JSON.parse(data.metrics) : data.metrics
    };
  }

  static async updateReportTemplate(updateData: UpdateReportTemplateData): Promise<ReportTemplateData> {
    const { id, ...updates } = updateData;
    
    const processedUpdates = {
      ...updates,
      ...(updates.metrics && { metrics: JSON.stringify(updates.metrics) }),
      updated_at: new Date().toISOString()
    };
    
    const { data, error } = await supabase
      .from('report_templates_data')
      .update(processedUpdates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return {
      ...data,
      metrics: typeof data.metrics === 'string' ? JSON.parse(data.metrics) : data.metrics
    };
  }

  static async deleteReportTemplate(id: string): Promise<void> {
    const { error } = await supabase
      .from('report_templates_data')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Generated Reports
  static async getGeneratedReports(userId: string): Promise<GeneratedReportData[]> {
    const { data, error } = await supabase
      .from('generated_reports_data')
      .select('*')
      .eq('user_id', userId)
      .order('generated_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async createGeneratedReport(reportData: CreateGeneratedReportData): Promise<GeneratedReportData> {
    const { data, error } = await supabase
      .from('generated_reports_data')
      .insert([reportData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateGeneratedReport(updateData: UpdateGeneratedReportData): Promise<GeneratedReportData> {
    const { id, ...updates } = updateData;
    
    const { data, error } = await supabase
      .from('generated_reports_data')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteGeneratedReport(id: string): Promise<void> {
    const { error } = await supabase
      .from('generated_reports_data')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Bulk operations for easy setup
  static async initializeReportTemplates(userId: string): Promise<ReportTemplateData[]> {
    const defaultTemplates: CreateReportTemplateData[] = [
      {
        id: 'candidate-summary',
        user_id: userId,
        name: 'Candidate Summary Report',
        description: 'Overview of all candidates including status, sources, and demographics',
        type: 'candidate',
        format: 'pdf',
        metrics: ['Total Candidates', 'Source Breakdown', 'Status Distribution', 'Skills Analysis'],
        last_generated: '2024-01-15T10:30:00Z'
      },
      {
        id: 'hiring-analytics',
        user_id: userId,
        name: 'Hiring Analytics Report',
        description: 'Comprehensive hiring metrics and performance indicators',
        type: 'analytics',
        format: 'excel',
        metrics: ['Time to Hire', 'Cost per Hire', 'Source Effectiveness', 'Conversion Rates'],
        last_generated: '2024-01-14T15:45:00Z'
      },
      {
        id: 'job-performance',
        user_id: userId,
        name: 'Job Performance Report',
        description: 'Analysis of job posting performance and application trends',
        type: 'job',
        format: 'pdf',
        metrics: ['Job Views', 'Application Rates', 'Time to Fill', 'Salary Benchmarks'],
        last_generated: '2024-01-13T09:20:00Z'
      },
      {
        id: 'pipeline-analysis',
        user_id: userId,
        name: 'Pipeline Analysis Report',
        description: 'Detailed analysis of recruitment pipeline efficiency',
        type: 'performance',
        format: 'excel',
        metrics: ['Stage Conversion', 'Bottleneck Analysis', 'Interview Success', 'Offer Acceptance']
      }
    ];

    const createdData = await Promise.all(
      defaultTemplates.map(template => this.createReportTemplate(template))
    );

    return createdData;
  }

  static async initializeGeneratedReports(userId: string): Promise<GeneratedReportData[]> {
    const defaultReports: CreateGeneratedReportData[] = [
      {
        id: '1',
        user_id: userId,
        name: 'Candidate Summary Report - January 2024',
        type: 'Candidate',
        format: 'PDF',
        file_size: '2.4 MB',
        download_url: '#'
      },
      {
        id: '2',
        user_id: userId,
        name: 'Hiring Analytics Report - Q4 2023',
        type: 'Analytics',
        format: 'Excel',
        file_size: '5.1 MB',
        download_url: '#'
      },
      {
        id: '3',
        user_id: userId,
        name: 'Job Performance Report - December 2023',
        type: 'Job Performance',
        format: 'PDF',
        file_size: '1.8 MB',
        download_url: '#'
      }
    ];

    const createdData = await Promise.all(
      defaultReports.map(report => this.createGeneratedReport(report))
    );

    return createdData;
  }
} 