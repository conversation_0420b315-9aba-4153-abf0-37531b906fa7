
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useNotificationSettings } from "@/hooks/useNotificationSettings";
import { Loader2, Bell } from "lucide-react";

export const SettingsNotifications = () => {
  const { settings, updateSetting, saveSettings, isLoading } = useNotificationSettings();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="w-5 h-5" />
          Notification Settings
        </CardTitle>
        <CardDescription>
          Configure how you receive notifications and updates
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="email-notifications">Email Notifications</Label>
            <p className="text-sm text-muted-foreground">
              Receive email notifications for important updates
            </p>
          </div>
          <Switch 
            id="email-notifications" 
            checked={settings.emailNotifications}
            onCheckedChange={(checked) => updateSetting('emailNotifications', checked)}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="application-updates">Application Updates</Label>
            <p className="text-sm text-muted-foreground">
              Get notified about candidate applications and status changes
            </p>
          </div>
          <Switch 
            id="application-updates" 
            checked={settings.applicationUpdates}
            onCheckedChange={(checked) => updateSetting('applicationUpdates', checked)}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="marketing">Marketing Communications</Label>
            <p className="text-sm text-muted-foreground">
              Receive newsletters and product updates
            </p>
          </div>
          <Switch 
            id="marketing" 
            checked={settings.marketing}
            onCheckedChange={(checked) => updateSetting('marketing', checked)}
          />
        </div>

        <Button 
          onClick={saveSettings} 
          disabled={isLoading}
          className="w-full md:w-auto"
        >
          {isLoading && (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          )}
          Save Notification Settings
        </Button>
      </CardContent>
    </Card>
  );
};
