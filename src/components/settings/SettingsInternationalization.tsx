import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Internationalization } from "@/components/layout/Internationalization";
import { Globe } from "lucide-react";

export function SettingsInternationalization() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Internationalization Settings
          </CardTitle>
          <CardDescription>
            Configure language, region, and formatting preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Internationalization />
        </CardContent>
      </Card>
    </div>
  );
}