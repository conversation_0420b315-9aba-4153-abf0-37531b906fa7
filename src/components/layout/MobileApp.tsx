import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { QrCode, Smartphone, Bell, Download } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

export function MobileApp() {
  const { toast } = useToast();
  const [pushNotifications, setPushNotifications] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  
  const handleDownloadApp = () => {
    toast({
      title: "Download Started",
      description: "The mobile app download has started."
    });
  };
  
  const handleSendLink = () => {
    toast({
      title: "Link Sent",
      description: "A download link has been sent to your email."
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Smartphone className="h-5 w-5" />
          Mobile App
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col md:flex-row gap-6 items-center">
          <div className="flex-1 space-y-4">
            <h3 className="text-lg font-medium">HireLogix Mobile</h3>
            <p className="text-muted-foreground">
              Access your recruitment dashboard on the go with our mobile app. Manage candidates, review applications, and stay updated with real-time notifications.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={handleDownloadApp} className="flex-1">
                <Download className="mr-2 h-4 w-4" />
                Download App
              </Button>
              <Button variant="outline" onClick={handleSendLink} className="flex-1">
                Send Download Link
              </Button>
            </div>
          </div>
          
          <div className="bg-muted p-6 rounded-lg">
            <QrCode className="h-32 w-32" />
            <p className="text-xs text-center mt-2 text-muted-foreground">
              Scan to download
            </p>
          </div>
        </div>
        
        <div className="border-t pt-6">
          <h3 className="text-lg font-medium mb-4">Mobile Notification Settings</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="push-notifications">Push Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive push notifications on your mobile device
                </p>
              </div>
              <Switch
                id="push-notifications"
                checked={pushNotifications}
                onCheckedChange={setPushNotifications}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="email-notifications">Email Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive email notifications for important updates
                </p>
              </div>
              <Switch
                id="email-notifications"
                checked={emailNotifications}
                onCheckedChange={setEmailNotifications}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="candidate-alerts">Candidate Alerts</Label>
                <p className="text-sm text-muted-foreground">
                  Get notified when candidates update their profiles
                </p>
              </div>
              <Switch
                id="candidate-alerts"
                checked={true}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="interview-reminders">Interview Reminders</Label>
                <p className="text-sm text-muted-foreground">
                  Receive reminders before scheduled interviews
                </p>
              </div>
              <Switch
                id="interview-reminders"
                checked={true}
              />
            </div>
          </div>
        </div>
        
        <div className="bg-muted/50 p-4 rounded-lg flex items-start gap-2">
          <Bell className="h-5 w-5 text-primary shrink-0 mt-0.5" />
          <div>
            <h4 className="font-medium">Stay Connected</h4>
            <p className="text-sm text-muted-foreground">
              The mobile app syncs automatically with your web dashboard, ensuring you always have the latest information at your fingertips.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}