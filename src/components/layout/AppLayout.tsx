import React from "react";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { AppHeader } from "@/components/layout/AppHeader";
import { FeedbackWidget } from "@/components/feedback/FeedbackWidget";
import { useAnalyticsAutoUpdate } from "@/hooks/useAnalyticsAutoUpdate";
import { useAuth } from "@/contexts/AuthContext";
import { EventsService } from "@/services/EventsService";

interface AppLayoutProps {
  children: React.ReactNode;
}

// LocalEvents context
export const LocalEventsContext = React.createContext<{
  localEvents: any[];
  setLocalEvents: React.Dispatch<React.SetStateAction<any[]>>;
}>({ localEvents: [], setLocalEvents: () => {} });

export function AppLayout({ children }: AppLayoutProps) {
  // Auto-update analytics when data changes (maintains real-time consistency)
  useAnalyticsAutoUpdate();
  const { user } = useAuth();

  // Shared localEvents state
  const [localEvents, setLocalEvents] = React.useState<any[]>([]);

  // On mount or when user changes, fetch events if localEvents is empty
  React.useEffect(() => {
    if (user && localEvents.length === 0) {
      EventsService.getEvents(user.id).then(events => {
        setLocalEvents(events);
      }).catch(() => {});
    }
  }, [user]);

  return (
    <LocalEventsContext.Provider value={{ localEvents, setLocalEvents }}>
      <SidebarProvider defaultOpen={false}>
        <div className="min-h-screen flex w-full">
          <AppSidebar />
          <div className="flex-1 flex flex-col transition-all duration-300 ease-linear peer-data-[state=collapsed]:ml-[--sidebar-width-icon] peer-data-[state=expanded]:ml-[--sidebar-width]">
            <AppHeader />
            <div className="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8 max-w-[1600px] animate-fade-in">
              {children}
            </div>
          </div>
        </div>
        <FeedbackWidget />
      </SidebarProvider>
    </LocalEventsContext.Provider>
  );
}
