import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Calendar, Clock, MapPin, Link, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useCreateEvent } from "@/hooks/useCreateEvent";
import { useUpdateEvent } from "@/hooks/useUpdateEvent";
import { useDeleteEvent } from "@/hooks/useDeleteEvent";
import { Event } from "@/hooks/useEvents";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";

interface EventManagerProps {
  event?: Event;
  isOpen: boolean;
  onClose: () => void;
  mode: 'create' | 'edit';
  onEventCreated?: (eventObj: any) => void;
  onEventEdited?: (eventObj: any) => void;
  onEventDeleted?: (eventId: string) => void;
}

export function EventManager({ event, isOpen, onClose, mode, onEventCreated, onEventEdited, onEventDeleted }: EventManagerProps) {
  const { toast } = useToast();
  const createEvent = useCreateEvent();
  const updateEvent = useUpdateEvent();
  const deleteEvent = useDeleteEvent();
  
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    start_date: "",
    start_time: "",
    end_date: "",
    end_time: "",
    location: "",
    meeting_link: "",
    event_type: "meeting" as 'meeting' | 'interview' | 'call' | 'presentation' | 'other',
    priority: "medium" as 'low' | 'medium' | 'high',
    category: "general" as 'general' | 'recruitment' | 'client' | 'internal'
  });

  // Reset form when dialog opens/closes or mode changes
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && event) {
        const startDate = new Date(event.start_time);
        const endDate = new Date(event.end_time);
        
        setFormData({
          title: event.title,
          description: event.description || "",
          start_date: startDate.toISOString().split('T')[0],
          start_time: startDate.toTimeString().slice(0, 5),
          end_date: endDate.toISOString().split('T')[0],
          end_time: endDate.toTimeString().slice(0, 5),
          location: event.location || "",
          meeting_link: event.meeting_link || "",
          event_type: event.event_type as any,
          priority: event.priority as any,
          category: event.category as any
        });
      } else if (mode === 'create' && event) {
        // Prefill with provided event (from date click)
        const startDate = new Date(event.start_time);
        const endDate = new Date(event.end_time);
        setFormData({
          title: event.title || "",
          description: event.description || "",
          start_date: startDate.toISOString().split('T')[0],
          start_time: startDate.toTimeString().slice(0, 5),
          end_date: endDate.toISOString().split('T')[0],
          end_time: endDate.toTimeString().slice(0, 5),
          location: event.location || "",
          meeting_link: event.meeting_link || "",
          event_type: event.event_type || "meeting",
          priority: event.priority || "medium",
          category: event.category || "general"
        });
      } else {
        // For create mode, set default values
        const now = new Date();
        const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);
        
        setFormData({
          title: "",
          description: "",
          start_date: now.toISOString().split('T')[0],
          start_time: now.toTimeString().slice(0, 5),
          end_date: now.toISOString().split('T')[0],
          end_time: oneHourLater.toTimeString().slice(0, 5),
          location: "",
          meeting_link: "",
          event_type: "meeting",
          priority: "medium",
          category: "general"
        });
      }
    }
  }, [isOpen, mode, event]);

  const handleSubmit = async () => {
    try {
      // Validate form
      if (!formData.title.trim()) {
        toast({
          title: "Error",
          description: "Event title is required",
          variant: "destructive"
        });
        return;
      }
      
      // Create start and end time Date objects
      const startDateTime = new Date(`${formData.start_date}T${formData.start_time}`);
      const endDateTime = new Date(`${formData.end_date}T${formData.end_time}`);
      
      // Validate dates
      if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
        toast({
          title: "Error",
          description: "Invalid date or time format",
          variant: "destructive"
        });
        return;
      }
      
      if (endDateTime <= startDateTime) {
        toast({
          title: "Error",
          description: "End time must be after start time",
          variant: "destructive"
        });
        return;
      }
      
      const eventData = {
        title: formData.title,
        description: formData.description,
        start_time: startDateTime.toISOString(),
        end_time: endDateTime.toISOString(),
        location: formData.location,
        meeting_link: formData.meeting_link,
        event_type: formData.event_type,
        priority: formData.priority,
        category: formData.category
      };
      
      if (mode === 'create') {
        const created = await createEvent.mutateAsync(eventData);
        if (onEventCreated) {
          onEventCreated(created || {
            ...eventData,
            id: created?.id || Math.random().toString(36).slice(2),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });
        }
      } else if (mode === 'edit' && event) {
        const updated = await updateEvent.mutateAsync({
          id: event.id,
          ...eventData
        });
        if (onEventEdited) {
          onEventEdited(updated || { ...event, ...eventData });
        }
      }
      
      onClose();
    } catch (error) {
      console.error('Error saving event:', error);
      toast({
        title: "Error",
        description: "Failed to save event. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleDelete = async () => {
    if (!event) return;
    
    try {
      await deleteEvent.mutateAsync(event.id);
      if (onEventDeleted) onEventDeleted(event.id);
      onClose();
    } catch (error) {
      console.error('Error deleting event:', error);
      toast({
        title: "Error",
        description: "Failed to delete event. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{mode === 'create' ? 'Create Event' : 'Edit Event'}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Event Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData({...formData, title: e.target.value})}
              placeholder="Enter event title"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              placeholder="Enter event description"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Start Date & Time</Label>
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => setFormData({...formData, start_date: e.target.value})}
                  />
                </div>
                <div className="flex-1">
                  <Input
                    type="time"
                    value={formData.start_time}
                    onChange={(e) => setFormData({...formData, start_time: e.target.value})}
                  />
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>End Date & Time</Label>
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input
                    type="date"
                    value={formData.end_date}
                    onChange={(e) => setFormData({...formData, end_date: e.target.value})}
                  />
                </div>
                <div className="flex-1">
                  <Input
                    type="time"
                    value={formData.end_time}
                    onChange={(e) => setFormData({...formData, end_time: e.target.value})}
                  />
                </div>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <div className="flex">
                <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-input bg-muted text-muted-foreground">
                  <MapPin className="h-4 w-4" />
                </span>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData({...formData, location: e.target.value})}
                  placeholder="Enter location"
                  className="rounded-l-none"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="meeting_link">Meeting Link</Label>
              <div className="flex">
                <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-input bg-muted text-muted-foreground">
                  <Link className="h-4 w-4" />
                </span>
                <Input
                  id="meeting_link"
                  value={formData.meeting_link}
                  onChange={(e) => setFormData({...formData, meeting_link: e.target.value})}
                  placeholder="Enter meeting link"
                  className="rounded-l-none"
                />
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="event_type">Event Type</Label>
              <Select
                value={formData.event_type}
                onValueChange={(value: any) => setFormData({...formData, event_type: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select event type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="meeting">Meeting</SelectItem>
                  <SelectItem value="interview">Interview</SelectItem>
                  <SelectItem value="call">Call</SelectItem>
                  <SelectItem value="presentation">Presentation</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select
                value={formData.priority}
                onValueChange={(value: any) => setFormData({...formData, priority: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={formData.category}
                onValueChange={(value: any) => setFormData({...formData, category: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general">General</SelectItem>
                  <SelectItem value="recruitment">Recruitment</SelectItem>
                  <SelectItem value="client">Client</SelectItem>
                  <SelectItem value="internal">Internal</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <DialogFooter className="flex justify-between">
          <div>
            {mode === 'edit' && (
              <Button 
                variant="destructive" 
                onClick={handleDelete}
                disabled={deleteEvent.isPending}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {deleteEvent.isPending ? "Deleting..." : "Delete Event"}
              </Button>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleSubmit}
              disabled={createEvent.isPending || updateEvent.isPending}
            >
              {(createEvent.isPending || updateEvent.isPending) ? "Saving..." : "Save Event"}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}