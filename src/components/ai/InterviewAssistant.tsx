import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Loader2, <PERSON>rkles, Copy, Check, Save } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { generateInterviewQuestions } from "@/utils/gemini";

interface Question {
  id: string;
  question: string;
  category: 'technical' | 'behavioral' | 'cultural' | 'experience';
  difficulty: 'easy' | 'medium' | 'hard';
  rationale: string;
}

export function InterviewAssistant() {
  const [jobTitle, setJobTitle] = useState("");
  const [jobDescription, setJobDescription] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [savedQuestions, setSavedQuestions] = useState<Question[]>([]);
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const { toast } = useToast();

  const handleGenerateQuestions = async () => {
    if (!jobTitle.trim()) {
      toast({
        title: "Job title required",
        description: "Please enter a job title to generate relevant questions.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    try {
      // Use the Gemini-powered function to generate questions
      const generatedQuestions = await generateInterviewQuestions(jobTitle, jobDescription || "");
      setQuestions(generatedQuestions);
      toast({
        title: "Questions Generated",
        description: `Generated ${generatedQuestions.length} interview questions for ${jobTitle}.`
      });
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Failed to generate interview questions. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyQuestion = (id: string, question: string) => {
    navigator.clipboard.writeText(question);
    setCopiedId(id);
    setTimeout(() => setCopiedId(null), 2000);
    toast({
      title: "Copied to Clipboard",
      description: "Question has been copied to your clipboard."
    });
  };

  const handleSaveQuestion = (question: Question) => {
    if (!savedQuestions.some(q => q.id === question.id)) {
      setSavedQuestions([...savedQuestions, question]);
      toast({
        title: "Question Saved",
        description: "Question has been added to your saved questions."
      });
    }
  };

  const handleRemoveSavedQuestion = (id: string) => {
    setSavedQuestions(savedQuestions.filter(q => q.id !== id));
    toast({
      title: "Question Removed",
      description: "Question has been removed from your saved questions."
    });
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'hard': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5" />
          AI Interview Question Generator
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="generate">
          <TabsList className="mb-4">
            <TabsTrigger value="generate">Generate Questions</TabsTrigger>
            <TabsTrigger value="saved">Saved Questions ({savedQuestions.length})</TabsTrigger>
          </TabsList>
          
          <TabsContent value="generate" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="job-title">Job Title</Label>
                <Input
                  id="job-title"
                  placeholder="e.g., Senior Frontend Developer"
                  value={jobTitle}
                  onChange={(e) => setJobTitle(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="job-description">Job Description (Optional)</Label>
                <Textarea
                  id="job-description"
                  placeholder="Enter job description for more targeted questions..."
                  value={jobDescription}
                  onChange={(e) => setJobDescription(e.target.value)}
                  rows={1}
                />
              </div>
            </div>
            
            <Button 
              onClick={handleGenerateQuestions} 
              disabled={isGenerating || !jobTitle.trim()}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Questions...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Generate Interview Questions
                </>
              )}
            </Button>
            
            {questions.length > 0 && (
              <ScrollArea className="h-[400px] mt-4">
                <div className="space-y-4">
                  {questions.map((q) => (
                    <Card key={q.id} className="p-4">
                      <div className="flex gap-2 mb-2">
                        <Badge>{q.category}</Badge>
                        <Badge className={getDifficultyColor(q.difficulty) + " text-white"}>
                          {q.difficulty}
                        </Badge>
                      </div>
                      <p className="font-medium mb-2">{q.question}</p>
                      <p className="text-sm text-muted-foreground mb-4">
                        Rationale: {q.rationale}
                      </p>
                      <div className="flex justify-end gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleCopyQuestion(q.id, q.question)}
                        >
                          {copiedId === q.id ? (
                            <>
                              <Check className="h-4 w-4 mr-1" />
                              Copied
                            </>
                          ) : (
                            <>
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </>
                          )}
                        </Button>
                        <Button 
                          size="sm"
                          onClick={() => handleSaveQuestion(q)}
                        >
                          <Save className="h-4 w-4 mr-1" />
                          Save
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            )}
          </TabsContent>
          
          <TabsContent value="saved">
            {savedQuestions.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No saved questions yet. Generate and save questions to see them here.
              </div>
            ) : (
              <ScrollArea className="h-[400px]">
                <div className="space-y-4">
                  {savedQuestions.map((q) => (
                    <Card key={q.id} className="p-4">
                      <div className="flex gap-2 mb-2">
                        <Badge>{q.category}</Badge>
                        <Badge className={getDifficultyColor(q.difficulty) + " text-white"}>
                          {q.difficulty}
                        </Badge>
                      </div>
                      <p className="font-medium mb-2">{q.question}</p>
                      <p className="text-sm text-muted-foreground mb-4">
                        Rationale: {q.rationale}
                      </p>
                      <div className="flex justify-end gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleCopyQuestion(q.id, q.question)}
                        >
                          {copiedId === q.id ? (
                            <>
                              <Check className="h-4 w-4 mr-1" />
                              Copied
                            </>
                          ) : (
                            <>
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </>
                          )}
                        </Button>
                        <Button 
                          variant="destructive" 
                          size="sm"
                          onClick={() => handleRemoveSavedQuestion(q.id)}
                        >
                          Remove
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}