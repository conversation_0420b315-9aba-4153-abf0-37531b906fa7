import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend
} from 'recharts';
import { 
  RefreshCw, 
  Loader2, 
  Bar<PERSON>hart3, 
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  XCircle,
  Clock
} from "lucide-react";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useWorkflowUsageSummary } from "@/hooks/useAnalyticsMetrics";

// Gradient color palettes for charts
const BAR_GRADIENTS = [
  ['#8B5CF6', '#D946EF'], // Purple to Pink
  ['#6366F1', '#8B5CF6'], // Indigo to Purple
  ['#3B82F6', '#6366F1'], // Blue to Indigo
  ['#4F46E5', '#3B82F6'], // Indigo to Blue
];
const PIE_GRADIENTS = [
  // More vibrant and contrasting gradients for better visibility
  ['#10b981', '#059669'], // Completed: Emerald to darker green
  ['#ef4444', '#b91c1c'], // Failed: Red to dark red
  ['#3b82f6', '#1e40af'], // In Progress: Blue to dark blue
];

// Custom tooltip for PieChart and BarChart
const CustomTooltip = ({ active, payload, label }: { active?: any; payload?: any; label?: any }) => {
  if (active && payload && payload.length) {
    return (
      <div style={{
        background: 'rgba(255,255,255,0.95)',
        borderRadius: 8,
        border: '1px solid #e2e8f0',
        boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
        padding: 12,
      }}>
        {label && <div className="font-semibold mb-1">{label}</div>}
        {payload.map((entry, idx) => (
          <div key={idx} style={{ color: entry.color, marginBottom: 2 }}>
            <span className="font-medium">{entry.name}:</span> {entry.value}
          </div>
        ))}
      </div>
    );
  }
  return null;
};

interface WorkflowExecution {
  id: string;
  workflow_id: string;
  status: 'completed' | 'failed' | 'in_progress';
  started_at: string;
  completed_at: string | null;
  execution_log: any;
  created_by: string;
}

interface WorkflowConfiguration {
  id: string;
  name: string;
  description: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export function WorkflowAnalytics() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  
  // Real-time workflow executions subscription
  const { records: executions = [], isLoading: executionsLoading } = useRealtimeCollection(
    'workflow_executions',
    async () => {
      if (!user) return [];
      
      try {
        const { data, error } = await supabase
          .from('workflow_executions')
          .select('*')
          .eq('created_by', user.id)
          .order('started_at', { ascending: false });
          
        if (error) {
          console.error('Error fetching workflow executions:', error);
          return [];
        }
        
        return data || [];
      } catch (error) {
        console.error('Error in workflow executions fetch:', error);
        return [];
      }
    },
    'public',
    `created_by=eq.${user?.id}`
  );
  
  // Real-time workflow configurations subscription
  const { records: workflows = [], isLoading: workflowsLoading } = useRealtimeCollection(
    'workflow_configurations',
    async () => {
      if (!user) return [];
      
      try {
        const { data, error } = await supabase
          .from('workflow_configurations')
          .select('id, name, description, is_active, created_at, updated_at')
          .eq('created_by', user.id);
        
        if (error) {
          console.error('Error fetching workflow configurations:', error);
          return [];
        }
        
        return data || [];
      } catch (error) {
        console.error('Error in workflow configurations fetch:', error);
        return [];
      }
    },
    'public',
    `created_by=eq.${user?.id}`
  );
  
  // Combined loading state
  const isLoading = executionsLoading || workflowsLoading;
  
  // Memoized chart data preparation for better performance
  const statusData = useMemo(() => {
    const statusCounts = {
      completed: 0,
      failed: 0,
      in_progress: 0
    };
    
    executions.forEach(execution => {
      statusCounts[execution.status]++;
    });
    
    return [
      { name: 'Completed', value: statusCounts.completed, color: '#10b981' },
      { name: 'Failed', value: statusCounts.failed, color: '#ef4444' },
      { name: 'In Progress', value: statusCounts.in_progress, color: '#3b82f6' }
    ];
  }, [executions]);
  
  const workflowData = useMemo(() => {
    const workflowCounts: Record<string, { name: string, count: number }> = {};
    
    executions.forEach(execution => {
      const workflowId = execution.workflow_id;
      const workflow = workflows.find(w => w.id === workflowId);
      const workflowName = workflow ? workflow.name : 'Unknown';
      
      if (!workflowCounts[workflowId]) {
        workflowCounts[workflowId] = { name: workflowName, count: 0 };
      }
      
      workflowCounts[workflowId].count++;
    });
    
    return Object.values(workflowCounts).sort((a, b) => b.count - a.count);
  }, [executions, workflows]);
  
  const timelineData = useMemo(() => {
    // Group executions by day
    const dailyCounts: Record<string, { date: string, completed: number, failed: number }> = {};
    
    executions.forEach(execution => {
      const date = new Date(execution.started_at).toISOString().split('T')[0];
      
      if (!dailyCounts[date]) {
        dailyCounts[date] = { date, completed: 0, failed: 0 };
      }
      
      if (execution.status === 'completed') {
        dailyCounts[date].completed++;
      } else if (execution.status === 'failed') {
        dailyCounts[date].failed++;
      }
    });
    
    // Convert to array and sort by date
    return Object.values(dailyCounts).sort((a, b) => a.date.localeCompare(b.date));
  }, [executions]);
  
  const { data: workflowUsageSummary = [], isLoading: usageLoading } = useWorkflowUsageSummary();
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'in_progress':
        return <Clock className="h-4 w-4 text-blue-500" />;
      default:
        return null;
    }
  };
  
  const calculateSuccessRate = () => {
    const total = executions.length;
    if (total === 0) return 0;
    
    const completed = executions.filter(e => e.status === 'completed').length;
    return Math.round((completed / total) * 100);
  };
  
  const calculateAverageExecutionTime = () => {
    const completedExecutions = executions.filter(e => 
      e.status === 'completed' && e.completed_at
    );
    
    if (completedExecutions.length === 0) return 'N/A';
    
    let totalTimeMs = 0;
    
    completedExecutions.forEach(execution => {
      const startTime = new Date(execution.started_at).getTime();
      const endTime = new Date(execution.completed_at!).getTime();
      totalTimeMs += endTime - startTime;
    });
    
    const averageTimeMs = totalTimeMs / completedExecutions.length;
    
    // Format as seconds
    return `${(averageTimeMs / 1000).toFixed(2)}s`;
  };
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Workflow Analytics</CardTitle>
        <div className="flex items-center gap-2">
          {isLoading && <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />}
          <span className="text-sm text-muted-foreground">Real-time updates</span>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : executions.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No workflow execution data available. Run some workflows to see analytics.
          </div>
        ) : (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex flex-col items-center">
                    <h3 className="text-lg font-medium mb-2">Total Executions</h3>
                    <p className="text-4xl font-bold">{executions.length}</p>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex flex-col items-center">
                    <h3 className="text-lg font-medium mb-2">Success Rate</h3>
                    <p className="text-4xl font-bold">{calculateSuccessRate()}%</p>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex flex-col items-center">
                    <h3 className="text-lg font-medium mb-2">Avg. Execution Time</h3>
                    <p className="text-4xl font-bold">{calculateAverageExecutionTime()}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="status">Status</TabsTrigger>
                <TabsTrigger value="workflows">Workflows</TabsTrigger>
                <TabsTrigger value="timeline">Timeline</TabsTrigger>
                <TabsTrigger value="usage">Usage Summary</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <PieChartIcon className="h-5 w-5" />
                        Execution Status
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <defs>
                              {PIE_GRADIENTS.map((colors, index) => (
                                <linearGradient
                                  key={`pie-gradient-${index}`}
                                  id={`pie-gradient-${index}`}
                                  x1="0" y1="0" x2="1" y2="1"
                                >
                                  <stop offset="0%" stopColor={colors[0]} stopOpacity={1} />
                                  <stop offset="100%" stopColor={colors[1]} stopOpacity={1} />
                                </linearGradient>
                              ))}
                            </defs>
                            <Pie
                              data={statusData}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              outerRadius={80}
                              fill="#8884d8"
                              dataKey="value"
                              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                            >
                              {statusData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={`url(#pie-gradient-${index % PIE_GRADIENTS.length})`} stroke="white" strokeWidth={2} />
                              ))}
                            </Pie>
                            <Tooltip content={CustomTooltip} />
                            <Legend />
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        Top Workflows
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={workflowData.slice(0, 5)}
                            layout="vertical"
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" horizontal={false} />
                            <XAxis type="number" axisLine={false} tickLine={false} style={{ fontSize: '12px', fontFamily: 'inherit' }} />
                            <YAxis dataKey="name" type="category" width={150} axisLine={false} tickLine={false} style={{ fontSize: '12px', fontFamily: 'inherit' }} />
                            <Tooltip content={CustomTooltip} cursor={{ fill: 'rgba(236, 237, 254, 0.4)' }} contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: '8px', border: '1px solid #e2e8f0', boxShadow: '0 2px 4px rgba(0,0,0,0.05)' }} />
                            <defs>
                              {BAR_GRADIENTS.map((colors, index) => (
                                <linearGradient
                                  key={`bar-gradient-${index}`}
                                  id={`bar-gradient-${index}`}
                                  x1="0" y1="0" x2="1" y2="0"
                                >
                                  <stop offset="0%" stopColor={colors[0]} stopOpacity={0.8} />
                                  <stop offset="100%" stopColor={colors[1]} stopOpacity={0.9} />
                                </linearGradient>
                              ))}
                            </defs>
                            <Bar dataKey="count" fill="#8884d8" radius={[0, 4, 4, 0]} >
                              {workflowData.slice(0, 5).map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={`url(#bar-gradient-${index % BAR_GRADIENTS.length})`} />
                              ))}
                            </Bar>
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Executions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {executions.slice(0, 5).map((execution) => {
                        const workflow = workflows.find(w => w.id === execution.workflow_id);
                        return (
                          <div key={execution.id} className="flex items-center justify-between p-3 border rounded-md">
                            <div className="flex items-center gap-2">
                              {getStatusIcon(execution.status)}
                              <span className="font-medium">{workflow?.name || 'Unknown Workflow'}</span>
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {new Date(execution.started_at).toLocaleString()}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="status">
                <Card>
                  <CardHeader>
                    <CardTitle>Execution Status Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <defs>
                            {PIE_GRADIENTS.map((colors, index) => (
                              <linearGradient
                                key={`pie-gradient-${index}`}
                                id={`pie-gradient-${index}`}
                                x1="0" y1="0" x2="1" y2="1"
                              >
                                <stop offset="0%" stopColor={colors[0]} stopOpacity={1} />
                                <stop offset="100%" stopColor={colors[1]} stopOpacity={1} />
                              </linearGradient>
                            ))}
                          </defs>
                          <Pie
                            data={statusData}
                            cx="50%"
                            cy="50%"
                            labelLine={true}
                            outerRadius={150}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, value, percent }) => `${name}: ${value} (${(percent * 100).toFixed(0)}%)`}
                          >
                            {statusData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={`url(#pie-gradient-${index % PIE_GRADIENTS.length})`} stroke="white" strokeWidth={2} />
                            ))}
                          </Pie>
                          <Tooltip content={CustomTooltip} />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="workflows">
                <Card>
                  <CardHeader>
                    <CardTitle>Workflow Execution Counts</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={workflowData}
                          layout="vertical"
                          margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" horizontal={false} />
                          <XAxis type="number" axisLine={false} tickLine={false} style={{ fontSize: '12px', fontFamily: 'inherit' }} />
                          <YAxis dataKey="name" type="category" width={150} axisLine={false} tickLine={false} style={{ fontSize: '12px', fontFamily: 'inherit' }} />
                          <Tooltip content={CustomTooltip} cursor={{ fill: 'rgba(236, 237, 254, 0.4)' }} contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: '8px', border: '1px solid #e2e8f0', boxShadow: '0 2px 4px rgba(0,0,0,0.05)' }} />
                          <defs>
                            {BAR_GRADIENTS.map((colors, index) => (
                              <linearGradient
                                key={`bar-gradient-${index}`}
                                id={`bar-gradient-${index}`}
                                x1="0" y1="0" x2="1" y2="0"
                              >
                                <stop offset="0%" stopColor={colors[0]} stopOpacity={0.8} />
                                <stop offset="100%" stopColor={colors[1]} stopOpacity={0.9} />
                              </linearGradient>
                            ))}
                          </defs>
                          <Bar dataKey="count" fill="#8884d8" radius={[0, 4, 4, 0]} >
                            {workflowData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={`url(#bar-gradient-${index % BAR_GRADIENTS.length})`} />
                            ))}
                          </Bar>
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="timeline">
                <Card>
                  <CardHeader>
                    <CardTitle>Execution Timeline</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={timelineData}
                          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" horizontal={false} />
                          <XAxis dataKey="date" axisLine={false} tickLine={false} style={{ fontSize: '12px', fontFamily: 'inherit' }} />
                          <YAxis axisLine={false} tickLine={false} style={{ fontSize: '12px', fontFamily: 'inherit' }} />
                          <Tooltip content={CustomTooltip} cursor={{ fill: 'rgba(236, 237, 254, 0.4)' }} contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: '8px', border: '1px solid #e2e8f0', boxShadow: '0 2px 4px rgba(0,0,0,0.05)' }} />
                          <Legend />
                          <Bar dataKey="completed" stackId="a" fill="#10b981" name="Completed" radius={[0, 4, 4, 0]} />
                          <Bar dataKey="failed" stackId="a" fill="#ef4444" name="Failed" radius={[0, 4, 4, 0]} />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="usage" className="space-y-4">
                <h3 className="text-lg font-semibold mb-2">Workflow Usage Summary</h3>
                {usageLoading ? <div>Loading...</div> : (
                  <ul>
                    {workflowUsageSummary.map((u: any, idx: number) => (
                      <li key={idx}>
                        {u.date}: {u.workflows_executed} executed, {u.workflows_created} created, {u.active_users} active users
                      </li>
                    ))}
                  </ul>
                )}
              </TabsContent>
            </Tabs>
          </div>
        )}
      </CardContent>
    </Card>
  );
}