import { use<PERSON><PERSON>back, useRef, useState } from "react";
import { toast } from "sonner";
import {
  ReactFlow,
  Background,
  Controls,
  ConnectionMode,
  Node,
  Edge,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";

import { WorkflowNode } from "./WorkflowNode";
import { WorkflowControls } from "./WorkflowControls";
import { WorkflowErrorBoundary } from "./WorkflowErrorBoundary";
import { WorkflowNodeConfig } from "./WorkflowNodeConfig";
import { WorkflowEdge } from "./edges/WorkflowEdge";
import { WorkflowMinimap } from "./components/WorkflowMinimap";
import { WorkflowContainer } from "./components/WorkflowContainer";
import { useWorkflowState } from "./hooks/useWorkflowState";
import { parseDroppedData } from "./WorkflowValidation";
import { SaveWorkflowDialog } from "./dialogs/SaveWorkflowDialog";
import { LoadWorkflowDialog } from "./dialogs/LoadWorkflowDialog";
import { WorkflowValidationOverlay } from "./WorkflowValidationOverlay";
import { WorkflowExecutionLogViewer } from "./WorkflowExecutionLogViewer";
import { supabase } from "@/integrations/supabase/client";
import { useCreateWorkflowConfiguration } from "@/hooks/useWorkflowConfigurations";
import { useUpdateWorkflowConfiguration } from "@/hooks/useWorkflowConfigurations";
import { useEffect } from "react";

const nodeTypes = {
  workflowNode: WorkflowNode,
};

const edgeTypes = {
  default: WorkflowEdge,
};

// Modified to include a counter for each node type
const nodeCounters: { [key: string]: number } = {};

const getUniqueNodeId = (nodeType: string) => {
  nodeCounters[nodeType] = (nodeCounters[nodeType] || 0) + 1;
  return `${nodeType}-${nodeCounters[nodeType]}`;
};

// Add proOptions to remove the attribution
const proOptions = { hideAttribution: true };

interface WorkflowCanvasProps {
  className?: string;
  initialConfig?: any;
  workflowId?: string | null;
  workflows?: any[];
}

export function WorkflowCanvas({ className, initialConfig, workflowId, workflows }: WorkflowCanvasProps) {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    onConnect,
    reactFlowInstance,
    setReactFlowInstance,
    isMinimapExpanded,
    setIsMinimapExpanded,
    selectedNode,
    setSelectedNode,
    setNodes,
    setEdges,
  } = useWorkflowState();
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [isLoadDialogOpen, setIsLoadDialogOpen] = useState(false);
  const [showValidation, setShowValidation] = useState(false);
  const [showExecutionLog, setShowExecutionLog] = useState(false);
  const [currentExecutionId, setCurrentExecutionId] = useState<string | null>(null);
  const createWorkflow = useCreateWorkflowConfiguration();
  const updateWorkflow = useUpdateWorkflowConfiguration();

  // Get workflows from props
  const workflowsFromProps = workflows || [];

  // Load initial configuration if provided
  useEffect(() => {
    if (initialConfig && initialConfig.nodes && initialConfig.edges && Array.isArray(initialConfig.nodes) && Array.isArray(initialConfig.edges)) {
      setNodes(initialConfig.nodes);
      setEdges(initialConfig.edges);

      // Reset node counters based on loaded nodes
      initialConfig.nodes.forEach((node: any) => {
        const originalId = node.data.originalId || node.data.id;
        const match = node.id.match(new RegExp(`${originalId}-(\\d+)`));
        if (match && match[1]) {
          const counter = parseInt(match[1]);
          if (!nodeCounters[originalId] || nodeCounters[originalId] < counter) {
            nodeCounters[originalId] = counter;
          }
        }
      });
    }
  }, [initialConfig, setNodes, setEdges]);

  const onDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      if (!reactFlowBounds || !reactFlowInstance) {
        console.error("Missing flow bounds or instance");
        return;
      }

      const rawData = event.dataTransfer.getData("application/reactflow");
      const nodeData = parseDroppedData(rawData);

      if (!nodeData) {
        return;
      }

      const { x: viewportX, y: viewportY, zoom } = reactFlowInstance.getViewport();

      const position = {
        x: (event.clientX - reactFlowBounds.left - viewportX) / zoom,
        y: (event.clientY - reactFlowBounds.top - viewportY) / zoom,
      };

      const newNode = {
        id: getUniqueNodeId(nodeData.id),
        type: "workflowNode",
        position,
        data: {
          ...nodeData,
          originalId: nodeData.id,
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes]
  );

  const onSave = useCallback(() => {
    if (!nodes.length) {
      toast.error("Add some nodes to save the workflow");
      return;
    }
    setIsSaveDialogOpen(true);
  }, [nodes]);

  const handleSaveWorkflow = useCallback(async (name: string, description?: string) => {
    try {
      if (workflowId) {
        // Find the workflow to update
        const workflowToUpdate = workflowsFromProps.find(w => w.id === workflowId);
        if (workflowToUpdate) {
          // Update existing workflow
          await updateWorkflow.mutateAsync({
            id: workflowId,
            name,
            description,
            config: { nodes, edges },
            is_active: true,
          });
        } else {
          console.error("Workflow not found for ID:", workflowId);
          toast.error("Workflow not found");
        }
      } else {
        // Create new workflow
        await createWorkflow.mutateAsync({
          name,
          description,
          config: { nodes, edges },
          is_active: true,
        });
      }
      toast.success("Workflow saved successfully");
      setIsSaveDialogOpen(false);
    } catch (error) {
      console.error("Error saving workflow:", error);
      toast.error("Failed to save workflow");
    }
  }, [nodes, edges, createWorkflow, updateWorkflow, workflowId]);

  const onRun = useCallback(() => {
    // Generate a new execution ID for this run
    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    setCurrentExecutionId(executionId);
    setShowExecutionLog(true);
    toast.success("Workflow started successfully");
  }, []);

  const onNodeClick = useCallback((_: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
  }, [setSelectedNode]);

  const handleLoadWorkflow = useCallback((config: { nodes: Node[]; edges: Edge[] }) => {
    if (!config.nodes || !config.edges) {
      toast.error("Invalid workflow configuration");
      return;
    }

    setNodes(config.nodes);
    setEdges(config.edges);
    toast.success("Workflow loaded successfully");
  }, [setNodes, setEdges]);

  return (
    <WorkflowContainer
      className={className}
      onDragOver={onDragOver}
      onDrop={onDrop}
      ref={reactFlowWrapper}
    >
      <WorkflowErrorBoundary>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onInit={setReactFlowInstance}
          onNodeClick={onNodeClick}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          connectionMode={ConnectionMode.Strict}
          snapToGrid={true}
          snapGrid={[15, 15]}
          fitView
          proOptions={proOptions}
          deleteKeyCode={['Backspace', 'Delete']}
        >
          <Background />
          <Controls />
          <WorkflowMinimap
            isExpanded={isMinimapExpanded}
            onMouseEnter={() => setIsMinimapExpanded(true)}
            onMouseLeave={() => setIsMinimapExpanded(false)}
          />
          <WorkflowControls
            onClear={() => setNodes([])}
            onSave={onSave}
            onRun={onRun}
            hasNodes={nodes.length > 0}
            nodes={nodes}
            edges={edges}
            onLoad={handleLoadWorkflow}
            isEditing={!!workflowId}
            onToggleValidation={() => setShowValidation(!showValidation)}
            onToggleExecutionLog={() => setShowExecutionLog(!showExecutionLog)}
            showValidation={showValidation}
            showExecutionLog={showExecutionLog}
          />
        </ReactFlow>
      </WorkflowErrorBoundary>
      {selectedNode && (
        <WorkflowNodeConfig
          node={selectedNode}
          onClose={() => setSelectedNode(null)}
          onUpdate={(nodeId, newData) => {
            setNodes((nds) =>
              nds.map((node) => {
                if (node.id === nodeId) {
                  return {
                    ...node,
                    data: newData,
                  };
                }
                return node;
              })
            );
          }}
        />
      )}
      <SaveWorkflowDialog
        isOpen={isSaveDialogOpen}
        onClose={() => setIsSaveDialogOpen(false)}
        onSave={handleSaveWorkflow}
        isLoading={createWorkflow.isPending}
        initialData={workflowId ? { 
          name: workflowsFromProps.find(w => w.id === workflowId)?.name || '',
          description: workflowsFromProps.find(w => w.id === workflowId)?.description || ''
        } : undefined}
      />
      <LoadWorkflowDialog
        isOpen={isLoadDialogOpen}
        onClose={() => setIsLoadDialogOpen(false)}
        onLoad={handleLoadWorkflow}
      />
      <WorkflowValidationOverlay
        nodes={nodes}
        edges={edges}
        isVisible={showValidation}
        onClose={() => setShowValidation(false)}
      />
      <WorkflowExecutionLogViewer
        workflowId={workflowId}
        executionId={currentExecutionId}
        isVisible={showExecutionLog}
        onClose={() => setShowExecutionLog(false)}
      />
    </WorkflowContainer>
  );
}
