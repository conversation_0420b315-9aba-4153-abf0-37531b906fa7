import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { 
  WorkflowConfigProps, 
  UpdateStatusConfig, 
  AddToPoolConfig, 
  SendMessageConfig, 
  ExportDataConfig, 
  ArchiveCandidateConfig, 
  GenerateReportConfig 
} from "@/types/workflow";

export function UpdateStatusConfig({ config, setConfig }: WorkflowConfigProps<UpdateStatusConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>New Status</Label>
        <Select
          value={config.newStatus}
          onValueChange={(value: UpdateStatusConfig['newStatus']) => setConfig({ ...config, newStatus: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select new status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="screening">Screening</SelectItem>
            <SelectItem value="interview">Interview</SelectItem>
            <SelectItem value="technical">Technical Assessment</SelectItem>
            <SelectItem value="reference">Reference Check</SelectItem>
            <SelectItem value="offer">Offer</SelectItem>
            <SelectItem value="hired">Hired</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
            <SelectItem value="on_hold">On Hold</SelectItem>
            <SelectItem value="withdrawn">Withdrawn</SelectItem>
            <SelectItem value="custom">Custom Status</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {config.newStatus === 'custom' && (
        <div className="space-y-2">
          <Label>Custom Status</Label>
          <Input
            placeholder="Enter custom status"
            value={config.customStatus}
            onChange={(e) => setConfig({ ...config, customStatus: e.target.value })}
          />
        </div>
      )}
      
      <div className="space-y-2">
        <Label>Status Note</Label>
        <Textarea
          placeholder="Enter status update note"
          value={config.statusNote}
          onChange={(e) => setConfig({ ...config, statusNote: e.target.value })}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label htmlFor="notify-candidate">Notify Candidate</Label>
        <Switch
          id="notify-candidate"
          checked={config.notifyCandidate}
          onCheckedChange={(checked) => setConfig({ ...config, notifyCandidate: checked })}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label htmlFor="notify-team">Notify Team</Label>
        <Switch
          id="notify-team"
          checked={config.notifyTeam}
          onCheckedChange={(checked) => setConfig({ ...config, notifyTeam: checked })}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label htmlFor="log-activity">Log Activity</Label>
        <Switch
          id="log-activity"
          checked={config.logActivity}
          onCheckedChange={(checked) => setConfig({ ...config, logActivity: checked })}
        />
      </div>
    </>
  );
}

export function AddToPoolConfig({ config, setConfig }: WorkflowConfigProps<AddToPoolConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Talent Pool</Label>
        <Select
          value={config.poolName}
          onValueChange={(value: AddToPoolConfig['poolName']) => setConfig({ ...config, poolName: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select talent pool" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="engineering">Engineering</SelectItem>
            <SelectItem value="design">Design</SelectItem>
            <SelectItem value="marketing">Marketing</SelectItem>
            <SelectItem value="sales">Sales</SelectItem>
            <SelectItem value="product">Product</SelectItem>
            <SelectItem value="management">Management</SelectItem>
            <SelectItem value="future">Future Consideration</SelectItem>
            <SelectItem value="custom">Custom Pool</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {config.poolName === 'custom' && (
        <div className="space-y-2">
          <Label>Custom Pool Name</Label>
          <Input
            placeholder="Enter custom pool name"
            value={config.customPoolName}
            onChange={(e) => setConfig({ ...config, customPoolName: e.target.value })}
          />
        </div>
      )}
      
      <div className="space-y-2">
        <Label>Tags</Label>
        <Input
          placeholder="Enter tags (comma-separated)"
          value={config.tags}
          onChange={(e) => setConfig({ ...config, tags: e.target.value })}
        />
      </div>
      
      <div className="space-y-2">
        <Label>Follow-up Date</Label>
        <Input
          type="date"
          value={config.followUpDate}
          onChange={(e) => setConfig({ ...config, followUpDate: e.target.value })}
        />
      </div>
      
      <div className="space-y-2">
        <Label>Notes</Label>
        <Textarea
          placeholder="Enter notes about this candidate"
          value={config.notes}
          onChange={(e) => setConfig({ ...config, notes: e.target.value })}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label htmlFor="notify-candidate">Notify Candidate</Label>
        <Switch
          id="notify-candidate"
          checked={config.notifyCandidate}
          onCheckedChange={(checked) => setConfig({ ...config, notifyCandidate: checked })}
        />
      </div>
    </>
  );
}

export function SendMessageConfig({ config, setConfig }: WorkflowConfigProps<SendMessageConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Message Template</Label>
        <Select
          value={config.template}
          onValueChange={(value: SendMessageConfig['template']) => setConfig({ ...config, template: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select message template" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="follow-up">Follow-up</SelectItem>
            <SelectItem value="status-update">Status Update</SelectItem>
            <SelectItem value="interview-prep">Interview Prep</SelectItem>
            <SelectItem value="offer-details">Offer Details</SelectItem>
            <SelectItem value="rejection">Rejection</SelectItem>
            <SelectItem value="onboarding">Onboarding</SelectItem>
            <SelectItem value="custom">Custom Template</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label>Message Content</Label>
        <Textarea
          placeholder="Enter message content"
          value={config.messageContent}
          onChange={(e) => setConfig({ ...config, messageContent: e.target.value })}
        />
      </div>
      
      <div className="space-y-2">
        <Label>Channel</Label>
        <Select
          value={config.channel}
          onValueChange={(value: SendMessageConfig['channel']) => setConfig({ ...config, channel: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select message channel" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="email">Email</SelectItem>
            <SelectItem value="sms">SMS</SelectItem>
            <SelectItem value="in-app">In-App Message</SelectItem>
            <SelectItem value="whatsapp">WhatsApp</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="flex items-center justify-between">
        <Label htmlFor="send-immediately">Send Immediately</Label>
        <Switch
          id="send-immediately"
          checked={config.sendImmediately}
          onCheckedChange={(checked) => setConfig({ ...config, sendImmediately: checked })}
        />
      </div>
      
      {!config.sendImmediately && (
        <div className="space-y-2">
          <Label>Schedule Date</Label>
          <Input
            type="date"
            value={config.scheduleDate}
            onChange={(e) => setConfig({ ...config, scheduleDate: e.target.value })}
          />
        </div>
      )}
      
      <div className="flex items-center justify-between">
        <Label htmlFor="track-opens">Track Opens/Clicks</Label>
        <Switch
          id="track-opens"
          checked={config.trackOpens}
          onCheckedChange={(checked) => setConfig({ ...config, trackOpens: checked })}
        />
      </div>
    </>
  );
}

export function ExportDataConfig({ config, setConfig }: WorkflowConfigProps<ExportDataConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Export Format</Label>
        <Select
          value={config.format}
          onValueChange={(value: ExportDataConfig['format']) => setConfig({ ...config, format: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select export format" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="csv">CSV</SelectItem>
            <SelectItem value="json">JSON</SelectItem>
            <SelectItem value="xlsx">Excel</SelectItem>
            <SelectItem value="pdf">PDF</SelectItem>
            <SelectItem value="xml">XML</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label>Data to Export</Label>
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <Switch
              id="export-basic"
              checked={config.exportBasic}
              onCheckedChange={(checked) => setConfig({ ...config, exportBasic: checked })}
            />
            <Label htmlFor="export-basic">Basic Information</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="export-contact"
              checked={config.exportContact}
              onCheckedChange={(checked) => setConfig({ ...config, exportContact: checked })}
            />
            <Label htmlFor="export-contact">Contact Details</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="export-skills"
              checked={config.exportSkills}
              onCheckedChange={(checked) => setConfig({ ...config, exportSkills: checked })}
            />
            <Label htmlFor="export-skills">Skills & Experience</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="export-history"
              checked={config.exportHistory}
              onCheckedChange={(checked) => setConfig({ ...config, exportHistory: checked })}
            />
            <Label htmlFor="export-history">Application History</Label>
          </div>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label>Export Destination</Label>
        <Select
          value={config.destination}
          onValueChange={(value: ExportDataConfig['destination']) => setConfig({ ...config, destination: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select export destination" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="download">Download</SelectItem>
            <SelectItem value="email">Email</SelectItem>
            <SelectItem value="storage">Cloud Storage</SelectItem>
            <SelectItem value="sftp">SFTP Server</SelectItem>
            <SelectItem value="api">External API</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {config.destination === 'email' && (
        <div className="space-y-2">
          <Label>Email Recipients</Label>
          <Input
            placeholder="Enter email addresses (comma-separated)"
            value={config.emailRecipients}
            onChange={(e) => setConfig({ ...config, emailRecipients: e.target.value })}
          />
        </div>
      )}
      
      {config.destination === 'api' && (
        <div className="space-y-2">
          <Label>API Endpoint</Label>
          <Input
            placeholder="Enter API endpoint URL"
            value={config.apiEndpoint}
            onChange={(e) => setConfig({ ...config, apiEndpoint: e.target.value })}
          />
        </div>
      )}
      
      <div className="flex items-center justify-between">
        <Label htmlFor="include-headers">Include Headers/Column Names</Label>
        <Switch
          id="include-headers"
          checked={config.includeHeaders}
          onCheckedChange={(checked) => setConfig({ ...config, includeHeaders: checked })}
        />
      </div>
    </>
  );
}

export function ArchiveCandidateConfig({ config, setConfig }: WorkflowConfigProps<ArchiveCandidateConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Archive Reason</Label>
        <Select
          value={config.reason}
          onValueChange={(value: ArchiveCandidateConfig['reason']) => setConfig({ ...config, reason: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select archive reason" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="hired">Hired</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
            <SelectItem value="withdrawn">Withdrawn</SelectItem>
            <SelectItem value="duplicate">Duplicate</SelectItem>
            <SelectItem value="not-qualified">Not Qualified</SelectItem>
            <SelectItem value="position-filled">Position Filled</SelectItem>
            <SelectItem value="custom">Custom Reason</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {config.reason === 'custom' && (
        <div className="space-y-2">
          <Label>Custom Reason</Label>
          <Input
            placeholder="Enter custom archive reason"
            value={config.customReason}
            onChange={(e) => setConfig({ ...config, customReason: e.target.value })}
          />
        </div>
      )}
      
      <div className="space-y-2">
        <Label>Archive Note</Label>
        <Textarea
          placeholder="Enter archive note"
          value={config.note}
          onChange={(e) => setConfig({ ...config, note: e.target.value })}
        />
      </div>
      
      <div className="space-y-2">
        <Label>Retention Period</Label>
        <Select
          value={config.retentionPeriod}
          onValueChange={(value: ArchiveCandidateConfig['retentionPeriod']) => setConfig({ ...config, retentionPeriod: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select retention period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="30d">30 Days</SelectItem>
            <SelectItem value="90d">90 Days</SelectItem>
            <SelectItem value="6m">6 Months</SelectItem>
            <SelectItem value="1y">1 Year</SelectItem>
            <SelectItem value="2y">2 Years</SelectItem>
            <SelectItem value="permanent">Permanent</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="flex items-center justify-between">
        <Label htmlFor="notify-candidate">Notify Candidate</Label>
        <Switch
          id="notify-candidate"
          checked={config.notifyCandidate}
          onCheckedChange={(checked) => setConfig({ ...config, notifyCandidate: checked })}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label htmlFor="archive-documents">Archive Related Documents</Label>
        <Switch
          id="archive-documents"
          checked={config.archiveDocuments}
          onCheckedChange={(checked) => setConfig({ ...config, archiveDocuments: checked })}
        />
      </div>
    </>
  );
}

export function GenerateReportConfig({ config, setConfig }: WorkflowConfigProps<GenerateReportConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Report Type</Label>
        <Select
          value={config.reportType}
          onValueChange={(value: GenerateReportConfig['reportType']) => setConfig({ ...config, reportType: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select report type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="candidate">Candidate Summary</SelectItem>
            <SelectItem value="interview">Interview Feedback</SelectItem>
            <SelectItem value="assessment">Assessment Results</SelectItem>
            <SelectItem value="hiring">Hiring Process</SelectItem>
            <SelectItem value="custom">Custom Report</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {config.reportType === 'custom' && (
        <div className="space-y-2">
          <Label>Custom Report Name</Label>
          <Input
            placeholder="Enter custom report name"
            value={config.customReportName}
            onChange={(e) => setConfig({ ...config, customReportName: e.target.value })}
          />
        </div>
      )}
      
      <div className="space-y-2">
        <Label>Report Format</Label>
        <Select
          value={config.format}
          onValueChange={(value: GenerateReportConfig['format']) => setConfig({ ...config, format: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select report format" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="pdf">PDF</SelectItem>
            <SelectItem value="docx">Word Document</SelectItem>
            <SelectItem value="html">HTML</SelectItem>
            <SelectItem value="text">Plain Text</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label>Report Sections</Label>
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <Switch
              id="include-summary"
              checked={config.includeSummary}
              onCheckedChange={(checked) => setConfig({ ...config, includeSummary: checked })}
            />
            <Label htmlFor="include-summary">Executive Summary</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="include-details"
              checked={config.includeDetails}
              onCheckedChange={(checked) => setConfig({ ...config, includeDetails: checked })}
            />
            <Label htmlFor="include-details">Detailed Information</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="include-timeline"
              checked={config.includeTimeline}
              onCheckedChange={(checked) => setConfig({ ...config, includeTimeline: checked })}
            />
            <Label htmlFor="include-timeline">Timeline</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="include-recommendations"
              checked={config.includeRecommendations}
              onCheckedChange={(checked) => setConfig({ ...config, includeRecommendations: checked })}
            />
            <Label htmlFor="include-recommendations">Recommendations</Label>
          </div>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label>Distribution</Label>
        <Select
          value={config.distribution}
          onValueChange={(value: GenerateReportConfig['distribution']) => setConfig({ ...config, distribution: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select distribution method" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="email">Email</SelectItem>
            <SelectItem value="download">Download</SelectItem>
            <SelectItem value="store">Store in System</SelectItem>
            <SelectItem value="share">Share Link</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {config.distribution === 'email' && (
        <div className="space-y-2">
          <Label>Email Recipients</Label>
          <Input
            placeholder="Enter email addresses (comma-separated)"
            value={config.emailRecipients}
            onChange={(e) => setConfig({ ...config, emailRecipients: e.target.value })}
          />
        </div>
      )}
    </>
  );
}