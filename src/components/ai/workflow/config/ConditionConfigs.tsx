
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export function ExperienceCheckConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Minimum Years</Label>
        <Input
          type="number"
          min="0"
          max="30"
          value={config?.minYears || ""}
          onChange={(e) => setConfig({...config, minYears: e.target.value})}
        />
      </div>
      <div className="space-y-2">
        <Label>Required Industry</Label>
        <Select
          value={config?.industry || "any"}
          onValueChange={(value) => setConfig({...config, industry: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select industry" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Industry</SelectItem>
            <SelectItem value="tech">Technology</SelectItem>
            <SelectItem value="finance">Finance</SelectItem>
            <SelectItem value="healthcare">Healthcare</SelectItem>
            <SelectItem value="education">Education</SelectItem>
            <SelectItem value="retail">Retail</SelectItem>
            <SelectItem value="manufacturing">Manufacturing</SelectItem>
            <SelectItem value="consulting">Consulting</SelectItem>
            <SelectItem value="custom">Custom Industry</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {(config?.industry === 'custom') && (
        <div className="space-y-2">
          <Label>Custom Industry</Label>
          <Input
            placeholder="Enter industry name"
            value={config?.customIndustry || ""}
            onChange={(e) => setConfig({...config, customIndustry: e.target.value})}
          />
        </div>
      )}
      
      <div className="space-y-2">
        <Label>Experience Type</Label>
        <Select
          value={config?.experienceType || "any"}
          onValueChange={(value) => setConfig({...config, experienceType: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select experience type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Experience</SelectItem>
            <SelectItem value="full-time">Full-time Only</SelectItem>
            <SelectItem value="management">Management Experience</SelectItem>
            <SelectItem value="technical">Technical Experience</SelectItem>
            <SelectItem value="leadership">Leadership Experience</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Consider Internships</Label>
        <Switch
          checked={config?.considerInternships || false}
          onCheckedChange={(checked) => setConfig({...config, considerInternships: checked})}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Consider Freelance</Label>
        <Switch
          checked={config?.considerFreelance || false}
          onCheckedChange={(checked) => setConfig({...config, considerFreelance: checked})}
        />
      </div>
    </>
  );
}

export function EducationCheckConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Minimum Degree</Label>
        <Select
          value={config?.minDegree || "none"}
          onValueChange={(value) => setConfig({...config, minDegree: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select minimum degree" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="high-school">High School</SelectItem>
            <SelectItem value="associates">Associate's</SelectItem>
            <SelectItem value="bachelors">Bachelor's</SelectItem>
            <SelectItem value="masters">Master's</SelectItem>
            <SelectItem value="phd">Ph.D.</SelectItem>
            <SelectItem value="none">No Specific Requirement</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label>Field of Study</Label>
        <Select
          value={config?.fieldOfStudy || "any"}
          onValueChange={(value) => setConfig({...config, fieldOfStudy: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select field of study" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Field</SelectItem>
            <SelectItem value="cs">Computer Science</SelectItem>
            <SelectItem value="engineering">Engineering</SelectItem>
            <SelectItem value="business">Business</SelectItem>
            <SelectItem value="design">Design</SelectItem>
            <SelectItem value="science">Science</SelectItem>
            <SelectItem value="arts">Arts & Humanities</SelectItem>
            <SelectItem value="custom">Custom Field</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {(config?.fieldOfStudy === 'custom') && (
        <div className="space-y-2">
          <Label>Custom Field of Study</Label>
          <Input
            placeholder="Enter field of study"
            value={config?.customField || ""}
            onChange={(e) => setConfig({...config, customField: e.target.value})}
          />
        </div>
      )}
      
      <div className="space-y-2">
        <Label>Institution Tier</Label>
        <Select
          value={config?.institutionTier || "any"}
          onValueChange={(value) => setConfig({...config, institutionTier: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select institution tier" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Institution</SelectItem>
            <SelectItem value="top">Top Tier</SelectItem>
            <SelectItem value="accredited">Accredited</SelectItem>
            <SelectItem value="specific">Specific Institutions</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {(config?.institutionTier === 'specific') && (
        <div className="space-y-2">
          <Label>Specific Institutions</Label>
          <Textarea
            placeholder="Enter institution names (one per line)"
            value={config?.specificInstitutions || ""}
            onChange={(e) => setConfig({...config, specificInstitutions: e.target.value})}
          />
        </div>
      )}
      
      <div className="flex items-center justify-between">
        <Label>Consider Equivalent Experience</Label>
        <Switch
          checked={config?.considerEquivalentExp || false}
          onCheckedChange={(checked) => setConfig({...config, considerEquivalentExp: checked})}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Require Degree Completion</Label>
        <Switch
          checked={config?.requireCompletion || false}
          onCheckedChange={(checked) => setConfig({...config, requireCompletion: checked})}
        />
      </div>
    </>
  );
}

export function SkillsMatchConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Required Skills</Label>
        <Textarea
          placeholder="Enter required skills (comma-separated)"
          value={config?.requiredSkills || ""}
          onChange={(e) => setConfig({...config, requiredSkills: e.target.value})}
        />
      </div>
      <div className="space-y-2">
        <Label>Minimum Match Percentage</Label>
        <Input
          type="number"
          min="0"
          max="100"
          value={config?.minMatchPercentage || ""}
          onChange={(e) => setConfig({...config, minMatchPercentage: e.target.value})}
        />
      </div>
      <div className="space-y-2">
        <Label>Skill Proficiency Level</Label>
        <Select
          value={config?.proficiencyLevel || "any"}
          onValueChange={(value) => setConfig({...config, proficiencyLevel: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select minimum proficiency" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Level</SelectItem>
            <SelectItem value="beginner">Beginner</SelectItem>
            <SelectItem value="intermediate">Intermediate</SelectItem>
            <SelectItem value="advanced">Advanced</SelectItem>
            <SelectItem value="expert">Expert</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label>Skill Weighting</Label>
        <Select
          value={config?.skillWeighting || "equal"}
          onValueChange={(value) => setConfig({...config, skillWeighting: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select skill weighting" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="equal">Equal Weighting</SelectItem>
            <SelectItem value="primary">Primary Skills Higher</SelectItem>
            <SelectItem value="experience">Experience-Based</SelectItem>
            <SelectItem value="custom">Custom Weighting</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="flex items-center justify-between">
        <Label>Consider Similar Skills</Label>
        <Switch
          checked={config?.considerSimilarSkills || false}
          onCheckedChange={(checked) => setConfig({...config, considerSimilarSkills: checked})}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label>Require All Skills</Label>
        <Switch
          checked={config?.requireAllSkills || false}
          onCheckedChange={(checked) => setConfig({...config, requireAllSkills: checked})}
        />
      </div>
    </>
  );
}

export function LocationCheckConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Location Type</Label>
        <Select
          value={config?.locationType || "onsite"}
          onValueChange={(value) => setConfig({...config, locationType: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select location type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="onsite">On-site</SelectItem>
            <SelectItem value="hybrid">Hybrid</SelectItem>
            <SelectItem value="remote">Remote</SelectItem>
            <SelectItem value="flexible">Flexible</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label>Required Location</Label>
        <Input
          placeholder="Enter city or region"
          value={config?.requiredLocation || ""}
          onChange={(e) => setConfig({...config, requiredLocation: e.target.value})}
        />
      </div>
      <div className="space-y-2">
        <Label>Maximum Distance (miles)</Label>
        <Input
          type="number"
          min="0"
          max="1000"
          value={config?.maxDistance || ""}
          onChange={(e) => setConfig({...config, maxDistance: e.target.value})}
        />
      </div>
      <div className="space-y-2">
        <Label>Relocation</Label>
        <Select
          value={config?.relocation || "not-required"}
          onValueChange={(value) => setConfig({...config, relocation: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select relocation option" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="required">Required</SelectItem>
            <SelectItem value="preferred">Preferred</SelectItem>
            <SelectItem value="not-required">Not Required</SelectItem>
            <SelectItem value="no-relocation">No Relocation</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="flex items-center justify-between">
        <Label>Consider Remote Work</Label>
        <Switch
          checked={config?.considerRemote || false}
          onCheckedChange={(checked) => setConfig({...config, considerRemote: checked})}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label>Visa Sponsorship Available</Label>
        <Switch
          checked={config?.visaSponsorship || false}
          onCheckedChange={(checked) => setConfig({...config, visaSponsorship: checked})}
        />
      </div>
    </>
  );
}

export function SalaryCheckConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Minimum Salary</Label>
        <Input
          type="number"
          min="0"
          value={config?.minSalary || ""}
          onChange={(e) => setConfig({...config, minSalary: e.target.value})}
        />
      </div>
      <div className="space-y-2">
        <Label>Maximum Salary</Label>
        <Input
          type="number"
          min="0"
          value={config?.maxSalary || ""}
          onChange={(e) => setConfig({...config, maxSalary: e.target.value})}
        />
      </div>
      <div className="space-y-2">
        <Label>Currency</Label>
        <Select
          value={config?.currency || "USD"}
          onValueChange={(value) => setConfig({...config, currency: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select currency" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="USD">USD ($)</SelectItem>
            <SelectItem value="EUR">EUR (€)</SelectItem>
            <SelectItem value="GBP">GBP (£)</SelectItem>
            <SelectItem value="CAD">CAD ($)</SelectItem>
            <SelectItem value="AUD">AUD ($)</SelectItem>
            <SelectItem value="JPY">JPY (¥)</SelectItem>
            <SelectItem value="custom">Other Currency</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {(config?.currency === 'custom') && (
        <div className="space-y-2">
          <Label>Custom Currency</Label>
          <Input
            placeholder="Enter currency code (e.g., INR)"
            value={config?.customCurrency || ""}
            onChange={(e) => setConfig({...config, customCurrency: e.target.value})}
          />
        </div>
      )}
      
      <div className="space-y-2">
        <Label>Salary Period</Label>
        <Select
          value={config?.period || "annual"}
          onValueChange={(value) => setConfig({...config, period: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select salary period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="annual">Annual</SelectItem>
            <SelectItem value="monthly">Monthly</SelectItem>
            <SelectItem value="weekly">Weekly</SelectItem>
            <SelectItem value="hourly">Hourly</SelectItem>
            <SelectItem value="contract">Contract</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Include Benefits Value</Label>
        <Switch
          checked={config?.includeBenefits || false}
          onCheckedChange={(checked) => setConfig({...config, includeBenefits: checked})}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Negotiate Outside Range</Label>
        <Switch
          checked={config?.allowNegotiation || false}
          onCheckedChange={(checked) => setConfig({...config, allowNegotiation: checked})}
        />
      </div>
    </>
  );
}

export function AvailabilityCheckConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Availability Type</Label>
        <Select
          value={config?.availabilityType || "immediate"}
          onValueChange={(value) => setConfig({...config, availabilityType: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select availability type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="immediate">Immediate</SelectItem>
            <SelectItem value="notice-period">Notice Period</SelectItem>
            <SelectItem value="specific-date">Specific Date</SelectItem>
            <SelectItem value="flexible">Flexible</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {(config?.availabilityType === 'notice-period') && (
        <div className="space-y-2">
          <Label>Maximum Notice Period (days)</Label>
          <Input
            type="number"
            min="0"
            max="180"
            value={config?.maxNoticePeriod || ""}
            onChange={(e) => setConfig({...config, maxNoticePeriod: e.target.value})}
          />
        </div>
      )}
      
      {(config?.availabilityType === 'specific-date') && (
        <div className="space-y-2">
          <Label>Latest Start Date</Label>
          <Input
            type="date"
            value={config?.latestStartDate || ""}
            onChange={(e) => setConfig({...config, latestStartDate: e.target.value})}
          />
        </div>
      )}
      
      <div className="space-y-2">
        <Label>Work Schedule</Label>
        <Select
          value={config?.workSchedule || "any"}
          onValueChange={(value) => setConfig({...config, workSchedule: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select work schedule" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Schedule</SelectItem>
            <SelectItem value="full-time">Full-time</SelectItem>
            <SelectItem value="part-time">Part-time</SelectItem>
            <SelectItem value="contract">Contract</SelectItem>
            <SelectItem value="flexible">Flexible Hours</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label>Time Zone Requirements</Label>
        <Select
          value={config?.timeZone || "any"}
          onValueChange={(value) => setConfig({...config, timeZone: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select time zone requirement" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Time Zone</SelectItem>
            <SelectItem value="overlap">Minimum Overlap</SelectItem>
            <SelectItem value="specific">Specific Time Zone</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {(config?.timeZone === 'overlap') && (
        <div className="space-y-2">
          <Label>Minimum Overlap Hours</Label>
          <Input
            type="number"
            min="1"
            max="24"
            value={config?.minOverlapHours || ""}
            onChange={(e) => setConfig({...config, minOverlapHours: e.target.value})}
          />
        </div>
      )}
      
      {(config?.timeZone === 'specific') && (
        <div className="space-y-2">
          <Label>Specific Time Zone</Label>
          <Input
            placeholder="Enter time zone (e.g., UTC-5)"
            value={config?.specificTimeZone || ""}
            onChange={(e) => setConfig({...config, specificTimeZone: e.target.value})}
          />
        </div>
      )}
      
      <div className="flex items-center justify-between">
        <Label>Require Travel</Label>
        <Switch
          checked={config?.requireTravel || false}
          onCheckedChange={(checked) => setConfig({...config, requireTravel: checked})}
        />
      </div>
    </>
  );
}

export function VisaCheckConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Work Authorization</Label>
        <Select
          value={config?.workAuthorization || "any"}
          onValueChange={(value) => setConfig({...config, workAuthorization: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select work authorization" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Authorization</SelectItem>
            <SelectItem value="citizen">Citizen/Permanent Resident</SelectItem>
            <SelectItem value="authorized">Already Authorized</SelectItem>
            <SelectItem value="sponsorship">Requires Sponsorship</SelectItem>
            <SelectItem value="specific">Specific Visa Type</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {(config?.workAuthorization === 'specific') && (
        <div className="space-y-2">
          <Label>Specific Visa Types</Label>
          <Textarea
            placeholder="Enter visa types (comma-separated)"
            value={config?.specificVisaTypes || ""}
            onChange={(e) => setConfig({...config, specificVisaTypes: e.target.value})}
          />
        </div>
      )}
      
      <div className="space-y-2">
        <Label>Country</Label>
        <Input
          placeholder="Enter country"
          value={config?.country || ""}
          onChange={(e) => setConfig({...config, country: e.target.value})}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Sponsorship Available</Label>
        <Switch
          checked={config?.sponsorshipAvailable || false}
          onCheckedChange={(checked) => setConfig({...config, sponsorshipAvailable: checked})}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Require Background Check</Label>
        <Switch
          checked={config?.requireBackgroundCheck || false}
          onCheckedChange={(checked) => setConfig({...config, requireBackgroundCheck: checked})}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Require Security Clearance</Label>
        <Switch
          checked={config?.requireSecurityClearance || false}
          onCheckedChange={(checked) => setConfig({...config, requireSecurityClearance: checked})}
        />
      </div>
    </>
  );
}

export function FilterConditionConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Filter Type</Label>
        <Select
          value={config?.filterType || "simple"}
          onValueChange={(value) => setConfig({...config, filterType: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select filter type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="simple">Simple Condition</SelectItem>
            <SelectItem value="advanced">Advanced Condition</SelectItem>
            <SelectItem value="custom">Custom Expression</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {(config?.filterType === 'simple') && (
        <>
          <div className="space-y-2">
            <Label>Field</Label>
            <Input
              placeholder="Enter field name"
              value={config?.field || ""}
              onChange={(e) => setConfig({...config, field: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label>Operator</Label>
            <Select
              value={config?.operator || "equals"}
              onValueChange={(value) => setConfig({...config, operator: value})}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select operator" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="equals">Equals</SelectItem>
                <SelectItem value="not-equals">Not Equals</SelectItem>
                <SelectItem value="contains">Contains</SelectItem>
                <SelectItem value="greater-than">Greater Than</SelectItem>
                <SelectItem value="less-than">Less Than</SelectItem>
                <SelectItem value="in">In List</SelectItem>
                <SelectItem value="not-in">Not In List</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>Value</Label>
            <Input
              placeholder="Enter value"
              value={config?.value || ""}
              onChange={(e) => setConfig({...config, value: e.target.value})}
            />
          </div>
        </>
      )}
      
      {(config?.filterType === 'advanced') && (
        <>
          <div className="space-y-2">
            <Label>Condition 1</Label>
            <div className="grid grid-cols-3 gap-2">
              <Input
                placeholder="Field"
                value={config?.field1 || ""}
                onChange={(e) => setConfig({...config, field1: e.target.value})}
              />
              <Select
                value={config?.operator1 || "equals"}
                onValueChange={(value) => setConfig({...config, operator1: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Operator" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="equals">Equals</SelectItem>
                  <SelectItem value="not-equals">Not Equals</SelectItem>
                  <SelectItem value="contains">Contains</SelectItem>
                  <SelectItem value="greater-than">Greater Than</SelectItem>
                  <SelectItem value="less-than">Less Than</SelectItem>
                </SelectContent>
              </Select>
              <Input
                placeholder="Value"
                value={config?.value1 || ""}
                onChange={(e) => setConfig({...config, value1: e.target.value})}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label>Logic Operator</Label>
            <Select
              value={config?.logicOperator || "and"}
              onValueChange={(value) => setConfig({...config, logicOperator: value})}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select logic operator" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="and">AND</SelectItem>
                <SelectItem value="or">OR</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>Condition 2</Label>
            <div className="grid grid-cols-3 gap-2">
              <Input
                placeholder="Field"
                value={config?.field2 || ""}
                onChange={(e) => setConfig({...config, field2: e.target.value})}
              />
              <Select
                value={config?.operator2 || "equals"}
                onValueChange={(value) => setConfig({...config, operator2: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Operator" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="equals">Equals</SelectItem>
                  <SelectItem value="not-equals">Not Equals</SelectItem>
                  <SelectItem value="contains">Contains</SelectItem>
                  <SelectItem value="greater-than">Greater Than</SelectItem>
                  <SelectItem value="less-than">Less Than</SelectItem>
                </SelectContent>
              </Select>
              <Input
                placeholder="Value"
                value={config?.value2 || ""}
                onChange={(e) => setConfig({...config, value2: e.target.value})}
              />
            </div>
          </div>
        </>
      )}
      
      {(config?.filterType === 'custom') && (
        <div className="space-y-2">
          <Label>Custom Expression</Label>
          <Textarea
            placeholder="Enter custom filter expression"
            value={config?.customExpression || ""}
            onChange={(e) => setConfig({...config, customExpression: e.target.value})}
          />
          <p className="text-xs text-muted-foreground">
            Use JavaScript-like syntax, e.g., "candidate.experience {'>'}  5 && candidate.skills.includes('React')"
          </p>
        </div>
      )}
      
      <div className="flex items-center justify-between">
        <Label>Case Sensitive</Label>
        <Switch
          checked={config?.caseSensitive || false}
          onCheckedChange={(checked) => setConfig({...config, caseSensitive: checked})}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Strict Matching</Label>
        <Switch
          checked={config?.strictMatching || false}
          onCheckedChange={(checked) => setConfig({...config, strictMatching: checked})}
        />
      </div>
    </>
  );
}

export function DataComparisonConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Comparison Type</Label>
        <Select
          value={config?.comparisonType || "value"}
          onValueChange={(value) => setConfig({...config, comparisonType: value})}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select comparison type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="value">Value Comparison</SelectItem>
            <SelectItem value="range">Range Check</SelectItem>
            <SelectItem value="list">List Membership</SelectItem>
            <SelectItem value="regex">Regex Match</SelectItem>
            <SelectItem value="custom">Custom Comparison</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {(config?.comparisonType === 'value') && (
        <>
          <div className="space-y-2">
            <Label>Left Value</Label>
            <Input
              placeholder="Enter left value or variable"
              value={config?.leftValue || ""}
              onChange={(e) => setConfig({...config, leftValue: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label>Operator</Label>
            <Select
              value={config?.operator || "equals"}
              onValueChange={(value) => setConfig({...config, operator: value})}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select operator" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="equals">Equals (==)</SelectItem>
                <SelectItem value="strict-equals">Strict Equals (===)</SelectItem>
                <SelectItem value="not-equals">Not Equals (!=)</SelectItem>
                <SelectItem value="greater-than">Greater Than ({'>'})</SelectItem>
                <SelectItem value="less-than">Less Than ({'<'})</SelectItem>
                <SelectItem value="greater-equals">Greater Than or Equal ({'>'}=)</SelectItem>
                <SelectItem value="less-equals">Less Than or Equal ({'<'}=)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>Right Value</Label>
            <Input
              placeholder="Enter right value or variable"
              value={config?.rightValue || ""}
              onChange={(e) => setConfig({...config, rightValue: e.target.value})}
            />
          </div>
        </>
      )}
      
      {(config?.comparisonType === 'range') && (
        <>
          <div className="space-y-2">
            <Label>Value to Check</Label>
            <Input
              placeholder="Enter value or variable to check"
              value={config?.checkValue || ""}
              onChange={(e) => setConfig({...config, checkValue: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label>Minimum Value</Label>
            <Input
              placeholder="Enter minimum value"
              value={config?.minValue || ""}
              onChange={(e) => setConfig({...config, minValue: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label>Maximum Value</Label>
            <Input
              placeholder="Enter maximum value"
              value={config?.maxValue || ""}
              onChange={(e) => setConfig({...config, maxValue: e.target.value})}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label>Inclusive Range</Label>
            <Switch
              checked={config?.inclusiveRange || false}
              onCheckedChange={(checked) => setConfig({...config, inclusiveRange: checked})}
            />
          </div>
        </>
      )}
      
      {(config?.comparisonType === 'list') && (
        <>
          <div className="space-y-2">
            <Label>Value to Check</Label>
            <Input
              placeholder="Enter value or variable to check"
              value={config?.checkValue || ""}
              onChange={(e) => setConfig({...config, checkValue: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label>List Values</Label>
            <Textarea
              placeholder="Enter list values (comma-separated)"
              value={config?.listValues || ""}
              onChange={(e) => setConfig({...config, listValues: e.target.value})}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label>Negate (NOT IN)</Label>
            <Switch
              checked={config?.negate || false}
              onCheckedChange={(checked) => setConfig({...config, negate: checked})}
            />
          </div>
        </>
      )}
      
      {(config?.comparisonType === 'regex') && (
        <>
          <div className="space-y-2">
            <Label>Value to Check</Label>
            <Input
              placeholder="Enter value or variable to check"
              value={config?.checkValue || ""}
              onChange={(e) => setConfig({...config, checkValue: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label>Regular Expression</Label>
            <Input
              placeholder="Enter regex pattern"
              value={config?.regexPattern || ""}
              onChange={(e) => setConfig({...config, regexPattern: e.target.value})}
            />
          </div>
          <div className="flex items-center justify-between">
            <Label>Case Insensitive</Label>
            <Switch
              checked={config?.caseInsensitive || false}
              onCheckedChange={(checked) => setConfig({...config, caseInsensitive: checked})}
            />
          </div>
        </>
      )}
      
      {(config?.comparisonType === 'custom') && (
        <div className="space-y-2">
          <Label>Custom Comparison Expression</Label>
          <Textarea
            placeholder="Enter custom comparison expression"
            value={config?.customExpression || ""}
            onChange={(e) => setConfig({...config, customExpression: e.target.value})}
          />
          <p className="text-xs text-muted-foreground">
            Use JavaScript-like syntax, e.g., "value1 {'>'}  10 && value2.includes('test')"
          </p>
        </div>
      )}
      
      <div className="flex items-center justify-between">
        <Label>Invert Result</Label>
        <Switch
          checked={config?.invertResult || false}
          onCheckedChange={(checked) => setConfig({...config, invertResult: checked})}
        />
      </div>
    </>
  );
}
