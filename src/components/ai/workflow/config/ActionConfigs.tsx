import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export function SendEmailConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Email Template</Label>
        <Select
          value={config.template}
          onValueChange={(value) => setConfig({ ...config, template: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select email template" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="welcome">Welcome Email</SelectItem>
            <SelectItem value="interview">Interview Invitation</SelectItem>
            <SelectItem value="offer">Job Offer</SelectItem>
            <SelectItem value="rejection">Application Status</SelectItem>
            <SelectItem value="follow_up">Follow-up</SelectItem>
            <SelectItem value="assessment">Assessment Invitation</SelectItem>
            <SelectItem value="document_request">Document Request</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label>Custom Message</Label>
        <Textarea
          placeholder="Enter custom message"
          value={config.customMessage}
          onChange={(e) => setConfig({ ...config, customMessage: e.target.value })}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label htmlFor="cc-recruiter">CC Recruiter</Label>
        <Switch
          id="cc-recruiter"
          checked={config.ccRecruiter}
          onCheckedChange={(checked) => setConfig({ ...config, ccRecruiter: checked })}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label htmlFor="track-opens">Track Opens</Label>
        <Switch
          id="track-opens"
          checked={config.trackOpens}
          onCheckedChange={(checked) => setConfig({ ...config, trackOpens: checked })}
        />
      </div>
    </>
  );
}

export function ScheduleInterviewConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Interview Type</Label>
        <Select
          value={config.interviewType}
          onValueChange={(value) => setConfig({ ...config, interviewType: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select interview type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="technical">Technical Interview</SelectItem>
            <SelectItem value="behavioral">Behavioral Interview</SelectItem>
            <SelectItem value="cultural">Cultural Fit</SelectItem>
            <SelectItem value="final">Final Round</SelectItem>
            <SelectItem value="screening">Initial Screening</SelectItem>
            <SelectItem value="panel">Panel Interview</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label>Duration (minutes)</Label>
        <Input
          type="number"
          min="15"
          max="180"
          step="15"
          value={config.duration}
          onChange={(e) => setConfig({ ...config, duration: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label>Interviewers</Label>
        <Input
          placeholder="Enter interviewer emails (comma-separated)"
          value={config.interviewers}
          onChange={(e) => setConfig({ ...config, interviewers: e.target.value })}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label>Send Calendar Invite</Label>
        <Switch
          checked={config.sendCalendarInvite}
          onCheckedChange={(checked) => setConfig({ ...config, sendCalendarInvite: checked })}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label>Send Preparation Email</Label>
        <Switch
          checked={config.sendPrepEmail}
          onCheckedChange={(checked) => setConfig({ ...config, sendPrepEmail: checked })}
        />
      </div>
    </>
  );
}

export function AIScreenConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Screening Criteria</Label>
        <Select
          value={config.criteria}
          onValueChange={(value) => setConfig({ ...config, criteria: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select screening criteria" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="experience">Experience Match</SelectItem>
            <SelectItem value="skills">Skills Match</SelectItem>
            <SelectItem value="education">Education Match</SelectItem>
            <SelectItem value="comprehensive">Comprehensive</SelectItem>
            <SelectItem value="document_verification">Document Verification</SelectItem>
            <SelectItem value="cultural_fit">Cultural Fit</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label>Minimum Match Score (%)</Label>
        <Input
          type="number"
          min="0"
          max="100"
          value={config.minScore}
          onChange={(e) => setConfig({ ...config, minScore: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label>Custom Screening Instructions</Label>
        <Textarea
          placeholder="Enter custom screening instructions"
          value={config.customInstructions}
          onChange={(e) => setConfig({ ...config, customInstructions: e.target.value })}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label>Generate AI Summary</Label>
        <Switch
          checked={config.generateSummary}
          onCheckedChange={(checked) => setConfig({ ...config, generateSummary: checked })}
        />
      </div>
    </>
  );
}

export function SendAssessmentConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Assessment Type</Label>
        <Select
          value={config.assessmentType}
          onValueChange={(value) => setConfig({ ...config, assessmentType: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select assessment type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="coding">Coding Challenge</SelectItem>
            <SelectItem value="design">Design Exercise</SelectItem>
            <SelectItem value="writing">Writing Sample</SelectItem>
            <SelectItem value="personality">Personality Assessment</SelectItem>
            <SelectItem value="technical">Technical Quiz</SelectItem>
            <SelectItem value="case_study">Case Study</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label>Time Limit (minutes)</Label>
        <Input
          type="number"
          min="15"
          max="180"
          value={config.timeLimit}
          onChange={(e) => setConfig({ ...config, timeLimit: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label>Assessment Instructions</Label>
        <Textarea
          placeholder="Enter assessment instructions"
          value={config.instructions}
          onChange={(e) => setConfig({ ...config, instructions: e.target.value })}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label>Require Webcam</Label>
        <Switch
          checked={config.requireWebcam}
          onCheckedChange={(checked) => setConfig({ ...config, requireWebcam: checked })}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label>Auto-grade Assessment</Label>
        <Switch
          checked={config.autoGrade}
          onCheckedChange={(checked) => setConfig({ ...config, autoGrade: checked })}
        />
      </div>
    </>
  );
}

export function NotifyTeamConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Notification Channel</Label>
        <Select
          value={config.channel}
          onValueChange={(value) => setConfig({ ...config, channel: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select notification channel" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="email">Email</SelectItem>
            <SelectItem value="slack">Slack</SelectItem>
            <SelectItem value="teams">Microsoft Teams</SelectItem>
            <SelectItem value="sms">SMS</SelectItem>
            <SelectItem value="in_app">In-App Notification</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label>Team Members</Label>
        <Select
          value={config.teamMembers}
          onValueChange={(value) => setConfig({ ...config, teamMembers: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select team members" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Team Members</SelectItem>
            <SelectItem value="hiring-managers">Hiring Managers</SelectItem>
            <SelectItem value="recruiters">Recruiters</SelectItem>
            <SelectItem value="interviewers">Interviewers</SelectItem>
            <SelectItem value="custom">Custom Group</SelectItem>
          </SelectContent>
        </Select>
      </div>
      {config.teamMembers === 'custom' && (
        <div className="space-y-2">
          <Label>Custom Recipients</Label>
          <Input
            placeholder="Enter email addresses (comma-separated)"
            value={config.customRecipients}
            onChange={(e) => setConfig({ ...config, customRecipients: e.target.value })}
          />
        </div>
      )}
      <div className="space-y-2">
        <Label>Custom Message</Label>
        <Textarea
          placeholder="Enter custom notification message"
          value={config.customMessage}
          onChange={(e) => setConfig({ ...config, customMessage: e.target.value })}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label>High Priority</Label>
        <Switch
          checked={config.highPriority}
          onCheckedChange={(checked) => setConfig({ ...config, highPriority: checked })}
        />
      </div>
    </>
  );
}

export function DataEnrichmentConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Enrichment Source</Label>
        <Select
          value={config.source}
          onValueChange={(value) => setConfig({ ...config, source: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select data source" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="linkedin">LinkedIn</SelectItem>
            <SelectItem value="github">GitHub</SelectItem>
            <SelectItem value="clearbit">Clearbit</SelectItem>
            <SelectItem value="hunter">Hunter.io</SelectItem>
            <SelectItem value="custom">Custom API</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label>Data Fields to Enrich</Label>
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <Switch
              id="enrich-contact"
              checked={config.enrichContact}
              onCheckedChange={(checked) => setConfig({ ...config, enrichContact: checked })}
            />
            <Label htmlFor="enrich-contact">Contact Information</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="enrich-work"
              checked={config.enrichWork}
              onCheckedChange={(checked) => setConfig({ ...config, enrichWork: checked })}
            />
            <Label htmlFor="enrich-work">Work History</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="enrich-education"
              checked={config.enrichEducation}
              onCheckedChange={(checked) => setConfig({ ...config, enrichEducation: checked })}
            />
            <Label htmlFor="enrich-education">Education</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="enrich-skills"
              checked={config.enrichSkills}
              onCheckedChange={(checked) => setConfig({ ...config, enrichSkills: checked })}
            />
            <Label htmlFor="enrich-skills">Skills</Label>
          </div>
        </div>
      </div>
      {config.source === 'custom' && (
        <div className="space-y-2">
          <Label>Custom API Endpoint</Label>
          <Input
            placeholder="Enter API endpoint URL"
            value={config.customApiUrl}
            onChange={(e) => setConfig({ ...config, customApiUrl: e.target.value })}
          />
        </div>
      )}
      <div className="flex items-center justify-between">
        <Label>Auto-update Profile</Label>
        <Switch
          checked={config.autoUpdate}
          onCheckedChange={(checked) => setConfig({ ...config, autoUpdate: checked })}
        />
      </div>
    </>
  );
}

export function CreateTaskConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Task Title</Label>
        <Input
          placeholder="Enter task title"
          value={config.title}
          onChange={(e) => setConfig({ ...config, title: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label>Task Description</Label>
        <Textarea
          placeholder="Enter task description"
          value={config.description}
          onChange={(e) => setConfig({ ...config, description: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label>Assignee</Label>
        <Select
          value={config.assignee}
          onValueChange={(value) => setConfig({ ...config, assignee: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select assignee" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="recruiter">Recruiter</SelectItem>
            <SelectItem value="hiring-manager">Hiring Manager</SelectItem>
            <SelectItem value="team-lead">Team Lead</SelectItem>
            <SelectItem value="hr">HR</SelectItem>
            <SelectItem value="custom">Custom User</SelectItem>
          </SelectContent>
        </Select>
      </div>
      {config.assignee === 'custom' && (
        <div className="space-y-2">
          <Label>Custom Assignee</Label>
          <Input
            placeholder="Enter email address"
            value={config.customAssignee}
            onChange={(e) => setConfig({ ...config, customAssignee: e.target.value })}
          />
        </div>
      )}
      <div className="space-y-2">
        <Label>Due Date (days from now)</Label>
        <Input
          type="number"
          min="0"
          max="30"
          value={config.dueDays}
          onChange={(e) => setConfig({ ...config, dueDays: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label>Priority</Label>
        <Select
          value={config.priority}
          onValueChange={(value) => setConfig({ ...config, priority: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="low">Low</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="high">High</SelectItem>
            <SelectItem value="urgent">Urgent</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </>
  );
}

export function AddTagConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Tags to Add</Label>
        <Input
          placeholder="Enter tags (comma-separated)"
          value={config.tags}
          onChange={(e) => setConfig({ ...config, tags: e.target.value })}
        />
        <p className="text-xs text-muted-foreground">
          Enter multiple tags separated by commas (e.g., "skilled, experienced, python")
        </p>
      </div>
      <div className="space-y-2">
        <Label>Tag Category</Label>
        <Select
          value={config.category}
          onValueChange={(value) => setConfig({ ...config, category: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select tag category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="skills">Skills</SelectItem>
            <SelectItem value="experience">Experience</SelectItem>
            <SelectItem value="education">Education</SelectItem>
            <SelectItem value="location">Location</SelectItem>
            <SelectItem value="status">Status</SelectItem>
            <SelectItem value="custom">Custom</SelectItem>
          </SelectContent>
        </Select>
      </div>
      {config.category === 'custom' && (
        <div className="space-y-2">
          <Label>Custom Category</Label>
          <Input
            placeholder="Enter custom category name"
            value={config.customCategory}
            onChange={(e) => setConfig({ ...config, customCategory: e.target.value })}
          />
        </div>
      )}
      <div className="space-y-2">
        <Label>Tag Color</Label>
        <Select
          value={config.color}
          onValueChange={(value) => setConfig({ ...config, color: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select tag color" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="blue">Blue</SelectItem>
            <SelectItem value="green">Green</SelectItem>
            <SelectItem value="yellow">Yellow</SelectItem>
            <SelectItem value="red">Red</SelectItem>
            <SelectItem value="purple">Purple</SelectItem>
            <SelectItem value="gray">Gray</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="flex items-center justify-between">
        <Label htmlFor="overwrite-existing">Overwrite Existing Tags</Label>
        <Switch
          id="overwrite-existing"
          checked={config.overwriteExisting}
          onCheckedChange={(checked) => setConfig({ ...config, overwriteExisting: checked })}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label htmlFor="notify-team">Notify Team of Changes</Label>
        <Switch
          id="notify-team"
          checked={config.notifyTeam}
          onCheckedChange={(checked) => setConfig({ ...config, notifyTeam: checked })}
        />
      </div>
    </>
  );
}

export function ScoreCandidateConfig({ config, setConfig }: { config: any; setConfig: (config: any) => void }) {
  return (
    <>
      <div className="space-y-2">
        <Label>Scoring Method</Label>
        <Select
          value={config.scoringMethod}
          onValueChange={(value) => setConfig({ ...config, scoringMethod: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select scoring method" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="manual">Manual Score</SelectItem>
            <SelectItem value="calculated">Calculated Score</SelectItem>
            <SelectItem value="ai-generated">AI Generated</SelectItem>
            <SelectItem value="weighted">Weighted Criteria</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {config.scoringMethod === 'manual' && (
        <div className="space-y-2">
          <Label>Score (0-100)</Label>
          <Input
            type="number"
            min="0"
            max="100"
            value={config.manualScore}
            onChange={(e) => setConfig({ ...config, manualScore: e.target.value })}
          />
        </div>
      )}
      
      {config.scoringMethod === 'calculated' && (
        <>
          <div className="space-y-2">
            <Label>Calculation Formula</Label>
            <Select
              value={config.calculationFormula}
              onValueChange={(value) => setConfig({ ...config, calculationFormula: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select calculation method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="experience">Based on Experience</SelectItem>
                <SelectItem value="skills">Based on Skills Match</SelectItem>
                <SelectItem value="education">Based on Education</SelectItem>
                <SelectItem value="combined">Combined Factors</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </>
      )}
      
      {config.scoringMethod === 'ai-generated' && (
        <>
          <div className="space-y-2">
            <Label>AI Criteria</Label>
            <Textarea
              placeholder="Enter specific criteria for AI scoring"
              value={config.aiCriteria}
              onChange={(e) => setConfig({ ...config, aiCriteria: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label>AI Model</Label>
            <Select
              value={config.aiModel}
              onValueChange={(value) => setConfig({ ...config, aiModel: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select AI model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="gpt-4">GPT-4</SelectItem>
                <SelectItem value="claude">Claude</SelectItem>
                <SelectItem value="custom">Custom Model</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </>
      )}
      
      {config.scoringMethod === 'weighted' && (
        <>
          <div className="space-y-2">
            <Label>Experience Weight (%)</Label>
            <Input
              type="number"
              min="0"
              max="100"
              value={config.experienceWeight}
              onChange={(e) => setConfig({ ...config, experienceWeight: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label>Skills Weight (%)</Label>
            <Input
              type="number"
              min="0"
              max="100"
              value={config.skillsWeight}
              onChange={(e) => setConfig({ ...config, skillsWeight: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label>Education Weight (%)</Label>
            <Input
              type="number"
              min="0"
              max="100"
              value={config.educationWeight}
              onChange={(e) => setConfig({ ...config, educationWeight: e.target.value })}
            />
          </div>
        </>
      )}
      
      <div className="space-y-2">
        <Label>Score Category</Label>
        <Select
          value={config.scoreCategory}
          onValueChange={(value) => setConfig({ ...config, scoreCategory: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select score category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="overall">Overall Score</SelectItem>
            <SelectItem value="technical">Technical Score</SelectItem>
            <SelectItem value="cultural">Cultural Fit</SelectItem>
            <SelectItem value="communication">Communication</SelectItem>
            <SelectItem value="leadership">Leadership</SelectItem>
            <SelectItem value="custom">Custom Category</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {config.scoreCategory === 'custom' && (
        <div className="space-y-2">
          <Label>Custom Score Category</Label>
          <Input
            placeholder="Enter custom score category"
            value={config.customScoreCategory}
            onChange={(e) => setConfig({ ...config, customScoreCategory: e.target.value })}
          />
        </div>
      )}
      
      <div className="space-y-2">
        <Label>Score Notes</Label>
        <Textarea
          placeholder="Enter notes about this score"
          value={config.scoreNotes}
          onChange={(e) => setConfig({ ...config, scoreNotes: e.target.value })}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label htmlFor="update-overall">Update Overall Score</Label>
        <Switch
          id="update-overall"
          checked={config.updateOverall}
          onCheckedChange={(checked) => setConfig({ ...config, updateOverall: checked })}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label htmlFor="notify-changes">Notify Team of Score Changes</Label>
        <Switch
          id="notify-changes"
          checked={config.notifyChanges}
          onCheckedChange={(checked) => setConfig({ ...config, notifyChanges: checked })}
        />
      </div>
    </>
  );
}