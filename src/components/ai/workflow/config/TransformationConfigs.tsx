
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { 
  WorkflowConfigProps, 
  DataTransformConfig, 
  DataFilterConfig, 
  DataMergeConfig, 
  DataLoopConfig, 
  DelayConfig, 
  ScheduleConfig 
} from "@/types/workflow";

export function DataTransformConfig({ config, setConfig }: WorkflowConfigProps<DataTransformConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Transformation Type</Label>
        <Select
          value={config.transformType}
          onValueChange={(value: DataTransformConfig['transformType']) => setConfig({ ...config, transformType: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select transformation type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="map">Map Fields</SelectItem>
            <SelectItem value="format">Format Data</SelectItem>
            <SelectItem value="calculate">Calculate Values</SelectItem>
            <SelectItem value="extract">Extract Data</SelectItem>
            <SelectItem value="custom">Custom Transformation</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {config.transformType === 'map' && (
        <div className="space-y-2">
          <Label>Field Mapping</Label>
          <Textarea
            placeholder="Enter field mappings (JSON format)"
            value={config.fieldMapping}
            onChange={(e) => setConfig({ ...config, fieldMapping: e.target.value })}
          />
          <p className="text-xs text-muted-foreground">
            Format: "sourceField1": "targetField1", "sourceField2": "targetField2"
          </p>
        </div>
      )}
      
      {config.transformType === 'format' && (
        <>
          <div className="space-y-2">
            <Label>Format Template</Label>
            <Textarea
              placeholder="Enter format template"
              value={config.formatTemplate}
              onChange={(e) => setConfig({ ...config, formatTemplate: e.target.value })}
            />
            <p className="text-xs text-muted-foreground">
              Use fieldName to reference fields, e.g., "Hello, firstName lastName"
            </p>
          </div>
          <div className="space-y-2">
            <Label>Output Format</Label>
            <Select
              value={config.outputFormat}
              onValueChange={(value: DataTransformConfig['outputFormat']) => setConfig({ ...config, outputFormat: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select output format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="text">Plain Text</SelectItem>
                <SelectItem value="html">HTML</SelectItem>
                <SelectItem value="markdown">Markdown</SelectItem>
                <SelectItem value="json">JSON</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </>
      )}
      
      {config.transformType === 'calculate' && (
        <>
          <div className="space-y-2">
            <Label>Calculation Expression</Label>
            <Textarea
              placeholder="Enter calculation expression"
              value={config.calculationExpression}
              onChange={(e) => setConfig({ ...config, calculationExpression: e.target.value })}
            />
            <p className="text-xs text-muted-foreground">
              Use JavaScript-like expressions, e.g., "value1 * 2 + value2"
            </p>
          </div>
          <div className="space-y-2">
            <Label>Output Field</Label>
            <Input
              placeholder="Enter output field name"
              value={config.outputField}
              onChange={(e) => setConfig({ ...config, outputField: e.target.value })}
            />
          </div>
        </>
      )}
      
      {config.transformType === 'extract' && (
        <>
          <div className="space-y-2">
            <Label>Source Field</Label>
            <Input
              placeholder="Enter source field name"
              value={config.sourceField}
              onChange={(e) => setConfig({ ...config, sourceField: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label>Extraction Method</Label>
            <Select
              value={config.extractionMethod}
              onValueChange={(value: DataTransformConfig['extractionMethod']) => setConfig({ ...config, extractionMethod: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select extraction method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="regex">Regular Expression</SelectItem>
                <SelectItem value="jsonpath">JSON Path</SelectItem>
                <SelectItem value="xpath">XPath</SelectItem>
                <SelectItem value="substring">Substring</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>Extraction Pattern</Label>
            <Input
              placeholder="Enter extraction pattern"
              value={config.extractionPattern}
              onChange={(e) => setConfig({ ...config, extractionPattern: e.target.value })}
            />
          </div>
        </>
      )}
      
      {config.transformType === 'custom' && (
        <div className="space-y-2">
          <Label>Custom Transformation Code</Label>
          <Textarea
            placeholder="Enter custom transformation code"
            value={config.customCode}
            onChange={(e) => setConfig({ ...config, customCode: e.target.value })}
            rows={8}
          />
          <p className="text-xs text-muted-foreground">
            Use JavaScript code to transform the data. The input data is available as 'data' and you should return the transformed data.
          </p>
        </div>
      )}
      
      <div className="flex items-center justify-between">
        <Label>Preserve Original Data</Label>
        <Switch
          checked={config.preserveOriginal}
          onCheckedChange={(checked) => setConfig({ ...config, preserveOriginal: checked })}
        />
      </div>
    </>
  );
}

export function DataFilterConfig({ config, setConfig }: WorkflowConfigProps<DataFilterConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Filter Type</Label>
        <Select
          value={config.filterType}
          onValueChange={(value: DataFilterConfig['filterType']) => setConfig({ ...config, filterType: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select filter type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="include">Include Fields</SelectItem>
            <SelectItem value="exclude">Exclude Fields</SelectItem>
            <SelectItem value="condition">Conditional Filter</SelectItem>
            <SelectItem value="custom">Custom Filter</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {(config.filterType === 'include' || config.filterType === 'exclude') && (
        <div className="space-y-2">
          <Label>Fields</Label>
          <Textarea
            placeholder="Enter field names (comma-separated)"
            value={config.fields}
            onChange={(e) => setConfig({ ...config, fields: e.target.value })}
          />
        </div>
      )}
      
      {config.filterType === 'condition' && (
        <>
          <div className="space-y-2">
            <Label>Field</Label>
            <Input
              placeholder="Enter field name"
              value={config.field}
              onChange={(e) => setConfig({ ...config, field: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label>Operator</Label>
            <Select
              value={config.operator}
              onValueChange={(value: DataFilterConfig['operator']) => setConfig({ ...config, operator: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select operator" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="equals">Equals</SelectItem>
                <SelectItem value="not-equals">Not Equals</SelectItem>
                <SelectItem value="contains">Contains</SelectItem>
                <SelectItem value="greater-than">Greater Than</SelectItem>
                <SelectItem value="less-than">Less Than</SelectItem>
                <SelectItem value="exists">Exists</SelectItem>
                <SelectItem value="not-exists">Not Exists</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>Value</Label>
            <Input
              placeholder="Enter value"
              value={config.value}
              onChange={(e) => setConfig({ ...config, value: e.target.value })}
            />
          </div>
        </>
      )}
      
      {config.filterType === 'custom' && (
        <div className="space-y-2">
          <Label>Custom Filter Expression</Label>
          <Textarea
            placeholder="Enter custom filter expression"
            value={config.customExpression}
            onChange={(e) => setConfig({ ...config, customExpression: e.target.value })}
            rows={6}
          />
          <p className="text-xs text-muted-foreground">
            Use JavaScript-like syntax, e.g., "item =&gt; item.age &gt; 25 && item.skills.includes('JavaScript')"
          </p>
        </div>
      )}
      
      <div className="flex items-center justify-between">
        <Label>Case Sensitive</Label>
        <Switch
          checked={config.caseSensitive}
          onCheckedChange={(checked) => setConfig({ ...config, caseSensitive: checked })}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Trim Whitespace</Label>
        <Switch
          checked={config.trimWhitespace}
          onCheckedChange={(checked) => setConfig({ ...config, trimWhitespace: checked })}
        />
      </div>
    </>
  );
}

export function DataMergeConfig({ config, setConfig }: WorkflowConfigProps<DataMergeConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Merge Type</Label>
        <Select
          value={config.mergeType}
          onValueChange={(value: DataMergeConfig['mergeType']) => setConfig({ ...config, mergeType: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select merge type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="simple">Simple Merge</SelectItem>
            <SelectItem value="deep">Deep Merge</SelectItem>
            <SelectItem value="array">Array Merge</SelectItem>
            <SelectItem value="custom">Custom Merge</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label>Primary Data Source</Label>
        <Input
          placeholder="Enter primary data source"
          value={config.primarySource}
          onChange={(e) => setConfig({ ...config, primarySource: e.target.value })}
        />
        <p className="text-xs text-muted-foreground">
          Reference to the primary data source (e.g., "candidateData")
        </p>
      </div>
      
      <div className="space-y-2">
        <Label>Secondary Data Source</Label>
        <Input
          placeholder="Enter secondary data source"
          value={config.secondarySource}
          onChange={(e) => setConfig({ ...config, secondarySource: e.target.value })}
        />
        <p className="text-xs text-muted-foreground">
          Reference to the secondary data source (e.g., "jobData")
        </p>
      </div>
      
      {config.mergeType === 'custom' && (
        <div className="space-y-2">
          <Label>Custom Merge Logic</Label>
          <Textarea
            placeholder="Enter custom merge logic"
            value={config.customMergeLogic}
            onChange={(e) => setConfig({ ...config, customMergeLogic: e.target.value })}
            rows={6}
          />
          <p className="text-xs text-muted-foreground">
            Use JavaScript-like syntax to define custom merge logic
          </p>
        </div>
      )}
      
      <div className="flex items-center justify-between">
        <Label>Overwrite Existing Values</Label>
        <Switch
          checked={config.overwriteExisting}
          onCheckedChange={(checked) => setConfig({ ...config, overwriteExisting: checked })}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Include Null Values</Label>
        <Switch
          checked={config.includeNullValues}
          onCheckedChange={(checked) => setConfig({ ...config, includeNullValues: checked })}
        />
      </div>
    </>
  );
}

export function DataLoopConfig({ config, setConfig }: WorkflowConfigProps<DataLoopConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Loop Source</Label>
        <Input
          placeholder="Enter data source to loop through"
          value={config.loopSource}
          onChange={(e) => setConfig({ ...config, loopSource: e.target.value })}
        />
        <p className="text-xs text-muted-foreground">
          Reference to the array or collection to loop through (e.g., "candidates")
        </p>
      </div>
      
      <div className="space-y-2">
        <Label>Loop Variable Name</Label>
        <Input
          placeholder="Enter loop variable name"
          value={config.loopVariable}
          onChange={(e) => setConfig({ ...config, loopVariable: e.target.value })}
        />
        <p className="text-xs text-muted-foreground">
          Name of the variable to use for each item in the loop (e.g., "candidate")
        </p>
      </div>
      
      <div className="space-y-2">
        <Label>Loop Type</Label>
        <Select
          value={config.loopType}
          onValueChange={(value: DataLoopConfig['loopType']) => setConfig({ ...config, loopType: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select loop type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="for-each">For Each</SelectItem>
            <SelectItem value="for-of">For Of</SelectItem>
            <SelectItem value="while">While</SelectItem>
            <SelectItem value="do-while">Do While</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {(config.loopType === 'while' || config.loopType === 'do-while') && (
        <div className="space-y-2">
          <Label>Loop Condition</Label>
          <Input
            placeholder="Enter loop condition"
            value={config.loopCondition}
            onChange={(e) => setConfig({ ...config, loopCondition: e.target.value })}
          />
          <p className="text-xs text-muted-foreground">
            Condition to continue the loop (e.g., "index &lt; 10")
          </p>
        </div>
      )}
      
      <div className="space-y-2">
        <Label>Maximum Iterations</Label>
        <Input
          type="number"
          min="1"
          max="1000"
          value={config.maxIterations}
          onChange={(e) => setConfig({ ...config, maxIterations: e.target.value })}
        />
        <p className="text-xs text-muted-foreground">
          Safety limit to prevent infinite loops
        </p>
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Parallel Execution</Label>
        <Switch
          checked={config.parallelExecution}
          onCheckedChange={(checked) => setConfig({ ...config, parallelExecution: checked })}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Continue on Error</Label>
        <Switch
          checked={config.continueOnError}
          onCheckedChange={(checked) => setConfig({ ...config, continueOnError: checked })}
        />
      </div>
    </>
  );
}

export function DelayConfig({ config, setConfig }: WorkflowConfigProps<DelayConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Delay Duration</Label>
        <Input
          type="number"
          min="1"
          value={config.duration}
          onChange={(e) => setConfig({ ...config, duration: e.target.value })}
        />
      </div>
      
      <div className="space-y-2">
        <Label>Duration Unit</Label>
        <Select
          value={config.unit}
          onValueChange={(value: DelayConfig['unit']) => setConfig({ ...config, unit: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select time unit" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="seconds">Seconds</SelectItem>
            <SelectItem value="minutes">Minutes</SelectItem>
            <SelectItem value="hours">Hours</SelectItem>
            <SelectItem value="days">Days</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label>Delay Type</Label>
        <Select
          value={config.delayType}
          onValueChange={(value: DelayConfig['delayType']) => setConfig({ ...config, delayType: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select delay type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="fixed">Fixed Delay</SelectItem>
            <SelectItem value="random">Random Delay</SelectItem>
            <SelectItem value="exponential">Exponential Backoff</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {config.delayType === 'random' && (
        <div className="space-y-2">
          <Label>Maximum Duration</Label>
          <Input
            type="number"
            min="1"
            value={config.maxDuration}
            onChange={(e) => setConfig({ ...config, maxDuration: e.target.value })}
          />
          <p className="text-xs text-muted-foreground">
            Random delay between duration and maximum duration
          </p>
        </div>
      )}
      
      {config.delayType === 'exponential' && (
        <>
          <div className="space-y-2">
            <Label>Base Duration</Label>
            <Input
              type="number"
              min="1"
              value={config.baseDuration}
              onChange={(e) => setConfig({ ...config, baseDuration: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label>Retry Count</Label>
            <Input
              type="number"
              min="1"
              max="10"
              value={config.retryCount}
              onChange={(e) => setConfig({ ...config, retryCount: e.target.value })}
            />
          </div>
        </>
      )}
      
      <div className="flex items-center justify-between">
        <Label>Cancel on Workflow Stop</Label>
        <Switch
          checked={config.cancelOnStop}
          onCheckedChange={(checked) => setConfig({ ...config, cancelOnStop: checked })}
        />
      </div>
    </>
  );
}

export function ScheduleConfig({ config, setConfig }: WorkflowConfigProps<ScheduleConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Schedule Type</Label>
        <Select
          value={config.scheduleType}
          onValueChange={(value: ScheduleConfig['scheduleType']) => setConfig({ ...config, scheduleType: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select schedule type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="date">Specific Date/Time</SelectItem>
            <SelectItem value="delay">Delay from Now</SelectItem>
            <SelectItem value="cron">Cron Expression</SelectItem>
            <SelectItem value="business-hours">Business Hours</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {config.scheduleType === 'date' && (
        <>
          <div className="space-y-2">
            <Label>Date</Label>
            <Input
              type="date"
              value={config.date}
              onChange={(e) => setConfig({ ...config, date: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label>Time</Label>
            <Input
              type="time"
              value={config.time}
              onChange={(e) => setConfig({ ...config, time: e.target.value })}
            />
          </div>
        </>
      )}
      
      {config.scheduleType === 'delay' && (
        <>
          <div className="space-y-2">
            <Label>Delay Duration</Label>
            <Input
              type="number"
              min="1"
              value={config.duration}
              onChange={(e) => setConfig({ ...config, duration: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label>Duration Unit</Label>
            <Select
              value={config.unit}
              onValueChange={(value: ScheduleConfig['unit']) => setConfig({ ...config, unit: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select time unit" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="minutes">Minutes</SelectItem>
                <SelectItem value="hours">Hours</SelectItem>
                <SelectItem value="days">Days</SelectItem>
                <SelectItem value="weeks">Weeks</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </>
      )}
      
      {config.scheduleType === 'cron' && (
        <div className="space-y-2">
          <Label>Cron Expression</Label>
          <Input
            placeholder="Enter cron expression"
            value={config.cronExpression}
            onChange={(e) => setConfig({ ...config, cronExpression: e.target.value })}
          />
          <p className="text-xs text-muted-foreground">
            Format: minute hour day-of-month month day-of-week (e.g., "0 9 * * 1" for every Monday at 9 AM)
          </p>
        </div>
      )}
      
      {config.scheduleType === 'business-hours' && (
        <>
          <div className="space-y-2">
            <Label>Business Days</Label>
            <div className="flex flex-wrap gap-2">
              {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day, index) => (
                <div key={day} className="flex items-center space-x-2">
                  <Switch
                    id={`day-${index}`}
                    checked={config.businessDays?.[index] || false}
                    onCheckedChange={(checked) => {
                      const days = [...(config.businessDays || Array(7).fill(false))];
                      days[index] = checked;
                      setConfig({ ...config, businessDays: days });
                    }}
                  />
                  <Label htmlFor={`day-${index}`}>{day}</Label>
                </div>
              ))}
            </div>
          </div>
          <div className="space-y-2">
            <Label>Business Hours</Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Start Time</Label>
                <Input
                  type="time"
                  value={config.businessHoursStart}
                  onChange={(e) => setConfig({ ...config, businessHoursStart: e.target.value })}
                />
              </div>
              <div>
                <Label className="text-xs">End Time</Label>
                <Input
                  type="time"
                  value={config.businessHoursEnd}
                  onChange={(e) => setConfig({ ...config, businessHoursEnd: e.target.value })}
                />
              </div>
            </div>
          </div>
        </>
      )}
      
      <div className="space-y-2">
        <Label>Time Zone</Label>
        <Select
          value={config.timeZone}
          onValueChange={(value) => setConfig({ ...config, timeZone: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select time zone" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="UTC">UTC</SelectItem>
            <SelectItem value="America/New_York">Eastern Time (ET)</SelectItem>
            <SelectItem value="America/Chicago">Central Time (CT)</SelectItem>
            <SelectItem value="America/Denver">Mountain Time (MT)</SelectItem>
            <SelectItem value="America/Los_Angeles">Pacific Time (PT)</SelectItem>
            <SelectItem value="Europe/London">London (GMT)</SelectItem>
            <SelectItem value="Europe/Paris">Central European Time (CET)</SelectItem>
            <SelectItem value="Asia/Tokyo">Japan (JST)</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="flex items-center justify-between">
        <Label>Reschedule on Conflict</Label>
        <Switch
          checked={config.rescheduleOnConflict}
          onCheckedChange={(checked) => setConfig({ ...config, rescheduleOnConflict: checked })}
        />
      </div>
    </>
  );
}
