import { useParams } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { WorkflowExecutor } from "./WorkflowExecutor"; 
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useWorkflowConfiguration } from "@/hooks/useWorkflowConfigurations";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useState } from "react";

export function WorkflowRunnerPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { data: workflow, isLoading } = useWorkflowConfiguration(id || '');
  const [contextData, setContextData] = useState("{}");
  
  if (!id) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Button variant="ghost" onClick={() => navigate('/ai-workflows')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Workflows
          </Button>
        </div>
        <div className="text-center py-8">
          <h2 className="text-xl font-bold mb-2">Workflow Not Found</h2>
          <p className="text-muted-foreground">No workflow ID was provided.</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Button variant="ghost" onClick={() => navigate('/ai-workflows')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Workflows
        </Button>
      </div>
      
      {isLoading ? (
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[400px] w-full" />
          </CardContent>
        </Card>
      ) : workflow ? (
        <Card>
          <CardHeader>
            <CardTitle>Run Workflow: {workflow.name}</CardTitle>
          </CardHeader>
          <CardContent>
            {workflow.description && (
              <p className="text-muted-foreground mb-6">{workflow.description}</p>
            )}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="context-data">Execution Context (Optional)</Label>
                <Textarea
                  id="context-data"
                  placeholder='Enter JSON context data, e.g., {"candidateId": "123"}'
                  value={contextData}
                  onChange={(e) => setContextData(e.target.value)}
                  rows={3}
                />
                <p className="text-xs text-muted-foreground">
                  Provide context data for the workflow execution in JSON format
                </p>
              </div>
              
              <WorkflowExecutor 
                workflowId={id} 
                context={contextData}
              />
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="text-center py-8">
          <h2 className="text-xl font-bold mb-2">Workflow Not Found</h2>
          <p className="text-muted-foreground">The requested workflow could not be found or you don't have permission to access it.</p>
        </div>
      )}
    </div>
  );
}