import { type ReactElement } from "react";
import { screenCandidate } from "@/utils/gemini";
import { supabase } from "@/integrations/supabase/client";

export interface WorkflowNodeData {
  id: string;
  category: string;
  type: string;
  label: string;
  description: string;
  icon: ReactElement | null;
  config?: Record<string, any>;
}

export function parseDroppedData(rawData: string): WorkflowNodeData | null {
  try {
    const data = JSON.parse(rawData);
    // Create a new object with only the properties we need
    return {
      id: data.id,
      category: data.category,
      type: data.type,
      label: data.label,
      description: data.description,
      icon: data.icon, // Keep the icon as is
      config: data.config || {}
    };
  } catch (error) {
    console.error("Error parsing dropped data:", error);
    return null;
  }
}

// Validate workflow structure
export function validateWorkflow(nodes: any[], edges: any[]): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check if there are any nodes
  if (nodes.length === 0) {
    errors.push("Workflow must contain at least one node");
    return { valid: false, errors };
  }

  // Check if there is at least one trigger node
  const triggerNodes = nodes.filter(
    node => node.data.type === 'trigger' || node.data.category === 'triggers'
  );
  
  if (triggerNodes.length === 0) {
    errors.push("Workflow must contain at least one trigger node");
  }

  // Check for disconnected nodes (except trigger nodes)
  const nonTriggerNodes = nodes.filter(
    node => node.data.type !== 'trigger' && node.data.category !== 'triggers'
  );
  
  nonTriggerNodes.forEach(node => {
    const hasIncomingEdge = edges.some(edge => edge.target === node.id);
    const hasOutgoingEdge = edges.some(edge => edge.source === node.id);
    
    if (!hasIncomingEdge) {
      errors.push(`Node "${node.data.label}" (${node.id}) has no incoming connections`);
    }
    
    // Output nodes don't need outgoing edges
    if (node.data.type !== 'output' && node.data.category !== 'outputs' && !hasOutgoingEdge) {
      errors.push(`Node "${node.data.label}" (${node.id}) has no outgoing connections`);
    }
  });

  // Check for cycles in the graph
  if (hasCycle(nodes, edges)) {
    errors.push("Workflow contains a cycle, which is not allowed");
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

// Helper function to check for cycles in the graph
function hasCycle(nodes: any[], edges: any[]): boolean {
  // Create an adjacency list
  const adjacencyList: Record<string, string[]> = {};
  
  nodes.forEach(node => {
    adjacencyList[node.id] = [];
  });
  
  edges.forEach(edge => {
    if (adjacencyList[edge.source]) {
      adjacencyList[edge.source].push(edge.target);
    }
  });
  
  // Keep track of visited nodes
  const visited: Record<string, boolean> = {};
  const recursionStack: Record<string, boolean> = {};
  
  // DFS function to detect cycles
  function dfs(nodeId: string): boolean {
    // Mark the current node as visited and add to recursion stack
    visited[nodeId] = true;
    recursionStack[nodeId] = true;
    
    // Visit all the adjacent vertices
    for (const adjacentNode of adjacencyList[nodeId]) {
      // If not visited, then check if there's a cycle starting from this adjacent node
      if (!visited[adjacentNode] && dfs(adjacentNode)) {
        return true;
      } else if (recursionStack[adjacentNode]) {
        // If the adjacent node is in the recursion stack, then there's a cycle
        return true;
      }
    }
    
    // Remove the node from recursion stack
    recursionStack[nodeId] = false;
    return false;
  }
  
  // Check for cycles starting from each node
  for (const node of nodes) {
    if (!visited[node.id]) {
      if (dfs(node.id)) {
        return true;
      }
    }
  }
  
  return false;
}

// Function to evaluate a condition node
export function evaluateCondition(node: any, context: Record<string, any>): boolean {
  const nodeType = node.data.originalId || node.data.id;
  const config = node.data.config || {};
  
  // Evaluate the condition based on node type
  switch (nodeType) {
    case 'skills-match': {
      // Check if candidate has required skills
      if (!context.candidateId && !context.lastResult) return false;
      
      const requiredSkills = config.requiredSkills?.split(',')?.map((s: string) => s.trim()) || [];
      const minMatchPercentage = parseInt(config.minMatchPercentage || '70', 10);
      
      // If we have a candidate in the context, use their data for a real evaluation
      if (context.candidate) {
        // Get candidate skills
        const candidateSkills = context.candidate.skills || [];
        
        // Calculate match percentage based on required skills
        let matchCount = 0;
        requiredSkills.forEach((requiredSkill: string) => {
          const found = candidateSkills.some((skill: any) => {
            if (typeof skill === 'string') {
              return skill.toLowerCase().includes(requiredSkill.toLowerCase());
            } else if (skill.name) {
              return skill.name.toLowerCase().includes(requiredSkill.toLowerCase());
            }
            return false;
          });
          
          if (found) matchCount++;
        });
        
        const matchPercentage = requiredSkills.length > 0 
          ? (matchCount / requiredSkills.length) * 100 
          : 0;
          
        return matchPercentage >= minMatchPercentage;
      } else if (context.lastResult && context.lastResult.score) {
        // If we have a previous AI screening result, use that
        return context.lastResult.score >= minMatchPercentage;
      } else {
        // Otherwise simulate a score
        const matchPercentage = Math.floor(Math.random() * 100);
        return matchPercentage >= minMatchPercentage;
      }
    }
    
    case 'experience-check': {
      // Check if candidate has required experience
      if (!context.candidateId && !context.lastResult) return false;
      
      const minYears = parseInt(config.minYears || '3', 10);
      
      // In a real implementation, this would query the candidate's experience
      // For demo purposes, we'll simulate years of experience
      let yearsOfExperience = 0;
      
      // If we have candidate data in context, use that
      if (context.candidateExperience) {
        // Try to extract years from strings like "5 years"
        const match = context.candidateExperience.match(/(\d+)/);
        if (match) {
          yearsOfExperience = parseInt(match[1], 10);
        } else {
          yearsOfExperience = Math.floor(Math.random() * 10);
        }
      } else {
        // Otherwise simulate experience
        yearsOfExperience = Math.floor(Math.random() * 10);
      }
      
      return yearsOfExperience >= minYears;
    }
    
    case 'education-check': {
      // Check if candidate has required education
      if (!context.candidateId && !context.lastResult) return false;
      
      const minDegree = config.minDegree || 'bachelors'; 
      
      // In a real implementation, this would query the candidate's education
      // For demo purposes, we'll simulate a degree match
      const degrees = ['high-school', 'bachelors', 'masters', 'phd'];
      const degreeIndex = degrees.indexOf(minDegree);
      const candidateDegreeIndex = Math.floor(Math.random() * degrees.length);
      
      return candidateDegreeIndex >= degreeIndex;
    }
    
    case 'location-check': {
      // Check if candidate is in the required location
      if (!context.candidateId) return false;
      
      const locationType = config.locationType || 'onsite';
      
      // In a real implementation, this would query the candidate's location preferences
      // For demo purposes, we'll simulate a location match
      const locationTypes = ['onsite', 'hybrid', 'remote'];
      const candidateLocationType = locationTypes[Math.floor(Math.random() * locationTypes.length)];
      
      if (locationType === 'remote') {
        // Remote jobs match all candidate preferences
        return true;
      } else if (locationType === 'hybrid') {
        // Hybrid jobs match hybrid and onsite candidates
        return candidateLocationType !== 'remote';
      } else {
        // Onsite jobs only match onsite candidates
        return candidateLocationType === 'onsite';
      }
    }
    
    default:
      // Default to true for any unimplemented condition
      return true;
  }
}

// Function to execute a node action
export async function executeNodeAction(node: any, context: Record<string, any>): Promise<any> {
  // Get the node type and configuration
  const nodeType = node.data.originalId || node.data.id;
  const config = node.data.config || {};
  
  console.log(`Executing action for node type: ${nodeType}`);
  console.log(`Node config:`, config);
  console.log(`Context:`, context);
  
  // Execute the appropriate action based on node type
  switch (nodeType) {
    case 'send-email':
      return await sendEmailToCandidate(
        context.candidateId || context.lastResult?.candidateId || 'sample-candidate', 
        config.template || 'default', 
        config.customMessage
      );
    
    case 'ai-screen': {
      try {
        // Get candidate and job data from context
        const candidateId = context.candidateId || context.lastResult?.candidateId;
        const jobId = context.jobId || context.lastResult?.jobId;
        
        if (!candidateId) {
          throw new Error('No candidate ID found in context');
        }
        
        // Get candidate details
        const { data: candidate, error: candidateError } = await supabase
          .from('candidates')
          .select('*')
          .eq('id', candidateId)
          .single();
        
        if (candidateError) throw candidateError;
        
        // Get job details if available
        let jobDescription = "";
        if (jobId) {
          const { data: job, error: jobError } = await supabase
            .from('jobs')
            .select('*')
            .eq('id', jobId)
            .single();
          
          if (!jobError && job) {
            jobDescription = job.description;
          }
        }
        
        // Use Gemini to screen the candidate
        const screeningResult = await screenCandidate(candidate, jobDescription || `${candidate.role} position`);
        
        // Create a timeline entry for the screening
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (user) {
            await supabase
              .from('candidate_timeline')
              .insert({
                candidate_id: candidateId,
                user_id: user.id,
                event_type: 'screening',
                title: 'AI Screening Completed',
                description: `AI screening completed with score: ${screeningResult.score}%. Criteria: ${config.criteria || 'comprehensive'}`,
                event_date: new Date().toISOString(),
                status: 'completed'
              });
          }
        } catch (error) {
          console.error('Error creating timeline entry:', error);
        }
        
        return { 
          score: screeningResult.score,
          passed: screeningResult.score >= parseInt(config.minScore || '75', 10),
          candidateId,
          candidateName: candidate.name,
          criteria: config.criteria || 'comprehensive',
          strengths: screeningResult.strengths,
          gaps: screeningResult.gaps,
          summary: screeningResult.summary,
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        console.error('Error performing AI screening:', error);
        return { 
          score: 0,
          passed: false,
          candidateId: context.candidateId || context.lastResult?.candidateId,
          criteria: config.criteria || 'comprehensive',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        };
      }
    }
    
    case 'schedule-interview':
      return await scheduleInterview(
        context.candidateId || context.lastResult?.candidateId || 'sample-candidate', 
        config
      );
    
    case 'send-assessment':
      return await sendAssessment(
        context.candidateId || context.lastResult?.candidateId || 'sample-candidate', 
        config
      );
    
    case 'notify-team':
      return await notifyTeam(
        config.channel || 'email', 
        config.teamMembers || 'all', 
        config.customMessage || ''
      );
    
    case 'update-status':
      return await updateCandidateStatus(
        context.candidateId || context.lastResult?.candidateId || 'sample-candidate', 
        config.newStatus || 'pending', 
        config.statusNote || ''
      );
    
    case 'add-to-pool':
      return await addToTalentPool(
        context.candidateId || context.lastResult?.candidateId || 'sample-candidate', 
        config.poolName || 'general', 
        config.tags || ''
      );
    
    case 'send-message':
      return await sendMessage(
        context.candidateId || context.lastResult?.candidateId || 'sample-candidate', 
        config.template || 'general', 
        config.messageContent || ''
      );
    
    // Trigger nodes just initiate the workflow
    case 'new-application':
    case 'application-status':
    case 'scheduled-trigger': {
      return { 
        triggered: true, 
        type: nodeType,
        timestamp: new Date().toISOString(),
        context: { ...context }
      };
    }
    
    default: {
      // For any unimplemented node types, just return success
      return { 
        executed: true, 
        type: nodeType,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Implementation of action functions
async function sendEmailToCandidate(candidateId: string, template: string, customMessage?: string): Promise<any> {
  console.log(`🔹 Sending email to candidate ${candidateId} using template ${template}`);
  console.log(`🔹 Custom message: ${customMessage}`);
  
  try {
    // Get candidate details from database
    const { data: candidate, error: candidateError } = await supabase
      .from('candidates')
      .select('name, email')
      .eq('id', candidateId)
      .single();
    
    if (candidateError) {
      console.error('Error fetching candidate:', candidateError);
      throw new Error('Failed to fetch candidate details');
    }
    
    // Get email template from database
    const { data: templates, error: templateError } = await supabase
      .from('message_templates')
      .select('*')
      .eq('template_category', template)
      .limit(1);
    
    if (templateError) {
      console.error('Error fetching template:', templateError);
      throw new Error('Failed to fetch email template');
    }
    
    const emailTemplate = templates && templates.length > 0 ? templates[0] : null;
    
    // In a real implementation, this would call an email service
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Log the email that would be sent
    console.log(`📧 Would send email to: ${candidate?.email}`);
    console.log(`📧 Subject: ${emailTemplate?.subject || 'No subject'}`);
    console.log(`📧 Content: ${customMessage || emailTemplate?.content || 'No content'}`);
    
    // Create a message record in the database
    const { data: message, error: messageError } = await supabase
      .from('messages')
      .insert({
        sender_name: 'Workflow Automation',
        sender_email: '<EMAIL>',
        sender_role: 'System',
        content: customMessage || emailTemplate?.content || `Email sent to ${candidate?.name} using template: ${template}`,
        status: 'unread',
        user_id: (await supabase.auth.getUser()).data.user?.id
      })
      .select()
      .single();
    
    if (messageError) {
      console.error('Error creating message record:', messageError);
    }
    
    return { 
      sent: true, 
      candidateId, 
      template,
      candidateName: candidate?.name,
      candidateEmail: candidate?.email,
      messageId: message?.id,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error sending email:', error);
    return { 
      sent: false, 
      candidateId, 
      template,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
}

async function performAIScreening(candidateId: string, criteria: string, minScore: number): Promise<any> {
  console.log(`🔹 Performing AI screening for candidate ${candidateId} with criteria ${criteria}`);
  console.log(`🔹 Minimum score: ${minScore}`);
  
  try {
    // Get candidate details from database
    const { data: candidate, error: candidateError } = await supabase
      .from('candidates')
      .select('name, role, skills, experience, ai_summary')
      .eq('id', candidateId)
      .single();
    
    if (candidateError) {
      console.error('Error fetching candidate:', candidateError);
      throw new Error('Failed to fetch candidate details');
    }
    
    // In a real implementation, this would call an AI service
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Generate a score based on criteria and candidate data
    let score = 0;
    const skills = candidate?.skills || [];
    
    // Calculate score based on skills match
    if (Array.isArray(skills) && skills.length > 0) {
      // Higher score for more skills and higher levels
      const skillLevels = {
        'beginner': 1,
        'intermediate': 2,
        'advanced': 3,
        'expert': 4
      };
      
      let skillScore = 0;
      let totalPossible = 0;
      
      skills.forEach((skill: any) => {
        if (typeof skill === 'object' && skill.level) {
          const level = skill.level.toLowerCase();
          skillScore += skillLevels[level as keyof typeof skillLevels] || 1;
          totalPossible += 4; // Maximum possible score per skill
        }
      });
      
      // Convert to percentage
      score = Math.round((skillScore / totalPossible) * 100);
    } else {
      // Random score between 60 and 100 if no skills data
      score = Math.floor(Math.random() * 41) + 60;
    }
    
    // Adjust score based on criteria
    if (criteria === 'comprehensive') {
      // Comprehensive is more strict
      score = Math.max(score - 5, 0);
    } else if (criteria === 'document_verification') {
      // Document verification is less strict
      score = Math.min(score + 10, 100);
    }
    
    // Create a timeline entry for the screening
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase
          .from('candidate_timeline')
          .insert({
            candidate_id: candidateId,
            user_id: user.id,
            event_type: 'screening',
            title: 'AI Screening Completed',
            description: `AI screening completed with score: ${score}%. Criteria: ${criteria}`,
            event_date: new Date().toISOString(),
            status: 'completed'
          });
      }
    } catch (error) {
      console.error('Error creating timeline entry:', error);
    }
    
    return { 
      score,
      passed: score >= minScore,
      candidateId,
      candidateName: candidate?.name,
      criteria,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error performing AI screening:', error);
    return { 
      score: 0,
      passed: false,
      candidateId,
      criteria,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
}

async function scheduleInterview(candidateId: string, config: any): Promise<any> {
  console.log(`🔹 Scheduling ${config.interviewType} interview for candidate ${candidateId}`);
  console.log(`🔹 Duration: ${config.duration} minutes`);
  console.log(`🔹 Send calendar invite: ${config.sendCalendarInvite}`);
  
  try {
    // Get candidate details
    const { data: candidate, error: candidateError } = await supabase
      .from('candidates')
      .select('name, email')
      .eq('id', candidateId)
      .single();
    
    if (candidateError) {
      console.error('Error fetching candidate:', candidateError);
      throw new Error('Failed to fetch candidate details');
    }
    
    // Calculate interview date (3 business days from now)
    const interviewDate = new Date();
    interviewDate.setDate(interviewDate.getDate() + 3);
    
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Create interview record in database
    const { data: interview, error: interviewError } = await supabase
      .from('candidate_interviews')
      .insert({
        candidate_id: candidateId,
        user_id: user.id,
        interview_type: config.interviewType || 'Technical Interview',
        scheduled_date: interviewDate.toISOString(),
        duration_minutes: parseInt(config.duration) || 60,
        status: 'scheduled',
        interviewers: config.interviewers ? config.interviewers.split(',').map((i: string) => i.trim()) : [],
        location: config.location || null,
        meeting_platform: config.meetingPlatform || 'Zoom',
        meeting_link: config.meetingLink || null
      })
      .select()
      .single();
    
    if (interviewError) {
      console.error('Error creating interview:', interviewError);
      throw new Error('Failed to schedule interview');
    }
    
    // Create timeline entry
    await supabase
      .from('candidate_timeline')
      .insert({
        candidate_id: candidateId,
        user_id: user.id,
        event_type: 'interview',
        title: `${config.interviewType || 'Technical'} Interview Scheduled`,
        description: `Interview scheduled for ${new Date(interviewDate).toLocaleDateString()} at ${new Date(interviewDate).toLocaleTimeString()}`,
        event_date: interviewDate.toISOString(),
        status: 'upcoming'
      });
    
    // Create calendar event if needed
    if (config.sendCalendarInvite) {
      await supabase
        .from('events')
        .insert({
          title: `Interview: ${candidate?.name} - ${config.interviewType || 'Technical'}`,
          description: `Interview with ${candidate?.name} for ${config.interviewType || 'Technical'} assessment`,
          start_time: interviewDate.toISOString(),
          end_time: new Date(interviewDate.getTime() + (parseInt(config.duration) || 60) * 60000).toISOString(),
          location: config.location || null,
          meeting_link: config.meetingLink || null,
          event_type: 'interview',
          priority: 'high',
          category: 'recruitment',
          user_id: user.id
        });
    }
    
    return {
      scheduled: true,
      candidateId,
      candidateName: candidate?.name,
      interviewId: interview?.id,
      interviewType: config.interviewType || 'Technical Interview',
      duration: config.duration || 60,
      interviewDate: interviewDate.toISOString(),
      calendarInviteSent: config.sendCalendarInvite,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error scheduling interview:', error);
    return {
      scheduled: false,
      candidateId,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
}

async function sendAssessment(candidateId: string, config: any): Promise<any> {
  console.log(`🔹 Sending ${config.assessmentType} assessment to candidate ${candidateId}`);
  console.log(`🔹 Time limit: ${config.timeLimit} minutes`);
  console.log(`🔹 Require webcam: ${config.requireWebcam}`);
  
  // In a real implementation, this would send an assessment invitation
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Calculate due date (5 days from now)
  const dueDate = new Date();
  dueDate.setDate(dueDate.getDate() + 5);
  
  return {
    sent: true,
    candidateId,
    assessmentType: config.assessmentType,
    timeLimit: config.timeLimit,
    requireWebcam: config.requireWebcam,
    dueDate: dueDate.toISOString(),
    timestamp: new Date().toISOString()
  };
}

async function notifyTeam(channel: string, teamMembers: string, message: string): Promise<any> {
  console.log(`🔹 Notifying ${teamMembers} via ${channel}: ${message}`);
  
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // In a real implementation, this would send notifications to different channels
    // For now, we'll create a message in the database
    const { data: messageData, error: messageError } = await supabase
      .from('messages')
      .insert({
        sender_name: 'Workflow Notification',
        sender_email: '<EMAIL>',
        sender_role: 'System',
        content: `Team notification: ${message}\nRecipients: ${teamMembers}\nChannel: ${channel}`,
        status: 'unread',
        user_id: user.id
      })
      .select()
      .single();
    
    if (messageError) {
      console.error('Error creating notification message:', messageError);
      throw new Error('Failed to send notification');
    }
    
    return { 
      sent: true, 
      channel, 
      teamMembers,
      message,
      messageId: messageData?.id,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error notifying team:', error);
    return { 
      sent: false, 
      channel, 
      teamMembers,
      message,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
}

async function updateCandidateStatus(candidateId: string, newStatus: string, statusNote?: string): Promise<any> {
  console.log(`🔹 Updating status for candidate ${candidateId} to ${newStatus}`);
  console.log(`🔹 Status note: ${statusNote}`);
  
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Get candidate details
    const { data: candidate, error: candidateError } = await supabase
      .from('candidates')
      .select('name')
      .eq('id', candidateId)
      .single();
    
    if (candidateError) {
      console.error('Error fetching candidate:', candidateError);
      throw new Error('Failed to fetch candidate details');
    }
    
    // In a real implementation, this would update the candidate status in the database
    // For now, we'll just create a timeline entry
    const { data: timeline, error: timelineError } = await supabase
      .from('candidate_timeline')
      .insert({
        candidate_id: candidateId,
        user_id: user.id,
        event_type: 'status',
        title: `Status Updated: ${newStatus}`,
        description: statusNote || `Candidate status updated to ${newStatus}`,
        event_date: new Date().toISOString(),
        status: 'completed'
      })
      .select()
      .single();
    
    if (timelineError) {
      console.error('Error creating timeline entry:', timelineError);
      throw new Error('Failed to update candidate status');
    }
    
    return { 
      updated: true, 
      candidateId,
      candidateName: candidate?.name, 
      newStatus,
      statusNote,
      timelineId: timeline?.id,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error updating candidate status:', error);
    return { 
      updated: false, 
      candidateId, 
      newStatus,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
}

async function addToTalentPool(candidateId: string, poolName: string, tags?: string): Promise<any> {
  console.log(`🔹 Adding candidate ${candidateId} to talent pool ${poolName}`);
  console.log(`🔹 Tags: ${tags}`);
  
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Get candidate details
    const { data: candidate, error: candidateError } = await supabase
      .from('candidates')
      .select('name, tags')
      .eq('id', candidateId)
      .single();
    
    if (candidateError) {
      console.error('Error fetching candidate:', candidateError);
      throw new Error('Failed to fetch candidate details');
    }
    
    // Parse tags
    const tagArray = tags ? tags.split(',').map(t => t.trim()) : [];
    
    // Update candidate tags
    const existingTags = candidate?.tags || [];
    const newTags = [...new Set([...existingTags, ...tagArray, poolName])];
    
    const { error: updateError } = await supabase
      .from('candidates')
      .update({
        tags: newTags
      })
      .eq('id', candidateId);
    
    if (updateError) {
      console.error('Error updating candidate tags:', updateError);
      throw new Error('Failed to add candidate to talent pool');
    }
    
    // Create timeline entry
    await supabase
      .from('candidate_timeline')
      .insert({
        candidate_id: candidateId,
        user_id: user.id,
        event_type: 'general',
        title: `Added to Talent Pool: ${poolName}`,
        description: `Candidate added to ${poolName} talent pool${tagArray.length > 0 ? ` with tags: ${tagArray.join(', ')}` : ''}`,
        event_date: new Date().toISOString(),
        status: 'completed'
      });
    
    return { 
      added: true, 
      candidateId,
      candidateName: candidate?.name,
      poolName,
      tags: newTags,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error adding to talent pool:', error);
    return { 
      added: false, 
      candidateId, 
      poolName,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
}

async function sendMessage(candidateId: string, template: string, messageContent?: string): Promise<any> {
  console.log(`🔹 Sending message to candidate ${candidateId} using template ${template}`);
  console.log(`🔹 Message content: ${messageContent}`);
  
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Get candidate details
    const { data: candidate, error: candidateError } = await supabase
      .from('candidates')
      .select('name, email')
      .eq('id', candidateId)
      .single();
    
    if (candidateError) {
      console.error('Error fetching candidate:', candidateError);
      throw new Error('Failed to fetch candidate details');
    }
    
    // Get template if specified
    let templateContent = messageContent || '';
    if (template && !messageContent) {
      const { data: templates, error: templateError } = await supabase
        .from('message_templates')
        .select('content')
        .eq('template_category', template)
        .limit(1);
      
      if (!templateError && templates && templates.length > 0) {
        templateContent = templates[0].content;
      }
    }
    
    // Create message record
    const { data: message, error: messageError } = await supabase
      .from('messages')
      .insert({
        sender_name: 'Workflow Automation',
        sender_email: '<EMAIL>',
        sender_role: 'System',
        content: templateContent || `Message to ${candidate?.name} using template: ${template}`,
        status: 'unread',
        user_id: user.id
      })
      .select()
      .single();
    
    if (messageError) {
      console.error('Error creating message:', messageError);
      throw new Error('Failed to send message');
    }
    
    // Create timeline entry
    await supabase
      .from('candidate_timeline')
      .insert({
        candidate_id: candidateId,
        user_id: user.id,
        event_type: 'general',
        title: 'Message Sent',
        description: `Message sent to candidate using template: ${template}`,
        event_date: new Date().toISOString(),
        status: 'completed'
      });
    
    return { 
      sent: true, 
      candidateId,
      candidateName: candidate?.name,
      candidateEmail: candidate?.email,
      template,
      messageContent: templateContent,
      messageId: message?.id,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error sending message:', error);
    return { 
      sent: false, 
      candidateId, 
      template,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
}