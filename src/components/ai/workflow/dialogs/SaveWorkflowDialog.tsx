
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useEffect } from "react";

interface SaveWorkflowDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (name: string, description: string) => Promise<void>;
  isSaving?: boolean;
  isLoading?: boolean;
  isEditing?: boolean;
  currentName?: string;
  currentDescription?: string;
  initialData?: {
    name: string;
    description: string;
  };
}

export function SaveWorkflowDialog({ 
  isOpen, 
  onClose, 
  onSave, 
  isSaving, 
  isLoading, 
  isEditing, 
  currentName, 
  currentDescription,
  initialData 
}: SaveWorkflowDialogProps) {
  const [name, setName] = useState(initialData?.name || currentName || "");
  const [description, setDescription] = useState(initialData?.description || currentDescription || "");

  // Update form when initialData changes
  useEffect(() => {
    if (initialData) {
      setName(initialData.name);
      setDescription(initialData.description || "");
    } else if (currentName) {
      setName(currentName);
      setDescription(currentDescription || "");
    }
  }, [currentName, currentDescription, initialData]);

  // Reset form when dialog closes
  useEffect(() => {
    if (!isOpen && !currentName && !initialData) {
      setName("");
      setDescription("");
    }
  }, [isOpen, currentName, initialData]);

  const handleSave = async () => {
    if (name.trim()) {
      await onSave(name.trim(), description.trim());
    }
  };

  const loading = isSaving || isLoading;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{currentName || initialData?.name ? "Update Workflow" : "Save Workflow"}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="name">Workflow Name</Label>
            <Input
              id="name"
              placeholder="Enter workflow name"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              placeholder="Enter workflow description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={!name || loading}>
            {loading ? "Saving..." : (currentName || initialData?.name) ? "Update Workflow" : "Save Workflow"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
