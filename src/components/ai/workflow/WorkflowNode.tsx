import React from 'react';
import { <PERSON><PERSON>, Position } from "@xyflow/react";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { type WorkflowNodeData } from "./WorkflowValidation";
import * as Icons from "lucide-react";

interface WorkflowNodeProps {
  data: WorkflowNodeData & { originalId?: string };
  selected?: boolean;
}

export function WorkflowNode({ data, selected }: WorkflowNodeProps) {
  const getIcon = () => {
    const nodeId = data.originalId || data.id;
    switch (nodeId) {
      case 'new-application':
        return <Icons.UserPlus className="w-4 h-4" />;
      case 'application-status':
        return <Icons.AlertCircle className="w-4 h-4" />;
      case 'scheduled-trigger':
        return <Icons.Timer className="w-4 h-4" />;
      case 'send-email':
        return <Icons.Mail className="w-4 h-4" />;
      case 'schedule-interview':
        return <Icons.Calendar className="w-4 h-4" />;
      case 'ai-screen':
        return <Icons.BrainCircuit className="w-4 h-4" />;
      case 'send-assessment':
        return <Icons.FileCheck className="w-4 h-4" />;
      case 'notify-team':
        return <Icons.Bell className="w-4 h-4" />;
      case 'experience-check':
        return <Icons.Briefcase className="w-4 h-4" />;
      case 'education-check':
        return <Icons.GraduationCap className="w-4 h-4" />;
      case 'skills-match':
        return <Icons.CheckCircle className="w-4 h-4" />;
      case 'location-check':
        return <Icons.Building2 className="w-4 h-4" />;
      case 'update-status':
        return <Icons.FileCheck className="w-4 h-4" />;
      case 'add-to-pool':
        return <Icons.Users className="w-4 h-4" />;
      case 'send-message':
        return <Icons.MessageSquare className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const handleStyle = {
    width: '10px',
    height: '10px',
    background: '#374151',
    border: '2px solid white',
  };

  // Add special handles for condition nodes
  const isCondition = data.type === 'condition' || data.category === 'conditions';
  return (
    <div className="w-full h-full cursor-grab">
      {/* Source handles with unique IDs */}
      <Handle 
        type="source" 
        position={Position.Top} 
        id="source-top"
        style={handleStyle}
      />
      <Handle 
        type="source" 
        position={Position.Right}
        id="source-right"
        style={handleStyle}
      />
      <Handle 
        type="source" 
        position={Position.Bottom}
        id="source-bottom"
        style={handleStyle}
      />
      <Handle 
        type="source" 
        position={Position.Left}
        id="source-left"
        style={handleStyle}
      />
      {/* Conditional source handles for true/false paths */}
      {isCondition && (
        <>
          <Handle 
            type="source" 
            position={Position.Right}
            id="source-true"
            style={{...handleStyle, background: '#10b981'}}
          />
          <Handle 
            type="source" 
            position={Position.Bottom}
            id="source-false"
            style={{...handleStyle, background: '#ef4444'}}
          />
        </>
      )}

      {/* Target handles with unique IDs */}
      <Handle 
        type="target" 
        position={Position.Top}
        id="target-top"
        style={handleStyle}
      />
      <Handle 
        type="target" 
        position={Position.Right}
        id="target-right"
        style={handleStyle}
      />
      <Handle 
        type="target" 
        position={Position.Bottom}
        id="target-bottom"
        style={handleStyle}
      />
      <Handle 
        type="target" 
        position={Position.Left}
        id="target-left"
        style={handleStyle}
      />

      <Card 
        className={cn(
          "w-[200px] transition-shadow border select-none",
          selected ? "border-primary shadow-lg" : "shadow-sm",
          isCondition ? "border-yellow-500" : ""
        )}
      >
        <div className="p-3">
          <div className="flex items-center gap-2 text-sm font-medium">
            {getIcon()}
            <span>{data.label}</span>
          </div>
          <p className="text-xs text-muted-foreground mt-1">{data.description}</p>
          {isCondition && (
            <div className="mt-2 flex justify-between text-xs">
              <span className="text-green-500">True →</span>
              <span className="text-red-500">False ↓</span>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}