import { useState, useEffect } from "react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Loader2, 
  Play, 
  Pause,
  ArrowRight
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { WorkflowExecutionEngine } from "./WorkflowExecutionEngine";

interface WorkflowExecutorProps {
  workflowId: string;
  onComplete?: () => void;
  context?: Record<string, any> | string;
  initialContext?: string;
}

export function WorkflowExecutor({ workflowId, onComplete, context = {} }: WorkflowExecutorProps) {
  const { user } = useAuth();
  const [workflow, setWorkflow] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRunning, setIsRunning] = useState(false);
  const [currentNodeIndex, setCurrentNodeIndex] = useState(0);
  const [executionPath, setExecutionPath] = useState<string[]>([]);
  const [executionLogs, setExecutionLogs] = useState<{
    nodeId: string;
    status: 'success' | 'error' | 'pending' | 'skipped';
    message: string;
    timestamp: Date;
    data?: any;
  }[]>([]);
  const [progress, setProgress] = useState(0);
  const [executionContext, setExecutionContext] = useState<Record<string, any>>(
    typeof context === 'string' && context.length > 0 ? 
      (() => {
        try { return JSON.parse(context); } 
        catch { return {}; }
      })() : 
      (context || {})
  );

  useEffect(() => {
    const fetchWorkflow = async () => {
      if (!workflowId || !user) return;
      
      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('workflow_configurations')
          .select('*')
          .eq('id', workflowId)
          .eq('created_by', user.id)
          .single();
          
        if (error) throw error;
        setWorkflow(data);
      } catch (error) {
        console.error('Error fetching workflow:', error);
        toast.error('Failed to load workflow');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchWorkflow();
  }, [workflowId, user]);
  
  // Parse context if it's a string
  useEffect(() => {
    const parseContext = () => {
      if (!context) return;
      
      const contextToUse = typeof context === 'string' ? context : JSON.stringify(context);
      
      try {
        const parsedContext = typeof contextToUse === 'string' ? JSON.parse(contextToUse) : contextToUse;
        setExecutionContext(prev => ({
          ...prev,
          ...parsedContext
        }));
      } catch (error) {
        console.error('Error parsing execution context:', error);
      }
    };
    
    parseContext();
  }, [context]);

  const executeWorkflow = async () => {
    if (!workflow || !workflow.config || !workflow.config.nodes || !workflow.config.edges || !user) {
      toast.error('Invalid workflow configuration');
      return;
    }
    
    setIsRunning(true);
    setExecutionPath([]);
    setExecutionLogs([]);
    setProgress(0);
    setCurrentNodeIndex(0);
    
    try {
      // Create a new workflow execution engine
      const engine = new WorkflowExecutionEngine(
        workflowId,
        user.id,
        workflow.config.nodes,
        workflow.config.edges,
        executionContext
      );
      
      try {
        // Execute the workflow
        const result = await engine.execute();
        
        // Update state with execution results
        setExecutionPath(result.executionPath || []);
        setExecutionLogs(result.logs || []);
        setProgress(100);
        
        if (result.success) {
          toast.success('Workflow executed successfully');
        } else {
          toast.error(`Workflow execution failed: ${result.error?.message}`);
        }
      } catch (error) {
        console.error('Error during workflow execution:', error);
        toast.error('An unexpected error occurred during workflow execution');
        setProgress(100);
      }
    } catch (error) {
      console.error('Workflow execution error:', error);
      toast.error(`Workflow execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsRunning(false);
      onComplete?.();
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!workflow) {
    return (
      <div className="text-center p-8">
        <AlertTriangle className="h-8 w-8 text-warning mx-auto mb-4" />
        <h3 className="text-lg font-medium">Workflow Not Found</h3>
        <p className="text-muted-foreground">The requested workflow could not be found or you don't have permission to access it.</p>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Workflow Execution: {workflow.name}</span>
          <Button 
            onClick={executeWorkflow} 
            disabled={isRunning}
            className="ml-auto"
          >
            {isRunning ? (
              <>
                <Pause className="mr-2 h-4 w-4" />
                Running...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" />
                Run Workflow
              </>
            )}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isRunning && (
          <div className="mb-6">
            <div className="flex justify-between mb-2">
              <span className="text-sm font-medium">Execution Progress</span>
              <span className="text-sm">{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Execution Path</h3>
            {executionPath.length > 0 ? (
              <div className="flex flex-wrap items-center gap-2">
                {executionPath.map((nodeId, index) => {
                  const node = workflow.config.nodes.find((n: any) => n.id === nodeId);
                  return (
                    <div key={`path-${nodeId}`} className="flex items-center">
                      <Badge variant="outline" className="bg-accent/50">
                        {node?.data.label || nodeId}
                      </Badge>
                      {index < executionPath.length - 1 && (
                        <ArrowRight className="h-4 w-4 mx-1" />
                      )}
                    </div>
                  );
                })}
              </div>
            ) : (
              <p className="text-muted-foreground text-sm">No execution path yet. Run the workflow to see the execution path.</p>
            )}
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Execution Logs</h3>
            <ScrollArea className="h-[300px] border rounded-md p-4">
              {executionLogs.length > 0 ? (
                <div className="space-y-2">
                  {executionLogs.map((log, index) => (
                    <div key={`log-${index}-${log.nodeId}`} className="flex items-start gap-2 pb-2 border-b last:border-0">
                      {log.status === 'success' && <CheckCircle className="h-5 w-5 text-success shrink-0 mt-0.5" />}
                      {log.status === 'error' && <XCircle className="h-5 w-5 text-destructive shrink-0 mt-0.5" />}
                      {log.status === 'pending' && <Loader2 className="h-5 w-5 animate-spin text-primary shrink-0 mt-0.5" />}
                      {log.status === 'skipped' && <AlertTriangle className="h-5 w-5 text-warning shrink-0 mt-0.5" />}
                      <div>
                        <p className="text-sm">{log.message}</p>
                        <p className="text-xs text-muted-foreground">
                          {log.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-sm">No execution logs yet. Run the workflow to see logs.</p>
              )}
            </ScrollArea>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
