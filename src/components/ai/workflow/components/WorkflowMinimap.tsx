import { MiniMap } from "@xyflow/react";

interface WorkflowMinimapProps {
  isExpanded: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}

export function WorkflowMinimap({ isExpanded, onMouseEnter, onMouseLeave }: WorkflowMinimapProps) {
  return (
    <div 
      className={`absolute bottom-4 right-4 transition-all duration-300 ease-in-out
        ${isExpanded ? "scale-100" : "scale-50 hover:scale-75"}
        origin-bottom-right cursor-pointer`}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <MiniMap 
        style={{
          backgroundColor: "#f8f9fa",
          border: "1px solid #e9ecef",
          borderRadius: "8px",
        }}
        zoomable
        pannable
      />
    </div>
  );
}