import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  Activity, 
  CheckCircle, 
  XCircle, 
  Clock, 
  TrendingUp,
  TrendingDown,
  AlertCircle,
  BarChart3,
  Hash
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { formatDistanceToNow } from "date-fns";
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from "recharts";

interface WorkflowMetricsProps {
  workflowId?: string;
  showAllWorkflows?: boolean;
}

interface MetricsSummary {
  totalExecutions: number;
  successRate: number;
  averageDuration: number;
  failedNodes: number;
  recentFailures: Array<{
    executionId: string;
    timestamp: Date;
    errorMessage: string;
  }>;
}

interface WorkflowStats {
  workflowId: string;
  workflowName: string;
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  avgDurationMs: number;
  minDurationMs: number;
  maxDurationMs: number;
  totalFailedNodes: number;
  avgSuccessRate: number;
}

const COLORS = {
  success: "#10b981",
  failure: "#ef4444",
  warning: "#f59e0b",
  primary: "#3b82f6"
};

export function WorkflowMetrics({ workflowId, showAllWorkflows = false }: WorkflowMetricsProps) {
  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState<MetricsSummary | null>(null);
  const [workflowStats, setWorkflowStats] = useState<WorkflowStats[]>([]);
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d' | 'all'>('7d');
  const [executionTrend, setExecutionTrend] = useState<any[]>([]);

  useEffect(() => {
    loadMetrics();
  }, [workflowId, showAllWorkflows, timeRange]);

  const loadMetrics = async () => {
    try {
      setLoading(true);

      // Load workflow statistics
      const { data: stats, error: statsError } = await supabase
        .from('workflow_execution_stats')
        .select('*');

      if (statsError) throw statsError;

      let filteredStats = stats || [];
      if (!showAllWorkflows && workflowId) {
        filteredStats = filteredStats.filter(s => s.workflow_id === workflowId);
      }

      setWorkflowStats(filteredStats);

      // Calculate summary metrics
      if (filteredStats.length > 0) {
        const totalExecutions = filteredStats.reduce((sum, s) => sum + (s.total_executions || 0), 0);
        const successfulExecutions = filteredStats.reduce((sum, s) => sum + (s.successful_executions || 0), 0);
        const totalDuration = filteredStats.reduce((sum, s) => sum + (s.avg_duration_ms || 0) * (s.total_executions || 0), 0);
        const totalFailedNodes = filteredStats.reduce((sum, s) => sum + (s.total_failed_nodes || 0), 0);

        setMetrics({
          totalExecutions,
          successRate: totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0,
          averageDuration: totalExecutions > 0 ? totalDuration / totalExecutions : 0,
          failedNodes: totalFailedNodes,
          recentFailures: []
        });
      }

      // Load execution trend data
      await loadExecutionTrend();

    } catch (error) {
      console.error('Error loading metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadExecutionTrend = async () => {
    try {
      let query = supabase
        .from('workflow_metrics')
        .select('created_at, successful_nodes, failed_nodes, total_nodes_executed');

      if (!showAllWorkflows && workflowId) {
        query = query.eq('workflow_id', workflowId);
      }

      // Apply time range filter
      const now = new Date();
      let startDate: Date;
      switch (timeRange) {
        case '24h':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(0);
      }

      if (timeRange !== 'all') {
        query = query.gte('created_at', startDate.toISOString());
      }

      const { data, error } = await query;
      if (error) throw error;

      // Group by date and calculate daily metrics
      const groupedData = new Map<string, { date: string; success: number; failure: number; total: number }>();

      (data || []).forEach(metric => {
        const date = new Date(metric.created_at).toLocaleDateString();
        const existing = groupedData.get(date) || { date, success: 0, failure: 0, total: 0 };
        
        existing.success += metric.successful_nodes || 0;
        existing.failure += metric.failed_nodes || 0;
        existing.total += metric.total_nodes_executed || 0;
        
        groupedData.set(date, existing);
      });

      const trendData = Array.from(groupedData.values())
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        .map(item => ({
          ...item,
          successRate: item.total > 0 ? (item.success / item.total * 100).toFixed(1) : 0
        }));

      setExecutionTrend(trendData);

    } catch (error) {
      console.error('Error loading execution trend:', error);
    }
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="text-center">Loading metrics...</div>
        </CardContent>
      </Card>
    );
  }

  const pieData = metrics ? [
    { name: 'Successful', value: metrics.successRate, color: COLORS.success },
    { name: 'Failed', value: 100 - metrics.successRate, color: COLORS.failure }
  ] : [];

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Executions</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.totalExecutions || 0}</div>
            <p className="text-xs text-muted-foreground">
              Across {showAllWorkflows ? 'all workflows' : 'this workflow'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.successRate.toFixed(1)}%</div>
            <Progress 
              value={metrics?.successRate || 0} 
              className="mt-2"
              indicatorClassName={metrics?.successRate >= 90 ? "bg-green-500" : metrics?.successRate >= 70 ? "bg-yellow-500" : "bg-red-500"}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(metrics?.averageDuration || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Per execution
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed Nodes</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.failedNodes || 0}</div>
            <p className="text-xs text-muted-foreground">
              Total node failures
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Detailed Metrics
            </CardTitle>
            <Tabs value={timeRange} onValueChange={(v) => setTimeRange(v as any)}>
              <TabsList>
                <TabsTrigger value="24h">24h</TabsTrigger>
                <TabsTrigger value="7d">7d</TabsTrigger>
                <TabsTrigger value="30d">30d</TabsTrigger>
                <TabsTrigger value="all">All</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="trend" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="trend">Execution Trend</TabsTrigger>
              <TabsTrigger value="distribution">Success Distribution</TabsTrigger>
              <TabsTrigger value="workflows">By Workflow</TabsTrigger>
            </TabsList>

            {/* Execution Trend */}
            <TabsContent value="trend" className="space-y-4">
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={executionTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="successRate" 
                      stroke={COLORS.primary} 
                      name="Success Rate (%)"
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </TabsContent>

            {/* Success Distribution */}
            <TabsContent value="distribution" className="space-y-4">
              <div className="h-[300px] flex items-center justify-center">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, value }) => `${name}: ${value}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </TabsContent>

            {/* By Workflow */}
            <TabsContent value="workflows" className="space-y-4">
              <div className="space-y-4">
                {workflowStats.map((stat) => (
                  <Card key={stat.workflowId}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{stat.workflowName}</h4>
                        <Badge 
                          variant={stat.avgSuccessRate >= 90 ? "default" : stat.avgSuccessRate >= 70 ? "secondary" : "destructive"}
                        >
                          {(stat.avgSuccessRate * 100).toFixed(1)}% success
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Executions</p>
                          <p className="font-medium">{stat.totalExecutions}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Avg Duration</p>
                          <p className="font-medium">{formatDuration(stat.avgDurationMs)}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Success/Failed</p>
                          <p className="font-medium">
                            <span className="text-green-600">{stat.successfulExecutions}</span>
                            {" / "}
                            <span className="text-red-600">{stat.failedExecutions}</span>
                          </p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Failed Nodes</p>
                          <p className="font-medium">{stat.totalFailedNodes}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
