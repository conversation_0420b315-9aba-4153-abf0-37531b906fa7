import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { 
  Sparkles, 
  Lightbulb, 
  CheckCircle, 
  XCircle, 
  Loader2 
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { useWorkflowConfigurations } from "@/hooks/useWorkflowConfigurations";
import { generateText } from "@/utils/gemini";

interface Suggestion {
  id: string;
  title: string;
  description: string;
  type: 'improvement' | 'new' | 'warning';
  status: 'pending' | 'implemented' | 'dismissed';
  workflowId?: string;
}

export function WorkflowSuggestions() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { data: existingWorkflows = [] } = useWorkflowConfigurations();
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  
  useEffect(() => {
    // Load suggestions from local storage for demo purposes
    const loadSuggestions = () => {
      setIsLoading(true);
      try {
        const savedSuggestions = localStorage.getItem('workflow_suggestions');
        if (savedSuggestions) {
          setSuggestions(JSON.parse(savedSuggestions));
        } else {
          // Generate initial suggestions if none exist
          generateSuggestions();
        }
      } catch (error) {
        console.error('Error loading suggestions:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadSuggestions();
  }, [user]);
  
  const extractJsonFromResponse = (response: string): string => {
    // Remove markdown code block delimiters
    let cleanResponse = response.trim();
    if (cleanResponse.startsWith('```json')) {
      cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
    } else if (cleanResponse.startsWith('```')) {
      cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
    }
    
    // Find the first occurrence of '[' and the last occurrence of ']'
    const firstBracket = cleanResponse.indexOf('[');
    const lastBracket = cleanResponse.lastIndexOf(']');
    
    if (firstBracket !== -1 && lastBracket !== -1 && lastBracket > firstBracket) {
      // Extract only the JSON array portion
      const jsonSubstring = cleanResponse.substring(firstBracket, lastBracket + 1);
      
      // Validate that the extracted substring is valid JSON
      try {
        JSON.parse(jsonSubstring);
        return jsonSubstring;
      } catch (parseError) {
        console.warn('Extracted JSON substring is invalid:', parseError);
        // Return empty array as fallback for malformed JSON
        return '[]';
      }
    }
    
    // If no array brackets found, try to find object brackets
    const firstBrace = cleanResponse.indexOf('{');
    const lastBrace = cleanResponse.lastIndexOf('}');
    
    if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
      // Extract only the JSON object portion
      const jsonSubstring = cleanResponse.substring(firstBrace, lastBrace + 1);
      
      // Validate that the extracted substring is valid JSON
      try {
        JSON.parse(jsonSubstring);
        return `[${jsonSubstring}]`; // Wrap single object in array
      } catch (parseError) {
        console.warn('Extracted JSON object is invalid:', parseError);
        // Return empty array as fallback for malformed JSON
        return '[]';
      }
    }
    
    // Return empty array as fallback if no valid JSON structure found
    return '[]';
  };
  
  const getDefaultSuggestions = (): Suggestion[] => [
    {
      id: crypto.randomUUID(),
      title: "Optimize Candidate Screening",
      description: "Implement automated screening workflows to reduce manual review time and improve candidate quality assessment.",
      type: 'improvement' as const,
      status: 'pending' as const
    },
    {
      id: crypto.randomUUID(),
      title: "Add Interview Scheduling Automation",
      description: "Create a workflow that automatically schedules interviews based on candidate availability and interviewer calendars.",
      type: 'new' as const,
      status: 'pending' as const
    },
    {
      id: crypto.randomUUID(),
      title: "Implement Follow-up Reminders",
      description: "Set up automated reminders for candidate follow-ups to ensure no opportunities are missed.",
      type: 'new' as const,
      status: 'pending' as const
    }
  ];
  
  const generateSuggestions = async () => {
    setIsGenerating(true);
    try {
      // Get existing workflow data to provide context to Gemini
      const workflowData = existingWorkflows.map(w => ({
        id: w.id,
        name: w.name,
        description: w.description,
        isActive: w.is_active
      }));
      
      // Create a prompt for Gemini
      const prompt = `
Analyze the following recruitment workflow configurations and suggest improvements or new workflows:

EXISTING WORKFLOWS:
${JSON.stringify(workflowData, null, 2)}

Based on these existing workflows, suggest:
1. Improvements to existing workflows
2. New workflows that would complement the existing ones
3. Warnings about potential issues in the current setup

IMPORTANT: Respond ONLY with a valid JSON array. Do not include any explanatory text before or after the JSON.

Format your response as a JSON array with this exact structure:
[
  {
    "id": "unique-id-1",
    "title": "Suggestion title",
    "description": "Detailed description of the suggestion",
    "type": "improvement",
    "status": "pending",
    "workflowId": "id-of-related-workflow-if-applicable"
  }
]

Ensure:
- Each suggestion has a unique ID
- Type is one of: "improvement", "new", "warning"
- Status is always "pending"
- Description is properly escaped for JSON
- No trailing commas
- Valid JSON syntax throughout
`;

      const systemPrompt = "You are an expert in recruitment automation and workflow optimization. Respond only with valid JSON. Do not include any explanatory text outside the JSON structure.";
      
      // Call Gemini API
      const response = await generateText(prompt, systemPrompt);
      
      // Parse the response with improved error handling
      let newSuggestions: Suggestion[] = [];
      try {
        // Extract JSON from the response
        const jsonString = extractJsonFromResponse(response);
        
        // Parse the extracted JSON
        const parsedResponse = JSON.parse(jsonString);
        
        // Ensure the response is an array
        newSuggestions = Array.isArray(parsedResponse) ? parsedResponse : [parsedResponse];
        
        // Validate and clean up each suggestion
        newSuggestions = newSuggestions
          .filter(s => s && typeof s === 'object')
          .map(s => ({
            id: s.id || crypto.randomUUID(),
            title: String(s.title || 'Untitled Suggestion'),
            description: String(s.description || 'No description provided'),
            type: ['improvement', 'new', 'warning'].includes(s.type) ? s.type : 'improvement',
            status: 'pending' as const,
            workflowId: s.workflowId || undefined
          }));
        
        // Filter out suggestions for non-existent workflows
        newSuggestions = newSuggestions.filter(s =>
          !s.workflowId || existingWorkflows.some(w => w.id === s.workflowId)
        );
        
      } catch (parseError) {
        console.error('Error parsing Gemini response:', parseError);
        console.error('Raw response:', response);
        
        // Use default suggestions if parsing fails
        newSuggestions = [];
      }
      
      // If no valid suggestions were generated or parsed, use default suggestions
      if (newSuggestions.length === 0) {
        newSuggestions = getDefaultSuggestions();
        
        toast({
          title: "Using Default Suggestions",
          description: "AI suggestions couldn't be generated, showing default recommendations instead.",
          variant: "default"
        });
      } else {
        toast({
          title: "Suggestions Generated",
          description: `Generated ${newSuggestions.length} workflow suggestions.`
        });
      }
      
      setSuggestions(newSuggestions);
      localStorage.setItem('workflow_suggestions', JSON.stringify(newSuggestions));
      
    } catch (error) {
      console.error('Error generating suggestions:', error);
      
      // Provide fallback suggestions even on complete failure
      const fallbackSuggestions = getDefaultSuggestions();
      
      setSuggestions(fallbackSuggestions);
      localStorage.setItem('workflow_suggestions', JSON.stringify(fallbackSuggestions));
      
      toast({
        title: "Error Generating Suggestions",
        description: "Failed to generate AI suggestions. Showing basic recommendations instead.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };
  
  const handleImplementSuggestion = (suggestion: Suggestion) => {
    // Update suggestion status
    const updatedSuggestions = suggestions.map(s => 
      s.id === suggestion.id ? { ...s, status: 'implemented' as const } : s
    );
    
    setSuggestions(updatedSuggestions);
    localStorage.setItem('workflow_suggestions', JSON.stringify(updatedSuggestions));
    
    // If the suggestion has a workflowId, navigate to edit that workflow
    if (suggestion.workflowId) {
      navigate(`/ai-workflows?edit=${suggestion.workflowId}`);
    } else {
      // Otherwise, navigate to create a new workflow
      navigate('/ai-workflows?tab=canvas');
    }
    
    toast({
      title: "Suggestion Implemented",
      description: "You're now being redirected to implement this suggestion."
    });
  };
  
  const handleDismissSuggestion = (suggestion: Suggestion) => {
    // Update suggestion status
    const updatedSuggestions = suggestions.map(s => 
      s.id === suggestion.id ? { ...s, status: 'dismissed' as const } : s
    );
    
    setSuggestions(updatedSuggestions);
    localStorage.setItem('workflow_suggestions', JSON.stringify(updatedSuggestions));
    
    toast({
      title: "Suggestion Dismissed",
      description: "The suggestion has been dismissed and won't be shown again."
    });
  };
  
  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'improvement':
        return <Sparkles className="h-5 w-5 text-blue-500" />;
      case 'new':
        return <Lightbulb className="h-5 w-5 text-yellow-500" />;
      case 'warning':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Lightbulb className="h-5 w-5" />;
    }
  };
  
  const getSuggestionBadge = (type: string) => {
    switch (type) {
      case 'improvement':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            Improvement
          </Badge>
        );
      case 'new':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
            New Workflow
          </Badge>
        );
      case 'warning':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700">
            Warning
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {type}
          </Badge>
        );
    }
  };
  
  const activeSuggestions = suggestions.filter(s => s.status === 'pending');
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5" />
          AI Workflow Suggestions
        </CardTitle>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={generateSuggestions}
          disabled={isGenerating}
        >
          {isGenerating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Sparkles className="mr-2 h-4 w-4" />
              Generate Suggestions
            </>
          )}
        </Button>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : activeSuggestions.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No active suggestions. Click "Generate Suggestions" to get AI-powered recommendations for your workflows.
          </div>
        ) : (
          <ScrollArea className="h-[500px]">
            <div className="space-y-4">
              {activeSuggestions.map((suggestion) => (
                <Card key={suggestion.id} className="hover:bg-accent/5 transition-colors">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-4">
                      <div className="mt-1">
                        {getSuggestionIcon(suggestion.type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium">{suggestion.title}</h3>
                          {getSuggestionBadge(suggestion.type)}
                        </div>
                        <p className="text-sm text-muted-foreground mb-4">
                          {suggestion.description}
                        </p>
                        <div className="flex justify-end gap-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleDismissSuggestion(suggestion)}
                          >
                            <XCircle className="h-4 w-4 mr-1" />
                            Dismiss
                          </Button>
                          <Button 
                            size="sm"
                            onClick={() => handleImplementSuggestion(suggestion)}
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Implement
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}