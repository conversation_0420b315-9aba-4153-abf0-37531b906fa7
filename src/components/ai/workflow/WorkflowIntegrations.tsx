import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Linkedin, 
  Mail, 
  MessageSquare, 
  Calendar, 
  Database, 
  Globe, 
  Check, 
  X, 
  AlertTriangle,
  RefreshCw,
  Settings,
  Loader2
} from "lucide-react";
import { Badge } from "@/components/ui/badge";

export function WorkflowIntegrations() {
  const { toast } = useToast();
  const [emailConnected, setEmailConnected] = useState(false);
  const [calendarConnected, setCalendarConnected] = useState(false);
  const [slackConnected, setSlackConnected] = useState(false);
  const [linkedinConnected, setLinkedinConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState<string | null>(null);
  const [apiKey, setApiKey] = useState("");
  const [webhookUrl, setWebhookUrl] = useState("");
  const [enableWebhooks, setEnableWebhooks] = useState(true);
  
  const handleConnect = (service: string) => {
    setIsConnecting(service);
    
    // Simulate connection process
    setTimeout(() => {
      switch (service) {
        case 'email':
          setEmailConnected(true);
          toast({
            title: "Email Connected",
            description: "Your email service has been successfully connected."
          });
          break;
        case 'calendar':
          setCalendarConnected(true);
          toast({
            title: "Calendar Connected",
            description: "Your calendar service has been successfully connected."
          });
          break;
        case 'slack':
          setSlackConnected(true);
          toast({
            title: "Slack Connected",
            description: "Your Slack workspace has been successfully connected."
          });
          break;
        case 'linkedin':
          setLinkedinConnected(true);
          toast({
            title: "LinkedIn Connected",
            description: "Your LinkedIn account has been successfully connected."
          });
          break;
      }
      setIsConnecting(null);
    }, 1500);
  };
  
  const handleDisconnect = (service: string) => {
    switch (service) {
      case 'email':
        setEmailConnected(false);
        toast({
          title: "Email Disconnected",
          description: "Your email service has been disconnected."
        });
        break;
      case 'calendar':
        setCalendarConnected(false);
        toast({
          title: "Calendar Disconnected",
          description: "Your calendar service has been disconnected."
        });
        break;
      case 'slack':
        setSlackConnected(false);
        toast({
          title: "Slack Disconnected",
          description: "Your Slack workspace has been disconnected."
        });
        break;
      case 'linkedin':
        setLinkedinConnected(false);
        toast({
          title: "LinkedIn Disconnected",
          description: "Your LinkedIn account has been disconnected."
        });
        break;
    }
  };
  
  const handleSaveAPISettings = () => {
    toast({
      title: "API Settings Saved",
      description: "Your API settings have been updated successfully."
    });
  };
  
  const handleTestWebhook = () => {
    toast({
      title: "Webhook Test",
      description: "Testing webhook connection..."
    });
    
    // Simulate webhook test
    setTimeout(() => {
      toast({
        title: "Webhook Test Successful",
        description: "Your webhook endpoint is configured correctly."
      });
    }, 1500);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Workflow Integrations</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="services">
          <TabsList className="mb-4">
            <TabsTrigger value="services">Services</TabsTrigger>
            <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
            <TabsTrigger value="api">API Settings</TabsTrigger>
          </TabsList>
          
          <TabsContent value="services" className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="bg-blue-500 p-2 rounded-full">
                    <Mail className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium">Email Service</h3>
                    <p className="text-sm text-muted-foreground">
                      Connect your email service to send automated emails
                    </p>
                  </div>
                </div>
                {emailConnected ? (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
                      <Check className="h-3 w-3" />
                      Connected
                    </Badge>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleDisconnect('email')}
                    >
                      Disconnect
                    </Button>
                  </div>
                ) : (
                  <Button 
                    onClick={() => handleConnect('email')}
                    disabled={isConnecting === 'email'}
                  >
                    {isConnecting === 'email' ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Connecting...
                      </>
                    ) : (
                      'Connect'
                    )}
                  </Button>
                )}
              </div>
              
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="bg-green-500 p-2 rounded-full">
                    <Calendar className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium">Calendar</h3>
                    <p className="text-sm text-muted-foreground">
                      Connect your calendar to schedule interviews and events
                    </p>
                  </div>
                </div>
                {calendarConnected ? (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
                      <Check className="h-3 w-3" />
                      Connected
                    </Badge>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleDisconnect('calendar')}
                    >
                      Disconnect
                    </Button>
                  </div>
                ) : (
                  <Button 
                    onClick={() => handleConnect('calendar')}
                    disabled={isConnecting === 'calendar'}
                  >
                    {isConnecting === 'calendar' ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Connecting...
                      </>
                    ) : (
                      'Connect'
                    )}
                  </Button>
                )}
              </div>
              
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="bg-purple-500 p-2 rounded-full">
                    <MessageSquare className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium">Slack</h3>
                    <p className="text-sm text-muted-foreground">
                      Connect Slack to send notifications to your team
                    </p>
                  </div>
                </div>
                {slackConnected ? (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
                      <Check className="h-3 w-3" />
                      Connected
                    </Badge>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleDisconnect('slack')}
                    >
                      Disconnect
                    </Button>
                  </div>
                ) : (
                  <Button 
                    onClick={() => handleConnect('slack')}
                    disabled={isConnecting === 'slack'}
                  >
                    {isConnecting === 'slack' ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Connecting...
                      </>
                    ) : (
                      'Connect'
                    )}
                  </Button>
                )}
              </div>
              
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="bg-[#0077B5] p-2 rounded-full">
                    <Linkedin className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium">LinkedIn</h3>
                    <p className="text-sm text-muted-foreground">
                      Connect LinkedIn to post jobs and search for candidates
                    </p>
                  </div>
                </div>
                {linkedinConnected ? (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
                      <Check className="h-3 w-3" />
                      Connected
                    </Badge>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleDisconnect('linkedin')}
                    >
                      Disconnect
                    </Button>
                  </div>
                ) : (
                  <Button 
                    onClick={() => handleConnect('linkedin')}
                    disabled={isConnecting === 'linkedin'}
                  >
                    {isConnecting === 'linkedin' ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Connecting...
                      </>
                    ) : (
                      'Connect'
                    )}
                  </Button>
                )}
              </div>
            </div>
            
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-5 w-5 text-warning shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium">Integration Notice</h4>
                  <p className="text-sm text-muted-foreground">
                    Connecting to external services requires proper authentication and permissions. Some services may require additional setup in their respective platforms.
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="webhooks" className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="enable-webhooks">Enable Webhooks</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow external systems to trigger workflows via webhooks
                  </p>
                </div>
                <Switch
                  id="enable-webhooks"
                  checked={enableWebhooks}
                  onCheckedChange={setEnableWebhooks}
                />
              </div>
            </div>
            
            {enableWebhooks && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="webhook-url">Webhook URL</Label>
                  <div className="flex gap-2">
                    <Input
                      id="webhook-url"
                      value={webhookUrl}
                      onChange={(e) => setWebhookUrl(e.target.value)}
                      placeholder="https://your-webhook-endpoint.com/workflows"
                    />
                    <Button 
                      onClick={handleTestWebhook}
                      disabled={!webhookUrl}
                    >
                      Test
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Enter the URL that will receive webhook events
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label>Available Webhook Endpoints</Label>
                  <div className="space-y-2">
                    <div className="p-3 border rounded-md">
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className="font-medium">New Candidate Webhook</h4>
                          <p className="text-sm text-muted-foreground">
                            Trigger workflows when a new candidate is created
                          </p>
                        </div>
                        <Button variant="outline" size="sm" onClick={() => navigator.clipboard.writeText('https://api.example.com/webhooks/new-candidate')}>
                          Copy URL
                        </Button>
                      </div>
                      <div className="mt-2 p-2 bg-muted rounded-md text-xs font-mono">
                        https://api.example.com/webhooks/new-candidate
                      </div>
                    </div>
                    
                    <div className="p-3 border rounded-md">
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className="font-medium">Status Change Webhook</h4>
                          <p className="text-sm text-muted-foreground">
                            Trigger workflows when a candidate's status changes
                          </p>
                        </div>
                        <Button variant="outline" size="sm" onClick={() => navigator.clipboard.writeText('https://api.example.com/webhooks/status-change')}>
                          Copy URL
                        </Button>
                      </div>
                      <div className="mt-2 p-2 bg-muted rounded-md text-xs font-mono">
                        https://api.example.com/webhooks/status-change
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-muted/50 p-4 rounded-lg">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-5 w-5 text-warning shrink-0 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Webhook Security</h4>
                      <p className="text-sm text-muted-foreground">
                        Ensure your webhook endpoints are properly secured. We recommend using HTTPS and implementing authentication.
                      </p>
                    </div>
                  </div>
                </div>
              </>
            )}
          </TabsContent>
          
          <TabsContent value="api" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="api-key">API Key</Label>
              <Input
                id="api-key"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter your API key"
              />
              <p className="text-xs text-muted-foreground">
                This API key is used for all external service integrations
              </p>
            </div>
            
            <div className="space-y-2">
              <Label>Database Connection</Label>
              <div className="p-3 border rounded-md">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-medium">Supabase</h4>
                    <p className="text-sm text-muted-foreground">
                      Connected to Supabase database
                    </p>
                  </div>
                  <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
                    <Check className="h-3 w-3" />
                    Connected
                  </Badge>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>External API Connections</Label>
              <div className="space-y-2">
                <div className="p-3 border rounded-md">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Database className="h-4 w-4 text-primary" />
                      <div>
                        <h4 className="font-medium">AI Service</h4>
                        <p className="text-sm text-muted-foreground">
                          AI-powered candidate screening and matching
                        </p>
                      </div>
                    </div>
                    <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
                      <Check className="h-3 w-3" />
                      Connected
                    </Badge>
                  </div>
                </div>
                
                <div className="p-3 border rounded-md">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-primary" />
                      <div>
                        <h4 className="font-medium">External Job Boards</h4>
                        <p className="text-sm text-muted-foreground">
                          Post jobs to external job boards
                        </p>
                      </div>
                    </div>
                    <Badge variant="outline" className="bg-red-50 text-red-700 flex items-center gap-1">
                      <X className="h-3 w-3" />
                      Not Connected
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
            
            <Button onClick={handleSaveAPISettings}>
              <Settings className="mr-2 h-4 w-4" />
              Save API Settings
            </Button>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}