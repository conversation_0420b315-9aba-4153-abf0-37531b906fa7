import { useEffect, useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  AlertCircle, 
  CheckCircle, 
  AlertTriangle, 
  X,
  RefreshCw,
  FileWarning
} from "lucide-react";
import { validateWorkflow } from './WorkflowValidation';
import { cn } from '@/lib/utils';

interface WorkflowValidationOverlayProps {
  nodes: any[];
  edges: any[];
  isVisible: boolean;
  onClose: () => void;
  className?: string;
}

export function WorkflowValidationOverlay({ 
  nodes, 
  edges, 
  isVisible,
  onClose,
  className 
}: WorkflowValidationOverlayProps) {
  const [validationResult, setValidationResult] = useState<{
    valid: boolean;
    errors: string[];
  }>({ valid: true, errors: [] });
  const [isValidating, setIsValidating] = useState(false);

  useEffect(() => {
    if (isVisible) {
      validateWorkflowAsync();
    }
  }, [nodes, edges, isVisible]);

  const validateWorkflowAsync = async () => {
    setIsValidating(true);
    // Simulate async validation for better UX
    await new Promise(resolve => setTimeout(resolve, 300));
    const result = validateWorkflow(nodes, edges);
    setValidationResult(result);
    setIsValidating(false);
  };

  if (!isVisible) return null;

  const getValidationIcon = () => {
    if (isValidating) {
      return <RefreshCw className="w-5 h-5 animate-spin" />;
    }
    if (validationResult.valid) {
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    }
    return <AlertCircle className="w-5 h-5 text-red-500" />;
  };

  const getValidationStatus = () => {
    if (isValidating) return "Validating workflow...";
    if (validationResult.valid) return "Workflow is valid";
    return `Found ${validationResult.errors.length} issue${validationResult.errors.length > 1 ? 's' : ''}`;
  };

  const categorizeErrors = () => {
    const categories = {
      missing: [] as string[],
      disconnected: [] as string[],
      configuration: [] as string[],
      structural: [] as string[]
    };

    validationResult.errors.forEach(error => {
      if (error.includes('must contain')) {
        categories.missing.push(error);
      } else if (error.includes('no incoming') || error.includes('no outgoing')) {
        categories.disconnected.push(error);
      } else if (error.includes('cycle')) {
        categories.structural.push(error);
      } else {
        categories.configuration.push(error);
      }
    });

    return categories;
  };

  const errorCategories = categorizeErrors();

  return (
    <Card className={cn(
      "absolute top-4 left-4 w-96 z-50 shadow-lg",
      "bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
      className
    )}>
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          {getValidationIcon()}
          <h3 className="font-medium">Workflow Validation</h3>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="w-4 h-4" />
        </Button>
      </div>
      
      <div className="p-4">
        <div className="mb-4">
          <p className={cn(
            "text-sm font-medium",
            validationResult.valid ? "text-green-600" : "text-red-600"
          )}>
            {getValidationStatus()}
          </p>
        </div>

        {!validationResult.valid && (
          <ScrollArea className="h-[400px] pr-4">
            <div className="space-y-4">
              {errorCategories.missing.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <FileWarning className="w-4 h-4 text-orange-500" />
                    <h4 className="text-sm font-semibold">Missing Components</h4>
                  </div>
                  <div className="space-y-1">
                    {errorCategories.missing.map((error, index) => (
                      <Alert key={index} className="py-2">
                        <AlertDescription className="text-xs">
                          {error}
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                </div>
              )}

              {errorCategories.disconnected.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="w-4 h-4 text-yellow-500" />
                    <h4 className="text-sm font-semibold">Connection Issues</h4>
                  </div>
                  <div className="space-y-1">
                    {errorCategories.disconnected.map((error, index) => (
                      <Alert key={index} className="py-2">
                        <AlertDescription className="text-xs">
                          {error}
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                </div>
              )}

              {errorCategories.structural.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="w-4 h-4 text-red-500" />
                    <h4 className="text-sm font-semibold">Structural Issues</h4>
                  </div>
                  <div className="space-y-1">
                    {errorCategories.structural.map((error, index) => (
                      <Alert key={index} className="py-2" variant="destructive">
                        <AlertDescription className="text-xs">
                          {error}
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                </div>
              )}

              {errorCategories.configuration.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="w-4 h-4 text-blue-500" />
                    <h4 className="text-sm font-semibold">Configuration Issues</h4>
                  </div>
                  <div className="space-y-1">
                    {errorCategories.configuration.map((error, index) => (
                      <Alert key={index} className="py-2">
                        <AlertDescription className="text-xs">
                          {error}
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
        )}

        {validationResult.valid && (
          <Alert className="bg-green-50 border-green-200">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">All checks passed</AlertTitle>
            <AlertDescription className="text-green-700">
              Your workflow is properly configured and ready to run.
            </AlertDescription>
          </Alert>
        )}

        <div className="mt-4 flex justify-end">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={validateWorkflowAsync}
            disabled={isValidating}
          >
            <RefreshCw className={cn(
              "w-4 h-4 mr-2",
              isValidating && "animate-spin"
            )} />
            Revalidate
          </Button>
        </div>
      </div>
    </Card>
  );
}
