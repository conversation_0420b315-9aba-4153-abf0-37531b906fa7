import { useState, useEffect, useCallback } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Mail, 
  Slack, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Plus, 
  X,
  Save,
  Info
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface WorkflowAlertSettingsProps {
  workflowId: string;
  workflowName: string;
}

export function WorkflowAlertSettings({ workflowId, workflowName }: WorkflowAlertSettingsProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  // Alert configuration state
  const [alertConfig, setAlertConfig] = useState({
    enableEmail: false,
    emailAddresses: [] as string[],
    enableSlack: false,
    slackWebhookUrl: '',
    slackChannel: '',
    alertOnFailure: true,
    alertOnSuccess: false,
    alertOnDurationThreshold: false,
    durationThresholdMs: 60000, // 1 minute default
    maxAlertsPerHour: 5,
    isActive: true
  });
  
  const [newEmail, setNewEmail] = useState('');
  const [alertId, setAlertId] = useState<string | null>(null);

  const loadAlertConfiguration = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('workflow_alerts')
        .select('*')
        .eq('workflow_id', workflowId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        setAlertId(data.id);
        setAlertConfig({
          enableEmail: data.enable_email,
          emailAddresses: data.email_addresses || [],
          enableSlack: data.enable_slack,
          slackWebhookUrl: data.slack_webhook_url || '',
          slackChannel: data.slack_channel || '',
          alertOnFailure: data.alert_on_failure,
          alertOnSuccess: data.alert_on_success,
          alertOnDurationThreshold: data.alert_on_duration_threshold,
          durationThresholdMs: data.duration_threshold_ms || 60000,
          maxAlertsPerHour: data.max_alerts_per_hour,
          isActive: data.is_active
        });
      }
    } catch (error) {
      console.error('Error loading alert configuration:', error);
      toast({
        title: "Error",
        description: "Failed to load alert settings",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [workflowId, toast]);

  useEffect(() => {
    loadAlertConfiguration();
  }, [workflowId, loadAlertConfiguration]);

  const saveAlertConfiguration = async () => {
    setSaving(true);
    
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const alertData = {
        workflow_id: workflowId,
        enable_email: alertConfig.enableEmail,
        email_addresses: alertConfig.emailAddresses,
        enable_slack: alertConfig.enableSlack,
        slack_webhook_url: alertConfig.slackWebhookUrl,
        slack_channel: alertConfig.slackChannel,
        alert_on_failure: alertConfig.alertOnFailure,
        alert_on_success: alertConfig.alertOnSuccess,
        alert_on_duration_threshold: alertConfig.alertOnDurationThreshold,
        duration_threshold_ms: alertConfig.durationThresholdMs,
        max_alerts_per_hour: alertConfig.maxAlertsPerHour,
        is_active: alertConfig.isActive,
        created_by: user.id
      };

      if (alertId) {
        // Update existing configuration
        const { error } = await supabase
          .from('workflow_alerts')
          .update(alertData)
          .eq('id', alertId);

        if (error) throw error;
      } else {
        // Create new configuration
        const { data, error } = await supabase
          .from('workflow_alerts')
          .insert(alertData)
          .select()
          .single();

        if (error) throw error;
        setAlertId(data.id);
      }

      toast({
        title: "Success",
        description: "Alert settings saved successfully"
      });
    } catch (error) {
      console.error('Error saving alert configuration:', error);
      toast({
        title: "Error",
        description: "Failed to save alert settings",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const addEmailAddress = () => {
    if (newEmail && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newEmail)) {
      setAlertConfig(prev => ({
        ...prev,
        emailAddresses: [...prev.emailAddresses, newEmail]
      }));
      setNewEmail('');
    } else {
      toast({
        title: "Invalid Email",
        description: "Please enter a valid email address",
        variant: "destructive"
      });
    }
  };

  const removeEmailAddress = (index: number) => {
    setAlertConfig(prev => ({
      ...prev,
      emailAddresses: prev.emailAddresses.filter((_, i) => i !== index)
    }));
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="text-center">Loading alert settings...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5" />
          Alert Settings for {workflowName}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Active Status */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="alerts-active">Enable Alerts</Label>
            <p className="text-sm text-muted-foreground">
              Turn on/off all alerts for this workflow
            </p>
          </div>
          <Switch
            id="alerts-active"
            checked={alertConfig.isActive}
            onCheckedChange={(checked) => 
              setAlertConfig(prev => ({ ...prev, isActive: checked }))
            }
          />
        </div>

        <Tabs defaultValue="conditions" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="conditions">Conditions</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="slack">Slack</TabsTrigger>
          </TabsList>

          {/* Alert Conditions */}
          <TabsContent value="conditions" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="alert-failure" className="flex items-center gap-2">
                    <X className="h-4 w-4 text-destructive" />
                    Alert on Failure
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Send alerts when workflow execution fails
                  </p>
                </div>
                <Switch
                  id="alert-failure"
                  checked={alertConfig.alertOnFailure}
                  onCheckedChange={(checked) => 
                    setAlertConfig(prev => ({ ...prev, alertOnFailure: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="alert-success" className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    Alert on Success
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Send alerts when workflow completes successfully
                  </p>
                </div>
                <Switch
                  id="alert-success"
                  checked={alertConfig.alertOnSuccess}
                  onCheckedChange={(checked) => 
                    setAlertConfig(prev => ({ ...prev, alertOnSuccess: checked }))
                  }
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="alert-duration" className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-orange-500" />
                      Alert on Duration Exceeded
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Send alerts when execution time exceeds threshold
                    </p>
                  </div>
                  <Switch
                    id="alert-duration"
                    checked={alertConfig.alertOnDurationThreshold}
                    onCheckedChange={(checked) => 
                      setAlertConfig(prev => ({ ...prev, alertOnDurationThreshold: checked }))
                    }
                  />
                </div>
                
                {alertConfig.alertOnDurationThreshold && (
                  <div className="ml-6">
                    <Label htmlFor="duration-threshold">Duration Threshold (seconds)</Label>
                    <Input
                      id="duration-threshold"
                      type="number"
                      min="1"
                      value={alertConfig.durationThresholdMs / 1000}
                      onChange={(e) => 
                        setAlertConfig(prev => ({ 
                          ...prev, 
                          durationThresholdMs: parseInt(e.target.value) * 1000 
                        }))
                      }
                      className="w-32"
                    />
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="rate-limit" className="flex items-center gap-2">
                  Rate Limit
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Maximum number of alerts to send per hour</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <Input
                  id="rate-limit"
                  type="number"
                  min="1"
                  max="100"
                  value={alertConfig.maxAlertsPerHour}
                  onChange={(e) => 
                    setAlertConfig(prev => ({ 
                      ...prev, 
                      maxAlertsPerHour: parseInt(e.target.value) || 5
                    }))
                  }
                  className="w-32"
                />
              </div>
            </div>
          </TabsContent>

          {/* Email Configuration */}
          <TabsContent value="email" className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="enable-email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Enable Email Alerts
              </Label>
              <Switch
                id="enable-email"
                checked={alertConfig.enableEmail}
                onCheckedChange={(checked) => 
                  setAlertConfig(prev => ({ ...prev, enableEmail: checked }))
                }
              />
            </div>

            {alertConfig.enableEmail && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Email Recipients</Label>
                  <div className="flex gap-2">
                    <Input
                      type="email"
                      placeholder="Enter email address"
                      value={newEmail}
                      onChange={(e) => setNewEmail(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addEmailAddress()}
                    />
                    <Button onClick={addEmailAddress} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  {alertConfig.emailAddresses.map((email, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Badge variant="secondary" className="flex-1">
                        {email}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeEmailAddress(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  {alertConfig.emailAddresses.length === 0 && (
                    <p className="text-sm text-muted-foreground">
                      No email addresses configured
                    </p>
                  )}
                </div>
              </div>
            )}
          </TabsContent>

          {/* Slack Configuration */}
          <TabsContent value="slack" className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="enable-slack" className="flex items-center gap-2">
                <Slack className="h-4 w-4" />
                Enable Slack Alerts
              </Label>
              <Switch
                id="enable-slack"
                checked={alertConfig.enableSlack}
                onCheckedChange={(checked) => 
                  setAlertConfig(prev => ({ ...prev, enableSlack: checked }))
                }
              />
            </div>

            {alertConfig.enableSlack && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="slack-webhook">Slack Webhook URL</Label>
                  <Textarea
                    id="slack-webhook"
                    placeholder="https://hooks.slack.com/services/..."
                    value={alertConfig.slackWebhookUrl}
                    onChange={(e) => 
                      setAlertConfig(prev => ({ ...prev, slackWebhookUrl: e.target.value }))
                    }
                    rows={2}
                  />
                  <p className="text-sm text-muted-foreground">
                    Create an incoming webhook in your Slack workspace
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slack-channel">Slack Channel (optional)</Label>
                  <Input
                    id="slack-channel"
                    placeholder="#alerts or @username"
                    value={alertConfig.slackChannel}
                    onChange={(e) => 
                      setAlertConfig(prev => ({ ...prev, slackChannel: e.target.value }))
                    }
                  />
                  <p className="text-sm text-muted-foreground">
                    Override the default channel configured in the webhook
                  </p>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Save Button */}
        <div className="pt-4">
          <Button 
            onClick={saveAlertConfiguration} 
            disabled={saving}
            className="w-full"
          >
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Alert Settings'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
