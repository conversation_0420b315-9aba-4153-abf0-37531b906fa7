import { useState, use<PERSON>emo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { RefreshCw, Loader2, CheckCircle, XCircle, Clock, ArrowRight } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface WorkflowExecution {
  id: string;
  workflow_id: string;
  status: 'completed' | 'failed' | 'in_progress';
  started_at: string;
  completed_at: string | null;
  execution_log: any;
  created_by: string;
}

interface WorkflowHistoryProps {
  workflowId?: string;
}

export function WorkflowH<PERSON>ory({ workflowId }: WorkflowHistoryProps) {
  const { user } = useAuth();
  const [selectedExecution, setSelectedExecution] = useState<WorkflowExecution | null>(null);
  const [showExecutionDetails, setShowExecutionDetails] = useState(false);

  // Real-time workflow executions subscription
  const { records: allExecutions = [], isLoading } = useRealtimeCollection(
    'workflow_executions',
    async () => {
      if (!user) {
        console.log('No user found, returning empty array');
        return [];
      }
      
      try {
        let query = supabase
          .from('workflow_executions')
          .select('*')
          .eq('created_by', user.id);
          
        if (workflowId) {
          query = query.eq('workflow_id', workflowId);
        }
        
        const { data, error } = await query
          .order('started_at', { ascending: false })
          .limit(50);
          
        if (error) {
          console.error('Error fetching workflow executions:', error);
          return [];
        }
        
        return data || [];
      } catch (error) {
        console.error('Error in workflow executions fetch:', error);
        return [];
      }
    },
    'public',
    `created_by=eq.${user?.id}`
  );

  // Filter executions based on workflowId prop
  const executions = useMemo(() => {
    if (!workflowId) return allExecutions;
    return allExecutions.filter(execution => execution.workflow_id === workflowId);
  }, [allExecutions, workflowId]);
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            Completed
          </Badge>
        );
      case 'failed':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 flex items-center gap-1">
            <XCircle className="h-3 w-3" />
            Failed
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 flex items-center gap-1">
            <Clock className="h-3 w-3" />
            In Progress
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  const formatDuration = (startDate: string, endDate: string | null) => {
    if (!endDate) return 'In progress';
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    const durationMs = end.getTime() - start.getTime();
    
    // Format as minutes and seconds
    const seconds = Math.floor(durationMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes === 0) {
      return `${seconds} seconds`;
    }
    
    return `${minutes}m ${remainingSeconds}s`;
  };
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Workflow Execution History</CardTitle>
        <div className="flex items-center gap-2">
          {isLoading ? (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Loading...
            </div>
          ) : (
            <div className="flex items-center gap-2 text-sm text-green-600">
              <CheckCircle className="h-4 w-4" />
              Real-time
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : executions.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No execution history found. Run a workflow to see its execution history.
          </div>
        ) : (
          <div className="space-y-4">
            <ScrollArea className="h-[300px]">
              <div className="space-y-2">
                {executions.map((execution) => (
                  <div 
                    key={execution.id} 
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedExecution?.id === execution.id 
                        ? 'border-primary bg-accent/5' 
                        : 'hover:bg-accent/5'
                    }`}
                    onClick={() => setSelectedExecution(
                      selectedExecution?.id === execution.id ? null : execution
                    )}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getStatusBadge(execution.status)}
                        <span className="text-sm font-medium">
                          {formatDistanceToNow(new Date(execution.started_at), { addSuffix: true })}
                        </span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Duration: {formatDuration(execution.started_at, execution.completed_at)}
                      </div>
                    </div>
                    <div className="flex flex-col space-y-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">
                          {new Date(execution.started_at).toLocaleDateString()}
                        </span>
                        <Badge
                          variant={execution.status === 'completed' ? 'default' : execution.status === 'failed' ? 'destructive' : 'secondary'}
                        >
                          {execution.status}
                        </Badge>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => {
                            setSelectedExecution(execution);
                            setShowExecutionDetails(true);
                          }}
                        >
                          View Details
                        </Button>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {execution.completed_at 
                          ? `Duration: ${Math.round((new Date(execution.completed_at).getTime() - new Date(execution.started_at).getTime()) / 1000)} seconds` 
                          : 'In progress...'}
                      </p>
                    </div>
                    
                    {selectedExecution?.id === execution.id && execution.execution_log && (
                      <div className="mt-4 space-y-4">
                        {execution.execution_log.executionPath && (
                          <div>
                            <h4 className="text-sm font-medium mb-2">Execution Path</h4>
                            <div className="flex flex-wrap items-center gap-2">
                              {execution.execution_log.executionPath.map((nodeId: string, index: number) => (
                                <div key={nodeId} className="flex items-center">
                                  <Badge variant="outline" className="bg-accent/50">
                                    {nodeId}
                                  </Badge> 
                                  {index < execution.execution_log.executionPath.length - 1 && (
                                    <ArrowRight className="h-4 w-4 mx-1" />
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {execution.execution_log.nodes && (
                          <div>
                            <h4 className="text-sm font-medium mb-2">Execution Logs</h4>
                            <div className="space-y-2 text-sm">
                              {execution.execution_log.nodes.map((log: any, index: number) => (
                                <div key={index} className="flex items-start gap-2 pb-2 border-b last:border-0">
                                  {log.status === 'success' && <CheckCircle className="h-4 w-4 text-success shrink-0 mt-0.5" />}
                                  {log.status === 'error' && <XCircle className="h-4 w-4 text-destructive shrink-0 mt-0.5" />}
                                  {log.status === 'pending' && <Clock className="h-4 w-4 text-primary shrink-0 mt-0.5" />}
                                  <div>
                                    <p>{log.message}</p>
                                    {log.timestamp && (
                                      <p className="text-xs text-muted-foreground">
                                        {new Date(log.timestamp).toLocaleTimeString()}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {execution.execution_log.error && (
                          <div className="mt-2 p-2 bg-destructive/10 text-destructive rounded-md text-sm">
                            Error: {execution.execution_log.error}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
                
                {/* Execution Details Dialog */}
                <Dialog open={showExecutionDetails} onOpenChange={setShowExecutionDetails}>
                  <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>Execution Details</DialogTitle>
                    </DialogHeader>
                    
                    {selectedExecution && (
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <Badge variant={selectedExecution.status === 'completed' ? 'default' : selectedExecution.status === 'failed' ? 'destructive' : 'secondary'}>
                            {selectedExecution.status}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {new Date(selectedExecution.started_at).toLocaleString()}
                          </span>
                        </div>
                        
                        {selectedExecution.execution_log?.executionPath && (
                          <div>
                            <h4 className="text-sm font-medium mb-2">Execution Path</h4>
                            <div className="flex flex-wrap items-center gap-2">
                              {selectedExecution.execution_log.executionPath.map((nodeId: string, index: number) => (
                                <div key={nodeId} className="flex items-center">
                                  <Badge variant="outline" className="bg-accent/50">
                                    {nodeId}
                                  </Badge>
                                  {index < selectedExecution.execution_log.executionPath.length - 1 && (
                                    <ArrowRight className="h-4 w-4 mx-1" />
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {selectedExecution.execution_log?.nodes && (
                          <div>
                            <h4 className="text-sm font-medium mb-2">Execution Logs</h4>
                            <ScrollArea className="h-[300px]">
                              <div className="space-y-2 text-sm">
                                {selectedExecution.execution_log.nodes.map((log: any, index: number) => (
                                  <div key={index} className="flex items-start gap-2 pb-2 border-b last:border-0">
                                    {log.status === 'success' && <CheckCircle className="h-4 w-4 text-success shrink-0 mt-0.5" />}
                                    {log.status === 'error' && <XCircle className="h-4 w-4 text-destructive shrink-0 mt-0.5" />}
                                    {log.status === 'pending' && <Clock className="h-4 w-4 text-primary shrink-0 mt-0.5" />}
                                    <div>
                                      <p>{log.message}</p>
                                      {log.timestamp && (
                                        <p className="text-xs text-muted-foreground">
                                          {new Date(log.timestamp).toLocaleTimeString()}
                                        </p>
                                      )}
                                      {log.data && (
                                        <pre className="text-xs bg-muted p-2 mt-1 rounded overflow-auto">
                                          {JSON.stringify(log.data, null, 2)}
                                        </pre>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </ScrollArea>
                          </div>
                        )}
                        
                        {selectedExecution.execution_log?.context && (
                          <div>
                            <h4 className="text-sm font-medium mb-2">Execution Context</h4>
                            <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                              {JSON.stringify(selectedExecution.execution_log.context, null, 2)}
                            </pre>
                          </div>
                        )}
                        
                        {selectedExecution.execution_log?.error && (
                          <div className="mt-2 p-2 bg-destructive/10 text-destructive rounded-md text-sm">
                            Error: {selectedExecution.execution_log.error}
                          </div>
                        )}
                      </div>
                    )}
                  </DialogContent>
                </Dialog>
              </div>
            </ScrollArea>
          </div>
        )}
      </CardContent>
    </Card>
  );
}