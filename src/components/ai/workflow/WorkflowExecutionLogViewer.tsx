import { useEffect, useState, useRef } from 'react';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';
import { 
  Activity, 
  X, 
  Filter,
  Download,
  Trash2,
  RefreshCw,
  Circle,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Zap,
  Search
} from "lucide-react";
import { cn } from '@/lib/utils';
import { format } from 'date-fns';

interface ExecutionEvent {
  id: string;
  type: 'started' | 'node_started' | 'node_completed' | 'node_failed' | 'completed' | 'failed' | 'error';
  timestamp: string;
  nodeId?: string;
  nodeName?: string;
  message?: string;
  data?: any;
  error?: any;
}

interface WorkflowExecutionLogViewerProps {
  workflowId?: string;
  executionId?: string;
  isVisible: boolean;
  onClose: () => void;
  className?: string;
}

export function WorkflowExecutionLogViewer({ 
  workflowId,
  executionId,
  isVisible,
  onClose,
  className 
}: WorkflowExecutionLogViewerProps) {
  const [events, setEvents] = useState<ExecutionEvent[]>([]);
  const [filteredEvents, setFilteredEvents] = useState<ExecutionEvent[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTypes, setSelectedTypes] = useState<Set<string>>(new Set());
  const [isConnected, setIsConnected] = useState(false);
  const [autoScroll, setAutoScroll] = useState(true);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const channelRef = useRef<RealtimeChannel | null>(null);

  useEffect(() => {
    if (isVisible && (workflowId || executionId)) {
      subscribeToRealtimeEvents();
    }

    return () => {
      if (channelRef.current) {
        channelRef.current.unsubscribe();
      }
    };
  }, [workflowId, executionId, isVisible]);

  useEffect(() => {
    filterEvents();
  }, [events, searchQuery, selectedTypes]);

  useEffect(() => {
    if (autoScroll && scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [filteredEvents, autoScroll]);

  const subscribeToRealtimeEvents = async () => {
    const channelName = executionId 
      ? `workflow-execution:${executionId}`
      : `workflow:${workflowId}`;

    channelRef.current = supabase
      .channel(channelName)
      .on('broadcast', { event: 'workflow_events' }, (payload) => {
        console.log('Received workflow events:', payload);
        if (payload.payload?.events) {
          const newEvents = payload.payload.events.map((event: any) => ({
            ...event,
            id: `${event.timestamp}-${Math.random().toString(36).substr(2, 9)}`
          }));
          setEvents(prev => [...prev, ...newEvents]);
        }
      })
      .subscribe((status) => {
        setIsConnected(status === 'SUBSCRIBED');
        console.log(`Channel ${channelName} subscription status:`, status);
      });
  };

  const filterEvents = () => {
    let filtered = [...events];

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(event => 
        event.message?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.nodeName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.type.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by selected types
    if (selectedTypes.size > 0) {
      filtered = filtered.filter(event => selectedTypes.has(event.type));
    }

    setFilteredEvents(filtered);
  };

  const toggleTypeFilter = (type: string) => {
    const newTypes = new Set(selectedTypes);
    if (newTypes.has(type)) {
      newTypes.delete(type);
    } else {
      newTypes.add(type);
    }
    setSelectedTypes(newTypes);
  };

  const clearLogs = () => {
    setEvents([]);
    setFilteredEvents([]);
  };

  const exportLogs = () => {
    const logContent = filteredEvents.map(event => 
      `[${format(new Date(event.timestamp), 'HH:mm:ss.SSS')}] ${event.type.toUpperCase()} - ${event.message || event.nodeName || 'No message'}`
    ).join('\n');

    const blob = new Blob([logContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `workflow-execution-${new Date().toISOString()}.log`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'started':
        return <Zap className="w-4 h-4" />;
      case 'node_started':
        return <Circle className="w-4 h-4" />;
      case 'node_completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'node_failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-orange-500" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const getEventColor = (type: string) => {
    switch (type) {
      case 'started':
        return 'text-blue-600';
      case 'node_started':
        return 'text-gray-600';
      case 'node_completed':
        return 'text-green-600';
      case 'node_failed':
        return 'text-red-600';
      case 'completed':
        return 'text-green-700';
      case 'failed':
        return 'text-red-700';
      case 'error':
        return 'text-orange-600';
      default:
        return 'text-gray-600';
    }
  };

  if (!isVisible) return null;

  const eventTypes = Array.from(new Set(events.map(e => e.type)));

  return (
    <Card className={cn(
      "absolute bottom-4 right-4 w-[600px] h-[500px] z-50 shadow-lg flex flex-col",
      className
    )}>
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Activity className="w-5 h-5" />
          <h3 className="font-medium">Execution Log</h3>
          <Badge variant={isConnected ? "success" : "secondary"} className="ml-2">
            {isConnected ? "Connected" : "Disconnected"}
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setAutoScroll(!autoScroll)}
            className={cn(autoScroll && "bg-accent")}
            title={autoScroll ? "Disable auto-scroll" : "Enable auto-scroll"}
          >
            <RefreshCw className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={exportLogs}
            title="Export logs"
          >
            <Download className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={clearLogs}
            title="Clear logs"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="p-4 space-y-3 border-b">
        <div className="relative">
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search logs..."
            className="pl-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {eventTypes.length > 0 && (
          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-muted-foreground">Filter:</span>
            {eventTypes.map(type => (
              <Badge
                key={type}
                variant={selectedTypes.has(type) ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => toggleTypeFilter(type)}
              >
                {type.replace('_', ' ')}
              </Badge>
            ))}
          </div>
        )}
      </div>

      <ScrollArea className="flex-1 px-4" ref={scrollAreaRef}>
        <div className="space-y-2 py-4">
          {filteredEvents.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {events.length === 0 
                ? "No execution events yet..."
                : "No events match your filters"
              }
            </div>
          ) : (
            filteredEvents.map((event) => (
              <div
                key={event.id}
                className={cn(
                  "flex items-start gap-3 p-2 rounded-lg hover:bg-accent/50 transition-colors",
                  "border-l-2",
                  event.type.includes('failed') || event.type === 'error' 
                    ? "border-red-500" 
                    : event.type.includes('completed')
                    ? "border-green-500"
                    : "border-transparent"
                )}
              >
                <div className="flex-shrink-0 mt-0.5">
                  {getEventIcon(event.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className={cn("text-sm font-medium", getEventColor(event.type))}>
                      {event.type.replace('_', ' ').toUpperCase()}
                    </span>
                    {event.nodeName && (
                      <Badge variant="secondary" className="text-xs">
                        {event.nodeName}
                      </Badge>
                    )}
                  </div>
                  {event.message && (
                    <p className="text-sm text-muted-foreground mt-1">
                      {event.message}
                    </p>
                  )}
                  {event.error && (
                    <pre className="text-xs text-red-600 mt-1 p-2 bg-red-50 rounded overflow-x-auto">
                      {JSON.stringify(event.error, null, 2)}
                    </pre>
                  )}
                  {event.data && Object.keys(event.data).length > 0 && (
                    <details className="mt-1">
                      <summary className="text-xs text-muted-foreground cursor-pointer">
                        View data
                      </summary>
                      <pre className="text-xs mt-1 p-2 bg-muted rounded overflow-x-auto">
                        {JSON.stringify(event.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
                <div className="flex-shrink-0 text-xs text-muted-foreground">
                  {format(new Date(event.timestamp), 'HH:mm:ss.SSS')}
                </div>
              </div>
            ))
          )}
        </div>
      </ScrollArea>

      <div className="p-2 border-t text-xs text-center text-muted-foreground">
        {filteredEvents.length} event{filteredEvents.length !== 1 ? 's' : ''} 
        {searchQuery || selectedTypes.size > 0 ? ` (filtered from ${events.length})` : ''}
      </div>
    </Card>
  );
}
