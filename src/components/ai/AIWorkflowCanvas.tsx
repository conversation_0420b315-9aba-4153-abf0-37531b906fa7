import { WorkflowLibrary } from "./workflow/WorkflowLibrary";
import { WorkflowCanvas } from "./workflow/WorkflowCanvas";
import { useWorkflowConfigurations } from "@/hooks/useWorkflowConfigurations";
import { useEffect, useState } from "react";

export function AIWorkflowCanvas({ editWorkflowId }: { editWorkflowId?: string | null }) {
  const { data: workflows } = useWorkflowConfigurations();
  const [workflowConfig, setWorkflowConfig] = useState<any>(null);
  
  // Load workflow configuration when editWorkflowId changes
  useEffect(() => {
    if (editWorkflowId && workflows) {
      const workflow = workflows.find(w => w.id === editWorkflowId);
      if (workflow) {
        setWorkflowConfig(workflow.config);
      }
    } else {
      setWorkflowConfig(null);
    }
  }, [editWorkflowId, workflows]);

  const handleNodeSelect = (node: any) => {
    console.log("Selected node:", node);
    // Handle node selection logic here
  };

  return (
    <div className="flex gap-4 h-[calc(100vh-12rem)]">
      <WorkflowLibrary onNodeSelect={handleNodeSelect} />
      <WorkflowCanvas 
        className="flex-1" 
        initialConfig={workflowConfig} 
        workflowId={editWorkflowId}
        workflows={workflows}
      />
    </div>
  );
}