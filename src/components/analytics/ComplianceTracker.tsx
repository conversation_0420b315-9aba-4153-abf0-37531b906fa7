import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Shield, AlertTriangle, CheckCircle, Loader2 } from "lucide-react";
import { useCompliance } from "@/hooks/useCompliance";
import { Skeleton } from "@/components/ui/skeleton";

export function ComplianceTracker() {
  const { data: complianceData, isLoading, error } = useCompliance();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            <Skeleton className="h-6 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center space-y-4">
            <Skeleton className="h-16 w-16 rounded-full mx-auto" />
            <div className="space-y-2">
              <Skeleton className="h-8 w-32 mx-auto" />
              <Skeleton className="h-4 w-48 mx-auto" />
            </div>
          </div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="space-y-3">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-5 w-48" />
                  <Skeleton className="h-5 w-12" />
                </div>
                <Skeleton className="h-4 w-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !complianceData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Compliance Tracker
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center p-8">
            <p className="text-muted-foreground">Failed to load compliance data. Please try again.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Prepare metrics data from database
  const metrics = [
    {
      name: "Documentation Compliance",
      status: complianceData.documentation_compliance >= 95 ? "compliant" : complianceData.documentation_compliance >= 85 ? "warning" : "non-compliant",
      value: `${complianceData.documentation_compliance}%`,
      description: complianceData.documentation_compliance >= 95 ? "All required documents are properly maintained" : "Some documentation may need attention"
    },
    {
      name: "Equal Opportunity",
      status: complianceData.equal_opportunity >= 95 ? "compliant" : complianceData.equal_opportunity >= 85 ? "warning" : "non-compliant",
      value: `${complianceData.equal_opportunity}%`,
      description: complianceData.equal_opportunity >= 95 ? "Meeting all EEO requirements" : "EEO practices need improvement"
    },
    {
      name: "Interview Process",
      status: complianceData.interview_process >= 95 ? "compliant" : complianceData.interview_process >= 85 ? "warning" : "non-compliant",
      value: `${complianceData.interview_process}%`,
      description: complianceData.interview_process >= 95 ? "Interview process meets all standards" : "Some interviews may lack proper documentation"
    }
  ];

  // Generate recent alerts based on compliance scores
  const recentAlerts = [];
  
  if (complianceData.interview_process < 90) {
    recentAlerts.push({
      type: "warning" as const,
      message: `Interview process compliance at ${complianceData.interview_process}% - needs attention`,
      date: "1 day ago"
    });
  }
  
  if (complianceData.overall_score >= 90) {
    recentAlerts.push({
      type: "success" as const,
      message: "Monthly compliance report submitted successfully",
      date: "2 days ago"
    });
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-4 w-4" />
          Compliance Tracker
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Score */}
        <div className="text-center space-y-4">
          <div className="relative inline-flex items-center justify-center w-20 h-20">
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 opacity-20"></div>
            <div className="relative text-2xl font-bold text-primary">
              {complianceData.overall_score}
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold">Overall Compliance Score</h3>
            <p className="text-sm text-muted-foreground">
              Last updated: {new Date(complianceData.last_updated).toLocaleDateString()}
            </p>
          </div>
        </div>

        {/* Metrics */}
        <div className="space-y-4">
          <h4 className="font-medium">Compliance Metrics</h4>
          {metrics.map((metric, index) => (
            <div key={index} className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {metric.status === "compliant" && (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  )}
                  {metric.status === "warning" && (
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  )}
                  {metric.status === "non-compliant" && (
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  )}
                  <span className="font-medium">{metric.name}</span>
                </div>
                <span className="font-bold text-lg">{metric.value}</span>
              </div>
              <p className="text-sm text-muted-foreground pl-6">
                {metric.description}
              </p>
            </div>
          ))}
        </div>

        {/* Recent Alerts */}
        {recentAlerts.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Recent Alerts</h4>
            {recentAlerts.map((alert, index) => (
              <Alert key={index} variant={alert.type === "warning" ? "destructive" : "default"}>
                {alert.type === "warning" ? (
                  <AlertTriangle className="h-4 w-4" />
                ) : (
                  <CheckCircle className="h-4 w-4" />
                )}
                <AlertDescription className="flex items-center justify-between">
                  <span>{alert.message}</span>
                  <span className="text-xs text-muted-foreground">{alert.date}</span>
                </AlertDescription>
              </Alert>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}