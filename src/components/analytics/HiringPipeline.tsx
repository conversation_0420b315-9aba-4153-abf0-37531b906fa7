import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Users } from "lucide-react";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { Skeleton } from "@/components/ui/skeleton";

interface PipelineStage {
  stage: string;
  count: number;
  percentage: number;
}

export function HiringPipeline() {
  const { user } = useAuth();
  
  // Real-time hiring pipeline data subscription using proper pipeline tables
  const { records: pipelineData = [], isLoading } = useRealtimeCollection(
    'pipeline_stages',
    async (): Promise<PipelineStage[]> => {
      if (!user) return [];

      try {
        // Get pipeline stages with their counts from the pipeline_stages table
        const { data: stages, error: stagesError } = await supabase
          .from('pipeline_stages')
          .select('*')
          .eq('user_id', user.id)
          .order('stage_order', { ascending: true });

        if (stagesError) throw stagesError;

        if (!stages || stages.length === 0) {
          // Fallback to calculating from pipeline_candidates if no stages defined
          const { data: candidates, error: candidatesError } = await supabase
            .from('pipeline_candidates')
            .select('stage')
            .eq('user_id', user.id);

          if (candidatesError) throw candidatesError;

          // Group candidates by stage
          const stageCounts: { [key: string]: number } = {};
          candidates?.forEach(candidate => {
            stageCounts[candidate.stage] = (stageCounts[candidate.stage] || 0) + 1;
          });

          const totalCandidates = candidates?.length || 0;

          return Object.entries(stageCounts).map(([stage, count]) => ({
            stage,
            count,
            percentage: totalCandidates > 0 ? Math.round((count / totalCandidates) * 100) : 0
          }));
        }

        // Use the pipeline_stages table data with real counts
        const totalCandidates = stages.reduce((sum, stage) => sum + (stage.count || 0), 0);

        return stages.map(stage => ({
          stage: stage.name,
          count: stage.count || 0,
          percentage: totalCandidates > 0 ? Math.round(((stage.count || 0) / totalCandidates) * 100) : 0
        }));

      } catch (error) {
        console.error('Error fetching hiring pipeline data:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${user?.id}`
  );
  
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <Skeleton className="h-4 w-32" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="space-y-1">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <Skeleton className="h-2 w-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-4 w-4" />
          Hiring Pipeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {pipelineData?.map((stage, index) => (
            <div key={index} className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{stage.stage}</span>
                <span className="text-sm text-muted-foreground">
                  {stage.count} candidates
                </span>
              </div>
              <Progress value={stage.percentage} className="h-2" />
              <span className="text-xs text-muted-foreground">
                {stage.percentage.toFixed(1)}% conversion rate
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}