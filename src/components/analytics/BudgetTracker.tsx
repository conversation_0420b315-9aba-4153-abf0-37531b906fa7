import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { DollarSign, Loader2 } from "lucide-react";
import { useBudgetSummary } from "@/hooks/useBudget";
import { Skeleton } from "@/components/ui/skeleton";

export function BudgetTracker() {
  const { data: budgetSummary, isLoading, error } = useBudgetSummary();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            <Skeleton className="h-6 w-32" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <div className="flex items-center justify-between mb-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-12" />
              </div>
              <Skeleton className="h-2 w-full" />
              <div className="flex justify-between mt-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>

            <div className="space-y-4">
              {[...Array(4)].map((_, i) => (
                <div key={i}>
                  <div className="flex items-center justify-between mb-1">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <Skeleton className="h-1 w-full" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !budgetSummary) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Budget Tracker
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center p-8">
            <p className="text-muted-foreground">Failed to load budget data. Please try again.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { total_budget, spent_amount, categories } = budgetSummary;
  const percentageSpent = total_budget > 0 ? (spent_amount / total_budget) * 100 : 0;
  const remaining = total_budget - spent_amount;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-4 w-4" />
          Budget Tracker
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Total Budget Usage</span>
              <span className="text-sm font-medium">{percentageSpent.toFixed(1)}%</span>
            </div>
            <Progress value={percentageSpent} className="h-2" />
            <div className="flex justify-between mt-2 text-sm text-muted-foreground">
              <span>${spent_amount.toLocaleString()}</span>
              <span>${remaining.toLocaleString()} remaining</span>
            </div>
          </div>

          <div className="space-y-4">
            {categories.map((category, index) => (
              <div key={index}>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm">{category.category}</span>
                  <span className="text-sm font-medium">
                    ${category.amount.toLocaleString()}
                  </span>
                </div>
                <Progress value={category.percentage} className="h-1" />
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}