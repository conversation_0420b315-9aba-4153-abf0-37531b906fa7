import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON>, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { Zap } from 'lucide-react';
import { useAnalyticsSources } from "@/hooks/useAnalyticsSources";
import { Skeleton } from "@/components/ui/skeleton";


// Beautiful gradient colors for the pie chart
const COLORS = [
  ['#60A5FA', '#3B82F6'], // Blue gradient
  ['#34D399', '#10B981'], // Green gradient
  ['#A78BFA', '#8B5CF6'], // Purple gradient
  ['#F472B6', '#EC4899'], // Pink gradient
];

export const SourceEffectiveness = ({ expanded = false }: { expanded?: boolean }) => {
  const { data: candidateSourceEffectiveness = [], isLoading } = useAnalyticsSources();

  // Use only real data from Supabase
  const sourceData = candidateSourceEffectiveness;

  // Transform data for chart
  const chartData = sourceData.map(source => ({
    name: source.source,
    value: source.hires,
    effectiveness: source.effectiveness
  }));

  // Production logging removed for performance

  if (isLoading) {
    return (
      <div className={expanded ? 'h-full' : ''}>
        <Card className={expanded ? 'h-full border-0 shadow-none' : ''}>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent className={expanded ? 'h-[calc(100%-80px)]' : 'h-[300px]'}>
            <Skeleton className="h-full w-full rounded-full" />
          </CardContent>
        </Card>
      </div>
    );
  }
  const renderCustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 rounded-lg shadow-lg border border-gray-100">
          <p className="font-medium text-gray-900">{payload[0].name}</p>
          <p className="text-sm text-gray-600">
            Hires: <span className="font-medium">{payload[0].value}</span>
          </p>
          <p className="text-sm text-gray-600">
            Effectiveness: <span className="font-medium">{sourceData.find(item => item.source === payload[0].name)?.effectiveness}%</span>
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className={expanded ? 'h-full' : ''}>
      <Card className={expanded ? 'h-full border-0 shadow-none' : ''}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Source Effectiveness
          </CardTitle>
        </CardHeader>
        <CardContent className={expanded ? 'h-[calc(100%-80px)]' : 'h-[300px]'}>
          <style>
            {`
              .recharts-wrapper, 
              .recharts-wrapper *,
              .recharts-pie-sector,
              .recharts-pie,
              .recharts-surface,
              .recharts-layer {
                outline: none !important;
                border: none !important;
              }
              .recharts-wrapper:focus,
              .recharts-wrapper *:focus {
                outline: none !important;
                border: none !important;
              }
            `}
          </style>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <defs>
                  {COLORS.map((colors, index) => (
                    <linearGradient
                      key={`gradient-${index}`}
                      id={`gradient-${index}`}
                      x1="0"
                      y1="0"
                      x2="1"
                      y2="1"
                    >
                      <stop offset="0%" stopColor={colors[0]} stopOpacity={1} />
                      <stop offset="100%" stopColor={colors[1]} stopOpacity={1} />
                    </linearGradient>
                  ))}
                </defs>
                <Pie
                  data={chartData}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  innerRadius={expanded ? '45%' : 40}
                  outerRadius={expanded ? '70%' : 80}
                  paddingAngle={4}
                  label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                  labelLine={{ stroke: '#6B7280', strokeWidth: 1 }}
                >
                  {chartData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={`url(#gradient-${index})`}
                      stroke="white"
                      strokeWidth={2}
                      style={{ outline: 'none', cursor: 'default' }}
                      onFocus={(e) => e.preventDefault()}
                      tabIndex={-1}
                    />
                  ))}
                </Pie>
                <Tooltip content={renderCustomTooltip} />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
};