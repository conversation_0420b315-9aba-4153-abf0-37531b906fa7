import { MetricsCards } from "./MetricsCards";
import { ApplicationsChart } from "./ApplicationsChart";
import { HiringTrends } from "./HiringTrends";
import { SkillGapAnalysis } from "./SkillGapAnalysis";
import { DiversityMetrics } from "./DiversityMetrics";
import { SourceEffectiveness } from "./SourceEffectiveness";
import { SalaryRecommendations } from "./SalaryRecommendations";
import { useState } from "react";
import { Maximize2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";



export const AnalyticsDashboard = () => {
  const [expandedCard, setExpandedCard] = useState<string | null>(null);

  const handleExpand = (cardId: string) => {
    setExpandedCard(cardId);
  };

  const handleClose = () => {
    setExpandedCard(null);
  };

  return (
    <div className="space-y-6">

      <MetricsCards />
      <ApplicationsChart />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="relative">
          <button
            onClick={() => handleExpand('hiring')}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <HiringTrends expanded={false} />
        </div>
        <div className="relative">
          <button
            onClick={() => handleExpand('skills')}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <SkillGapAnalysis expanded={false} />
        </div>
        <div className="relative">
          <button
            onClick={() => handleExpand('diversity')}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <DiversityMetrics expanded={false} />
        </div>
        <div className="relative">
          <button
            onClick={() => handleExpand('source')}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <SourceEffectiveness expanded={false} />
        </div>
        <div className="relative">
          <button
            onClick={() => handleExpand('salary')}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <SalaryRecommendations expanded={false} />
        </div>
      </div>

      <Dialog open={expandedCard === 'hiring'} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none flex flex-col">
          <DialogHeader>
            <DialogTitle>Hiring Trends</DialogTitle>
          </DialogHeader>
          <div className="flex-1">
            <HiringTrends expanded={true} />
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={expandedCard === 'skills'} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none flex flex-col">
          <DialogHeader>
            <DialogTitle>Skill Gap Analysis</DialogTitle>
          </DialogHeader>
          <div className="flex-1">
            <SkillGapAnalysis expanded={true} />
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={expandedCard === 'diversity'} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none flex flex-col">
          <DialogHeader>
            <DialogTitle>Diversity Metrics</DialogTitle>
          </DialogHeader>
          <div className="flex-1">
            <DiversityMetrics expanded={true} />
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={expandedCard === 'source'} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none flex flex-col">
          <DialogHeader>
            <DialogTitle>Source Effectiveness</DialogTitle>
          </DialogHeader>
          <div className="flex-1">
            <SourceEffectiveness expanded={true} />
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={expandedCard === 'salary'} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none flex flex-col">
          <DialogHeader>
            <DialogTitle>Salary Recommendations</DialogTitle>
          </DialogHeader>
          <div className="flex-1">
            <SalaryRecommendations expanded={true} />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};