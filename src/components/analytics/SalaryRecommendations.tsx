import { useState, useEffect, use<PERSON>em<PERSON>, use<PERSON>allback } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import { DollarSign, MapPin, Briefcase, Building2, Sparkles, Loader2 } from "lucide-react";
import { generateText } from "@/utils/gemini";
import { useToast } from "@/hooks/use-toast";
import { useAnalyticsSalary } from "@/hooks/useAnalyticsSalary";
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Cell
} from "recharts";

// Memoized constants for better performance
const LOCATIONS = [
  { name: "New York", premium: 0.25 },
  { name: "San Francisco", premium: 0.30 },
  { name: "Seattle", premium: 0.20 },
  { name: "Austin", premium: 0.15 },
  { name: "Chicago", premium: 0.10 },
  { name: "Denver", premium: 0.05 },
  { name: "Remote", premium: 0.00 }
] as const;

const EXPERIENCE_LEVELS = [
  { years: "0-2", premium: -0.15 },
  { years: "3-5", premium: 0.00 },
  { years: "6-8", premium: 0.20 },
  { years: "9-12", premium: 0.35 },
  { years: "13+", premium: 0.50 }
] as const;

const INDUSTRIES = [
  { name: "Technology", premium: 0.15 },
  { name: "Finance", premium: 0.20 },
  { name: "Healthcare", premium: 0.10 },
  { name: "Education", premium: -0.05 },
  { name: "Retail", premium: -0.10 },
  { name: "Manufacturing", premium: 0.05 }
] as const;

// Removed fallback data - using only Supabase data

interface SalaryData {
  role: string;
  market: number;
  recommended: number;
}

export const SalaryRecommendations = ({ expanded = false }: { expanded?: boolean }) => {
  const { data: salaryData = [], isLoading } = useAnalyticsSalary();
  const { toast } = useToast();

  // Optimized state with proper types
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null);
  const [selectedExperience, setSelectedExperience] = useState<string | null>(null);
  const [selectedIndustry, setSelectedIndustry] = useState<string | null>(null);
  const [aiInsights, setAiInsights] = useState<string>("");
  const [isGeneratingInsights, setIsGeneratingInsights] = useState(false);

  // Memoized premium calculations for better performance
  const premiums = useMemo(() => {
    return {
      location: LOCATIONS.find(loc => loc.name === selectedLocation)?.premium || 0,
      experience: EXPERIENCE_LEVELS.find(exp => exp.years === selectedExperience)?.premium || 0,
      industry: INDUSTRIES.find(ind => ind.name === selectedIndustry)?.premium || 0
    };
  }, [selectedLocation, selectedExperience, selectedIndustry]);

  // Optimized salary calculation with memoization
  const adjustedSalaryData = useMemo<SalaryData[]>(() => {
    // Early return if filters not set
    if (selectedLocation === null || selectedExperience === null || selectedIndustry === null) {
      return [];
    }
    
    const dataToUse = salaryData;
    const totalPremium = 1 + premiums.location + premiums.experience + premiums.industry;
    
    // Single-pass transformation for better performance
    return dataToUse.map(item => ({
      role: item.role,
      market: item.market_salary,
      recommended: salaryData.length > 0 
        ? item.recommended_salary 
        : Math.round(item.market_salary * totalPremium)
    }));
  }, [salaryData, selectedLocation, selectedExperience, selectedIndustry, premiums]);

  // Initialize filters with first available data
  useEffect(() => {
    if (salaryData.length > 0 && selectedLocation === null) {
      const firstRecord = salaryData[0];
      setSelectedLocation(firstRecord?.location || LOCATIONS[0].name);
      setSelectedExperience(firstRecord?.experience_level || EXPERIENCE_LEVELS[2].years);
      setSelectedIndustry(firstRecord?.industry || INDUSTRIES[0].name);
    }
  }, [salaryData.length, selectedLocation]);

  // Generate AI insights for salary recommendations
  const generateAIInsights = async () => {
    if (!salaryData.length || isGeneratingInsights) return;

    setIsGeneratingInsights(true);
    try {
      const currentData = adjustedSalaryData.slice(0, 5); // Top 5 roles
      const prompt = `
Analyze the following salary data and provide actionable insights for recruitment:

Salary Data:
${currentData.map(item => `${item.role}: Market Average $${item.market?.toLocaleString()}, AI Recommended $${item.recommended?.toLocaleString()}`).join('\n')}

Filters Applied:
- Location: ${selectedLocation || 'All'}
- Experience Level: ${selectedExperience || 'All'}
- Industry: ${selectedIndustry || 'All'}

Provide insights on:
1. Market trends and competitive positioning
2. Budget recommendations for hiring managers
3. Salary negotiation strategies
4. Cost-effective hiring opportunities

Keep the response concise (2-3 paragraphs) and actionable for recruiters.
`;

      const systemPrompt = "You are an expert compensation analyst providing strategic salary insights for recruitment teams. Focus on practical, data-driven recommendations.";

      const insights = await generateText(prompt, systemPrompt);
      setAiInsights(insights);

      toast({
        title: "AI Insights Generated",
        description: "Salary analysis and recommendations are ready."
      });
    } catch (error) {
      console.error('Error generating AI insights:', error);
      toast({
        title: "Insights Unavailable",
        description: "Unable to generate AI insights at the moment.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingInsights(false);
    }
  };

  // Memoized tooltip renderer for better performance
  const renderCustomTooltip = useCallback(({ active, payload }: any) => {
    if (active && payload && payload.length >= 2) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 rounded-lg shadow-lg border border-gray-100">
          <p className="font-medium text-gray-900">{data.role}</p>
          <p className="text-sm text-gray-600">
            Market Average: <span className="font-medium">${data.market?.toLocaleString() || 'N/A'}</span>
          </p>
          <p className="text-sm text-gray-600">
            AI Recommended: <span className="font-medium">${data.recommended?.toLocaleString() || 'N/A'}</span>
          </p>
        </div>
      );
    }
    return null;
  }, []);

  if (isLoading) {
    return (
      <div className={expanded ? 'h-full' : ''}>
        <Card className={expanded ? 'h-full border-0 shadow-none' : ''}>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent className={expanded ? 'h-[calc(100%-80px)]' : 'h-[300px]'}>
            <Skeleton className="h-full w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={expanded ? 'h-full' : ''}>
      <Card className={expanded ? 'h-full border-0 shadow-none' : ''}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Salary Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent className={expanded ? 'h-[calc(100%-80px)]' : 'h-[300px]'}>
          {expanded && (
            <div className="mb-6 space-y-4">
              <h3 className="text-lg font-semibold text-gray-700 mb-4">Adjustment Factors</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                    <Briefcase className="h-4 w-4" />
                    Experience
                  </div>
                  <Select value={selectedExperience || ''} onValueChange={setSelectedExperience}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select experience level" />
                    </SelectTrigger>
                    <SelectContent>
                      {EXPERIENCE_LEVELS.map((exp) => (
                        <SelectItem key={exp.years} value={exp.years}>
                          {exp.years} years
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                    <MapPin className="h-4 w-4" />
                    Location
                  </div>
                  <Select value={selectedLocation || ''} onValueChange={setSelectedLocation}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select location" />
                    </SelectTrigger>
                    <SelectContent>
                      {LOCATIONS.map((loc) => (
                        <SelectItem key={loc.name} value={loc.name}>
                          {loc.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                    <Building2 className="h-4 w-4" />
                    Industry
                  </div>
                  <Select value={selectedIndustry || ''} onValueChange={setSelectedIndustry}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select industry" />
                    </SelectTrigger>
                    <SelectContent>
                      {INDUSTRIES.map((ind) => (
                        <SelectItem key={ind.name} value={ind.name}>
                          {ind.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <Separator className="my-6" />

              {/* AI Insights Section */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-700 flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    AI Salary Insights
                  </h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={generateAIInsights}
                    disabled={isGeneratingInsights}
                  >
                    {isGeneratingInsights ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Analyzing...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        Generate Insights
                      </>
                    )}
                  </Button>
                </div>

                {aiInsights && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="text-sm text-gray-700 whitespace-pre-wrap">
                      {aiInsights}
                    </div>
                  </div>
                )}
              </div>

              <Separator className="my-6" />
            </div>
          )}
          <div className={expanded ? 'h-[calc(100%-350px)]' : 'h-full min-h-[250px]'}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={adjustedSalaryData}
                margin={{ top: 20, right: 30, left: 0, bottom: 10 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="role" 
                  angle={-45} 
                  textAnchor="end" 
                  height={70}
                  axisLine={false}
                  tickLine={false}
                  style={{
                    fontSize: '12px',
                    fontFamily: 'inherit',
                  }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  style={{
                    fontSize: '12px',
                    fontFamily: 'inherit',
                  }}
                />
                <Tooltip 
                  content={renderCustomTooltip}
                  cursor={{ fill: 'rgba(236, 237, 254, 0.4)' }}
                />
                <Bar dataKey="market" name="Market Average" radius={[4, 4, 0, 0]}>
                  {adjustedSalaryData.map((_, index) => (
                    <Cell 
                      key={`cell-${index}`}
                      fill={`url(#marketGradient-${index})`}
                    />
                  ))}
                </Bar>
                <Bar dataKey="recommended" name="AI Recommended" radius={[4, 4, 0, 0]}>
                  {adjustedSalaryData.map((_, index) => (
                    <Cell 
                      key={`cell-${index}`}
                      fill={`url(#recommendedGradient-${index})`}
                    />
                  ))}
                </Bar>
                <defs>
                  {adjustedSalaryData.map((_, index) => (
                    <linearGradient
                      key={`marketGradient-${index}`}
                      id={`marketGradient-${index}`}
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="0%" stopColor="#8b5cf6" stopOpacity={0.8} />
                      <stop offset="100%" stopColor="#8b5cf6" stopOpacity={0.3} />
                    </linearGradient>
                  ))}
                  {adjustedSalaryData.map((_, index) => (
                    <linearGradient
                      key={`recommendedGradient-${index}`}
                      id={`recommendedGradient-${index}`}
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="0%" stopColor="#06d6a0" stopOpacity={0.8} />
                      <stop offset="100%" stopColor="#06d6a0" stopOpacity={0.3} />
                    </linearGradient>
                  ))}
                </defs>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};