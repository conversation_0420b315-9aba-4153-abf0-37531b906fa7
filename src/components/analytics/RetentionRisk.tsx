import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { TrendingDown, Loader2, Sparkles, AlertTriangle } from "lucide-react";
import { useRetention } from "@/hooks/useRetention";
import { Skeleton } from "@/components/ui/skeleton";
import { useState } from "react";
import { generateText } from "@/utils/gemini";
import { useToast } from "@/hooks/use-toast";

export const RetentionRisk = () => {
  const { data: retentionPredictions = [], isLoading, error } = useRetention();
  const { toast } = useToast();
  const [aiPredictions, setAiPredictions] = useState<string>("");
  const [isGeneratingPredictions, setIsGeneratingPredictions] = useState(false);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-4 w-4" />
            <Skeleton className="h-6 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center justify-between p-2 border rounded">
                <Skeleton className="h-5 w-24" />
                <div className="flex items-center gap-4">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-6 w-20" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-4 w-4" />
            AI-Predicted Retention Risk
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center p-8">
            <p className="text-muted-foreground">Failed to load retention data. Please try again.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const generateAIPredictions = async () => {
    if (!retentionPredictions.length || isGeneratingPredictions) return;

    setIsGeneratingPredictions(true);
    try {
      const retentionData = retentionPredictions.map(dept =>
        `${dept.department}: Risk Level ${dept.risk_level}, Score ${dept.score}`
      ).join('\n');

      const prompt = `
Analyze the following retention risk data and provide strategic insights:

${retentionData}

Provide analysis including:
1. Overall retention risk assessment across departments
2. Key factors contributing to retention risks
3. Proactive strategies to reduce turnover risk
4. Department-specific recommendations
5. Early warning signs to monitor

Focus on actionable insights that HR and recruitment teams can implement to improve retention.
`;

      const systemPrompt = "You are an expert HR analytics consultant specializing in employee retention. Provide strategic insights that help organizations proactively address retention risks and improve employee satisfaction.";

      const predictions = await generateText(prompt, systemPrompt);
      setAiPredictions(predictions);

      toast({
        title: "AI Predictions Generated",
        description: "Retention risk analysis and recommendations are ready."
      });
    } catch (error) {
      console.error('Error generating AI predictions:', error);
      toast({
        title: "Predictions Unavailable",
        description: "Unable to generate AI predictions at the moment.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingPredictions(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-4 w-4" />
            AI-Predicted Retention Risk
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={generateAIPredictions}
            disabled={isGeneratingPredictions || !retentionPredictions.length}
          >
            {isGeneratingPredictions ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                Generate Insights
              </>
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {retentionPredictions.map((dept, index) => (
            <div key={index} className="flex items-center justify-between p-2 border rounded">
              <span className="font-medium">{dept.department}</span>
              <div className="flex items-center gap-4">
                <span className="text-sm">Score: {dept.score}</span>
                <span className={`px-2 py-1 rounded text-xs ${
                  dept.risk_level === 'Low' ? 'bg-green-100 text-green-800' :
                  dept.risk_level === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {dept.risk_level} Risk
                </span>
              </div>
            </div>
          ))}

          {aiPredictions && (
            <div className="mt-6">
              <div className="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                  <h3 className="font-medium text-orange-900">AI Strategic Analysis</h3>
                </div>
                <div className="text-sm text-gray-700 whitespace-pre-wrap">
                  {aiPredictions}
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};