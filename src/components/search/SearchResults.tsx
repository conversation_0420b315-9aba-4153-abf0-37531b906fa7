import { Link } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge"; 
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { Dialog } from "@/components/ui/dialog";
import { JobDetailsDialog } from "@/components/job/JobDetailsDialog";
import { CandidateType } from "@/types/candidate";
import { Job } from "@/hooks/useJobs";
import { highlightSearchResults } from "@/utils/search";
import { MapPin, Clock, DollarSign, Users, Briefcase, Star, Calendar, Mail, Phone, Tag } from "lucide-react";

interface SearchResultsProps {
  results: (CandidateType | Job)[];
  isLoading: boolean;
  category: string;
  query?: string;
}

function isCandidateType(item: CandidateType | Job): item is CandidateType {
  return 'role' in item && 'email' in item;
}

function isJobType(item: CandidateType | Job): item is Job {
  return 'title' in item && 'department' in item;
}

export function SearchResults({ results, isLoading, category, query = "" }: SearchResultsProps) {
  const navigate = useNavigate();
  const [selectedJobDetails, setSelectedJobDetails] = useState<any>(null);

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <Skeleton className="h-16 w-16 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!results?.length) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-muted-foreground">
          {query ? "No results found. Try adjusting your search terms or filters." : "Enter a search query to see results."}
        </CardContent>
      </Card>
    );
  }

  const handleItemClick = (item: CandidateType | Job) => {
    if (isCandidateType(item)) { 
      navigate(`/candidates/${item.id}`);
    } else if (isJobType(item)) {
      // For jobs, show the job details dialog instead of navigating
      const jobDetails = {
        ...item,
        company: "Your Company",
        match: 85,
        requirements: item.requirements || [
          `${item.experience_required || 'No specific'} experience`,
          "Strong communication skills",
          "Team collaboration",
          "Problem solving abilities"
        ],
        benefits: item.benefits || [
          "Health insurance",
          "Flexible work hours", 
          "Professional development",
          "Competitive salary"
        ],
        postedDate: new Date(item.created_at).toLocaleDateString(),
        salary: item.salary_range || 'Competitive salary',
        applicants: item.applicant_count
      };
      setSelectedJobDetails(jobDetails);
    }
  };

  return (
    <div>
      <div className="space-y-4">
        {results.map((item) => (
          <Card key={item.id} className="hover:bg-accent/50 transition-colors cursor-pointer" onClick={() => handleItemClick(item)}>
            <CardContent className="p-6 relative">
              {isCandidateType(item) ? (
                <div className="flex flex-col gap-4">
                  <div className="flex items-start space-x-3 sm:space-x-4 flex-1">
                    <Avatar className="h-12 w-12 sm:h-16 sm:w-16 shrink-0">
                      <AvatarImage src={item.avatar} />
                      <AvatarFallback>{item.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <Link to={`/candidates/${item.id}`} className="font-medium text-base sm:text-lg hover:underline block">
                        <span dangerouslySetInnerHTML={{ 
                          __html: highlightSearchResults(item.name, query) 
                        }} />
                      </Link>
                      <p className="text-sm sm:text-base text-muted-foreground">
                        <span dangerouslySetInnerHTML={{ 
                          __html: highlightSearchResults(item.role, query) 
                        }} />
                      </p>
                      <div className="flex flex-wrap items-center gap-2 sm:gap-4 mt-2 text-xs sm:text-sm text-muted-foreground">
                        <div className="flex items-center gap-1.5">
                          <MapPin className="w-3 h-3 sm:w-4 sm:h-4" />
                          <span className="truncate" dangerouslySetInnerHTML={{ 
                            __html: highlightSearchResults(item.location, query) 
                          }} />
                        </div>
                        {item.experience && (
                          <div className="flex items-center gap-1.5">
                            <Briefcase className="w-3 h-3 sm:w-4 sm:h-4" />
                            <span className="truncate">{item.experience}</span>
                          </div>
                        )}
                        {item.email && (
                          <div className="flex items-center gap-1.5 hidden sm:flex">
                            <Mail className="w-4 h-4" />
                            <span className="truncate">{item.email}</span>
                          </div>
                        )}
                        {item.phone && (
                          <div className="flex items-center gap-1.5 hidden sm:flex">
                            <Phone className="w-4 h-4" />
                            <span className="truncate">{item.phone}</span>
                          </div>
                        )}
                      </div>
                      <div className="mt-3 flex flex-wrap gap-1 sm:gap-2">
                        {item.tags.slice(0, 2).map((tag) => (
                          <Badge key={tag} variant="secondary" className="px-1.5 py-0.5 text-xs">
                            <Tag className="w-2 h-2 sm:w-3 sm:h-3 mr-1" />
                            <span dangerouslySetInnerHTML={{ 
                              __html: highlightSearchResults(tag, query) 
                            }} />
                          </Badge>
                        ))}
                        {item.tags.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{item.tags.length - 2} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-row sm:flex-col items-center justify-between sm:items-end gap-2 border-t pt-3 sm:border-t-0 sm:pt-0">
                    <div className="flex items-center gap-2">
                      <Badge className="text-xs">Candidate</Badge>
                      <div className="flex items-center gap-1 text-primary font-medium text-sm">
                        <Star className="w-3 h-3 sm:w-4 sm:h-4 fill-primary" />
                        <span>{item.relationshipScore}% match</span>
                      </div>
                    </div>
                    <Button 
                      size="sm" 
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/candidates/${item.id}`);
                      }}
                      className="text-xs sm:text-sm"
                    >
                      View Profile
                    </Button>
                  </div>
                </div>
              ) : isJobType(item) ? (
                <div className="flex flex-col gap-4">
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-base sm:text-lg">
                      <span dangerouslySetInnerHTML={{ 
                        __html: highlightSearchResults(item.title, query) 
                      }} />
                    </div>
                    <p className="text-sm sm:text-base text-muted-foreground">
                      <span dangerouslySetInnerHTML={{ 
                        __html: highlightSearchResults(item.department, query) 
                      }} />
                    </p>
                    <div className="flex flex-wrap items-center gap-2 sm:gap-4 mt-2 text-xs sm:text-sm text-muted-foreground">
                      <div className="flex items-center gap-1.5">
                        <MapPin className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="truncate" dangerouslySetInnerHTML={{ 
                          __html: highlightSearchResults(item.location, query) 
                        }} />
                      </div>
                      <div className="flex items-center gap-1.5">
                        <Briefcase className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="truncate">{item.job_type}</span>
                      </div>
                      {item.salary_range && (
                        <div className="flex items-center gap-1.5 hidden sm:flex">
                          <DollarSign className="w-4 h-4" />
                          <span className="truncate">{item.salary_range}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-1.5">
                        <Calendar className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="truncate">Posted {new Date(item.created_at).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-1.5">
                        <Users className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="truncate">{item.applicant_count} applicants</span>
                      </div>
                    </div>
                    <div className="mt-3 flex flex-wrap gap-1 sm:gap-2">
                      {item.is_urgent && (
                        <Badge variant="destructive" className="text-xs">
                          Urgent
                        </Badge>
                      )}
                      {item.experience_required && (
                        <Badge variant="outline" className="text-xs">
                          {item.experience_required}
                        </Badge>
                      )}
                      {item.requirements && item.requirements.length > 0 && (
                        <Badge variant="secondary" className="text-xs">
                          {item.requirements.length} requirements
                        </Badge>
                      )}
                      {item.benefits && item.benefits.length > 0 && (
                        <Badge variant="outline" className="bg-green-50 border-green-200 text-green-700 text-xs">
                          {item.benefits.length} benefits
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-row sm:flex-col items-center justify-between sm:items-end gap-2 border-t pt-3 sm:border-t-0 sm:pt-0">
                    <Badge className="text-xs">Job</Badge>
                    <Button 
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleItemClick(item);
                      }}
                      className="text-xs sm:text-sm"
                    >
                      View Job
                    </Button>
                  </div>
                </div>
              ) : null}
            </CardContent>
          </Card>
        ))}
      </div>
      
      {/* Job Details Dialog */}
      {selectedJobDetails && (
        <Dialog open={!!selectedJobDetails} onOpenChange={() => setSelectedJobDetails(null)}>
          <JobDetailsDialog
            job={selectedJobDetails}
            onClose={() => setSelectedJobDetails(null)}
          />
        </Dialog>
      )}
    </div>
  );
}