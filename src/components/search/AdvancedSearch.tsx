import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SearchFilters } from "@/types/search";
import { Plus, X, Sparkles, Loader2, Lightbulb } from "lucide-react";
import { generateText } from "@/utils/gemini";
import { useToast } from "@/hooks/use-toast";

interface AdvancedSearchProps {
  filters: SearchFilters;
  onChange: (filters: SearchFilters) => void;
}

export function AdvancedSearch({ filters, onChange }: AdvancedSearchProps) {
  const { toast } = useToast();
  const [newSkill, setNewSkill] = useState("");
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const [isGeneratingSuggestions, setIsGeneratingSuggestions] = useState(false);

  const generateAISuggestions = async () => {
    if (isGeneratingSuggestions) return;

    setIsGeneratingSuggestions(true);
    try {
      const currentFilters = {
        location: filters.location?.address || '',
        skills: filters.skills || [],
        experience: filters.experience || '',
        jobType: filters.jobType || '',
        department: filters.department || ''
      };

      const prompt = `
Analyze the following search criteria and suggest improvements for better candidate search results:

Current Search Filters:
- Location: ${currentFilters.location}
- Skills: ${currentFilters.skills.map(s => s.name).join(', ')}
- Experience: ${currentFilters.experience}
- Job Type: ${currentFilters.jobType}
- Department: ${currentFilters.department}

Provide 4-6 specific suggestions to improve search effectiveness, such as:
1. Alternative keywords or synonyms
2. Related skills to consider
3. Location variations or remote options
4. Experience level adjustments
5. Additional filter combinations

Return only a JSON array of suggestion strings, no explanations.
Example format: ["Try 'Software Engineer' instead of 'Developer'", "Add 'TypeScript' to complement JavaScript skills", "Consider 'Remote' location option"]
`;

      const systemPrompt = "You are an expert recruitment search consultant. Provide practical suggestions that help recruiters find better candidates through improved search criteria.";

      const response = await generateText(prompt, systemPrompt);
      const cleanedResponse = response.replace(/```json\s*/, '').replace(/```\s*$/, '').trim();
      const suggestions = JSON.parse(cleanedResponse);

      if (Array.isArray(suggestions) && suggestions.length > 0) {
        setAiSuggestions(suggestions);
        toast({
          title: "AI Suggestions Generated",
          description: "Search optimization suggestions are ready."
        });
      }
    } catch (error) {
      console.error('Error generating AI suggestions:', error);
      toast({
        title: "Suggestions Unavailable",
        description: "Unable to generate AI suggestions at the moment.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingSuggestions(false);
    }
  };

  const handleAddSkill = () => {
    if (!newSkill) return;
    onChange({
      ...filters,
      skills: [
        ...(filters.skills || []),
        { name: newSkill, required: true }
      ]
    });
    setNewSkill("");
  };

  const handleRemoveSkill = (skillName: string) => {
    onChange({
      ...filters,
      skills: filters.skills?.filter(skill => skill.name !== skillName)
    });
  };

  const handleSkillProficiencyChange = (skillName: string, proficiency: 'beginner' | 'intermediate' | 'expert') => {
    onChange({
      ...filters,
      skills: filters.skills?.map(skill => 
        skill.name === skillName 
          ? { ...skill, proficiency } 
          : skill
      )
    });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Advanced Search</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={generateAISuggestions}
            disabled={isGeneratingSuggestions}
          >
            {isGeneratingSuggestions ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                AI Suggestions
              </>
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-4 space-y-4 sm:space-y-6">
        {/* AI Suggestions Section */}
        {aiSuggestions.length > 0 && (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <Lightbulb className="h-4 w-4 text-blue-600" />
              <h3 className="font-medium text-blue-900">AI Search Suggestions</h3>
            </div>
            <div className="space-y-2">
              {aiSuggestions.map((suggestion, index) => (
                <div key={index} className="text-sm text-gray-700 bg-white rounded p-2 border border-blue-100">
                  {suggestion}
                </div>
              ))}
            </div>
          </div>
        )}
        <div className="space-y-2">
          <Label>Location</Label>
          <div className="flex flex-col sm:flex-row gap-4">
            <Input
              placeholder="Enter location"
              value={filters.location?.address || ""}
              onChange={(e) => onChange({
                ...filters,
                location: {
                  address: e.target.value,
                  radius: filters.location?.radius || 25
                }
              })}
              className="flex-1"
            />
            <div className="w-full sm:w-48">
              <Label>Radius (miles)</Label>
              <Slider
                value={[filters.location?.radius || 25]}
                min={5}
                max={100}
                step={5}
                onValueChange={([value]) => onChange({
                  ...filters,
                  location: {
                    address: filters.location?.address || "",
                    radius: value
                  }
                })}
              />
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label>Skills</Label>
          <div className="flex flex-col sm:flex-row gap-2">
            <Input
              placeholder="Add a skill"
              value={newSkill}
              onChange={(e) => setNewSkill(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleAddSkill()}
              className="flex-1"
            />
            <Button onClick={handleAddSkill} size="icon" className="shrink-0">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-2">
            {filters.skills?.map((skill) => (
              <div
                key={skill.name}
                className="flex flex-col sm:flex-row items-start sm:items-center gap-2 bg-secondary p-2 rounded-md min-w-0"
              >
                <span className="truncate text-sm">{skill.name}</span>
                <Select
                  value={skill.proficiency || 'any'}
                  onValueChange={(value: any) => handleSkillProficiencyChange(skill.name, value)}
                >
                  <SelectTrigger className="h-7 w-full sm:w-32">
                    <SelectValue placeholder="Proficiency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any">Any Level</SelectItem>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="expert">Expert</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 shrink-0"
                  onClick={() => handleRemoveSkill(skill.name)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-3 sm:space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="remote-only"
              checked={filters.remoteOnly}
              onCheckedChange={(checked) => onChange({
                ...filters,
                remoteOnly: checked
              })}
            />
            <Label htmlFor="remote-only" className="text-sm sm:text-base">Remote Only</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="visa-sponsor"
              checked={filters.visaSponsor}
              onCheckedChange={(checked) => onChange({
                ...filters,
                visaSponsor: checked
              })}
            />
            <Label htmlFor="visa-sponsor" className="text-sm sm:text-base">Visa Sponsorship Available</Label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}