import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Clock, Star, TrendingUp, Sparkles, Loader2 } from "lucide-react";
import { useState, useEffect } from "react";
import { generateText } from "@/utils/gemini";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface SearchSuggestionsProps {
  onSelect: (query: string) => void;
}

export function SearchSuggestions({ onSelect }: SearchSuggestionsProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [recentSearches, setRecentSearches] = useState<string[]>([
    "Frontend Developer",
    "Product Manager",
    "Remote positions",
    "Senior Engineer"
  ]);
  const [trendingSearches, setTrendingSearches] = useState<string[]>([
    "React Developer",
    "DevOps Engineer",
    "Machine Learning",
    "UI/UX Designer"
  ]);
  const [popularSkills, setPopularSkills] = useState<string[]>([
    "TypeScript",
    "React",
    "Node.js",
    "Python"
  ]);
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const [isLoadingAI, setIsLoadingAI] = useState(false);

  // Load user's search history and generate AI suggestions
  useEffect(() => {
    const loadPersonalizedSuggestions = async () => {
      if (!user) return;

      try {
        // Load recent searches from search history
        const { data: searchHistory } = await supabase
          .from('search_history')
          .select('query')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(5);

        if (searchHistory && searchHistory.length > 0) {
          setRecentSearches(searchHistory.map(h => h.query));
        }

        // Load trending searches based on all users' recent activity
        const { data: trendingData } = await supabase
          .from('search_history')
          .select('query')
          .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
          .order('created_at', { ascending: false })
          .limit(20);

        if (trendingData && trendingData.length > 0) {
          // Count frequency and get top trending
          const queryCount = trendingData.reduce((acc, item) => {
            acc[item.query] = (acc[item.query] || 0) + 1;
            return acc;
          }, {} as Record<string, number>);

          const trending = Object.entries(queryCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 4)
            .map(([query]) => query);

          setTrendingSearches(trending);
        }

        // Load popular skills from candidates
        const { data: candidatesData } = await supabase
          .from('candidates')
          .select('skills')
          .not('skills', 'is', null)
          .limit(100);

        if (candidatesData && candidatesData.length > 0) {
          const allSkills = candidatesData
            .flatMap(c => {
              if (!c.skills) return [];

              // Handle different skill data formats
              if (Array.isArray(c.skills)) {
                return c.skills.map(skill => {
                  if (typeof skill === 'string') return skill;
                  if (skill && typeof skill === 'object' && skill.name) return skill.name;
                  return null;
                }).filter(Boolean);
              }

              // If skills is a single object or string
              if (typeof c.skills === 'string') return [c.skills];
              if (c.skills && typeof c.skills === 'object' && c.skills.name) return [c.skills.name];

              return [];
            })
            .filter(Boolean);

          const skillCount = allSkills.reduce((acc, skill) => {
            if (typeof skill === 'string' && skill.trim()) {
              acc[skill] = (acc[skill] || 0) + 1;
            }
            return acc;
          }, {} as Record<string, number>);

          const topSkills = Object.entries(skillCount)
            .sort(([,a], [,b]) => (b as number) - (a as number))
            .slice(0, 4)
            .map(([skill]) => skill);

          if (topSkills.length > 0) {
            setPopularSkills(topSkills);
          }
        }

      } catch (error) {
        console.error('Error loading personalized suggestions:', error);
      }
    };

    loadPersonalizedSuggestions();
  }, [user]);

  // Generate AI-powered suggestions based on user context
  const generateAISuggestions = async () => {
    if (!user || isLoadingAI) return;

    setIsLoadingAI(true);
    try {
      const prompt = `
Based on the following recruitment context, generate 4 intelligent search suggestions that would be valuable for a recruiter:

Recent searches: ${recentSearches.join(', ')}
Trending searches: ${trendingSearches.join(', ')}
Popular skills: ${popularSkills.join(', ')}

Generate search suggestions that are:
1. Relevant to current hiring trends
2. Complementary to recent search patterns
3. Focused on in-demand roles and skills
4. Actionable for recruitment purposes

Return only a JSON array of 4 search suggestion strings, no explanations.
Example format: ["Senior Full Stack Developer", "Remote Data Scientist", "Product Manager with AI experience", "DevOps Engineer - Kubernetes"]
`;

      const systemPrompt = "You are an expert recruitment consultant. Generate practical, relevant search suggestions for recruiters based on current market trends and user behavior.";

      const response = await generateText(prompt, systemPrompt);
      const cleanedResponse = response.replace(/```json\s*/, '').replace(/```\s*$/, '').trim();
      const suggestions = JSON.parse(cleanedResponse);

      if (Array.isArray(suggestions) && suggestions.length > 0) {
        setAiSuggestions(suggestions);
      }
    } catch (error) {
      console.error('Error generating AI suggestions:', error);
      toast({
        title: "AI Suggestions Unavailable",
        description: "Unable to generate AI suggestions at the moment.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingAI(false);
    }
  };

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Recent Searches
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {recentSearches.map((search) => (
              <Button
                key={search}
                variant="outline"
                size="sm"
                onClick={() => onSelect(search)}
              >
                {search}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Trending Now
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {trendingSearches.map((search) => (
              <Button
                key={search}
                variant="outline"
                size="sm"
                onClick={() => onSelect(search)}
              >
                {search}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-4 w-4" />
            Popular Skills
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {popularSkills.map((skill) => (
              <Button
                key={skill}
                variant="outline"
                size="sm"
                onClick={() => onSelect(skill)}
              >
                {skill}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI-Powered Suggestions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            AI Suggestions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Button
              variant="outline"
              size="sm"
              onClick={generateAISuggestions}
              disabled={isLoadingAI}
              className="w-full"
            >
              {isLoadingAI ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Generate AI Suggestions
                </>
              )}
            </Button>

            {aiSuggestions.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-3">
                {aiSuggestions.map((suggestion) => (
                  <Button
                    key={suggestion}
                    variant="outline"
                    size="sm"
                    onClick={() => onSelect(suggestion)}
                    className="text-xs"
                  >
                    {suggestion}
                  </Button>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}