
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { UserPlus, Plus, Search, BarChart3 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { AddCandidateDialog } from "@/components/candidate/AddCandidateDialog";
import { EditJobDialog } from "@/components/job/EditJobDialog";
import { v4 as uuidv4 } from "uuid";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { JobPostingForm } from "@/components/job/JobPostingForm";

export function QuickActions() {
  const navigate = useNavigate();
  const [showAddCandidate, setShowAddCandidate] = useState(false);
  const [showCreateJob, setShowCreateJob] = useState(false);

  // Blank job object for creation
  const blankJob = {
    id: uuidv4(),
    title: "",
    department: "",
    location: "",
    job_type: "Full-time",
    salary_range: "",
    experience_required: "",
    description: "",
    requirements: [""],
    benefits: [""],
    is_urgent: false,
    is_active: true,
    applicant_count: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    user_id: "",
  };

  const actions = [
    {
      title: "Add Candidate",
      description: "Add a new candidate to your talent pool",
      icon: UserPlus,
      onClick: () => setShowAddCandidate(true),
      variant: "default" as const,
    },
    {
      title: "Create Job",
      description: "Post a new job opening",
      icon: Plus,
      onClick: () => setShowCreateJob(true),
      variant: "outline" as const,
    },
    {
      title: "Search Talent",
      description: "Find candidates for your roles",
      icon: Search,
      onClick: () => navigate('/search'),
      variant: "outline" as const,
    },
    {
      title: "View Analytics",
      description: "Check your hiring metrics",
      icon: BarChart3,
      onClick: () => navigate('/analytics'),
      variant: "outline" as const,
    },
  ];

  // Handler for saving a new job (stub for now)
  const handleCreateJob = (job) => {
    // TODO: Implement job creation logic (API call, etc.)
    setShowCreateJob(false);
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {actions.map((action) => (
              <Button
                key={action.title}
                variant={action.variant}
                onClick={action.onClick}
                className="h-auto min-h-[80px] p-4 flex flex-col items-start gap-2 text-left whitespace-normal"
              >
                <div className="flex items-center gap-2 w-full">
                  <action.icon className="h-4 w-4 flex-shrink-0" />
                  <span className="font-medium text-sm leading-tight">{action.title}</span>
                </div>
                <span className="text-xs opacity-80 leading-relaxed break-words w-full">{action.description}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
      
      <AddCandidateDialog 
        open={showAddCandidate} 
        onOpenChange={setShowAddCandidate} 
      />
      <Dialog open={showCreateJob} onOpenChange={setShowCreateJob}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Post a Job</DialogTitle>
          </DialogHeader>
          <JobPostingForm onSuccess={() => setShowCreateJob(false)} />
        </DialogContent>
      </Dialog>
    </>
  );
}
