import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Mail, Send, FileText, Sparkles, Loader2, Info } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useCreateMessage } from "@/hooks/useCreateMessage";
import { useMessageTemplates } from "@/hooks/useMessageTemplates";
import { CandidateType } from "@/types/candidate";
import { generateText } from "@/utils/gemini";

interface EmailComposerProps {
  candidate?: CandidateType;
  onSuccess?: () => void;
}

export function EmailComposer({ candidate, onSuccess }: EmailComposerProps) {
  const [to, setTo] = useState(candidate?.email || "");
  const [cc, setCc] = useState("");
  const [subject, setSubject] = useState("");
  const [content, setContent] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState("");
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  
  const { toast } = useToast();
  const createMessage = useCreateMessage();
  const { data: templates = [] } = useMessageTemplates();

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setSubject(template.subject);

      // Replace common placeholders with actual values
      let processedContent = template.content;

      // Replace candidate-specific placeholders
      if (candidate?.name) {
        processedContent = processedContent.replace(/\[Name\]/g, candidate.name);
        processedContent = processedContent.replace(/\{candidateName\}/g, candidate.name);
      }

      // Replace common company placeholders with defaults
      processedContent = processedContent.replace(/\[Company\]/g, "HireLogix");
      processedContent = processedContent.replace(/\[Your Name\]/g, "Hiring Team");

      // Leave position-specific placeholders for manual editing
      // [Position], [Salary], [Benefits] will remain as placeholders

      setContent(processedContent);

      // Show a toast to inform user about placeholder replacement
      toast({
        title: "Template Applied",
        description: "Template has been applied. Please review and customize the placeholders as needed.",
      });
    }
  };

  const generateAIContent = async () => {
    if (!candidate) {
      toast({
        title: "Missing Information",
        description: "Please select a candidate to generate AI content.",
        variant: "destructive"
      });
      return;
    }

    setIsGeneratingAI(true);
    try {
      const prompt = `
        Write a professional recruitment email for a candidate named ${candidate.name} who is a ${candidate.role}.
        
        Context:
        - Candidate: ${candidate.name}
        - Position: ${candidate.role}
        - Experience: ${candidate.experience || "Not specified"}
        - Skills: ${candidate.skills?.map((s: any) => typeof s === 'string' ? s : s.name).join(', ') || "Not specified"}
        
        Write a personalized, professional email that:
        1. Shows genuine interest in their background
        2. Mentions specific skills or experience that caught your attention
        3. Invites them for next steps (interview or call)
        4. Maintains a professional but friendly tone
        
        Format the response as:
        Subject: [Email subject line]
        
        [Email body content]
      `;

      const response = await generateText(prompt, "You are a professional recruiter. Write personalized, engaging recruitment emails that show genuine interest in candidates.");
      
      const lines = response.split('\n');
      const subjectLine = lines.find(line => line.startsWith('Subject:'))?.replace('Subject:', '').trim();
      const bodyStart = lines.findIndex(line => line.startsWith('Subject:')) + 1;
      const bodyContent = lines.slice(bodyStart + 1).join('\n').trim();

      if (subjectLine) setSubject(subjectLine);
      if (bodyContent) setContent(bodyContent);

      toast({
        title: "AI Content Generated",
        description: "Personalized email content has been generated for this candidate."
      });
    } catch (error) {
      console.error('Error generating AI content:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate AI content. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingAI(false);
    }
  };

  const handleSend = async () => {
    if (!to || !subject || !content) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    try {
      await createMessage.mutateAsync({
        sender_name: "Your Name", // This would come from user profile
        sender_email: "<EMAIL>", // This would come from user profile
        content: `Subject: ${subject}\n\nTo: ${to}\n${cc ? `CC: ${cc}\n` : ''}\n\n${content}`,
        status: "read"
      });

      toast({
        title: "Email Sent",
        description: `Email sent successfully to ${to}.`
      });

      // Reset form
      setTo(candidate?.email || "");
      setCc("");
      setSubject("");
      setContent("");
      setSelectedTemplate("");
      
      onSuccess?.();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send email. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Compose Email
          {candidate && <span className="text-muted-foreground">to {candidate.name}</span>}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Select value={selectedTemplate} onValueChange={(value) => {
            setSelectedTemplate(value);
            handleTemplateSelect(value);
          }}>
            <SelectTrigger className="w-auto">
              <FileText className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Use Template" />
            </SelectTrigger>
            <SelectContent>
              {templates.map((template) => (
                <SelectItem key={template.id} value={template.id}>
                  {template.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {candidate && (
            <Button
              variant="outline"
              onClick={generateAIContent}
              disabled={isGeneratingAI}
            >
              {isGeneratingAI ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  AI Generate
                </>
              )}
            </Button>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="to">To *</Label>
          <Input
            id="to"
            type="email"
            placeholder="<EMAIL>"
            value={to}
            onChange={(e) => setTo(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="cc">CC</Label>
          <Input
            id="cc"
            type="email"
            placeholder="<EMAIL>"
            value={cc}
            onChange={(e) => setCc(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="subject">Subject *</Label>
          <Input
            id="subject"
            placeholder="Email subject"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="content">Message *</Label>
          <Textarea
            id="content"
            placeholder="Type your message here..."
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={8}
          />
          {selectedTemplate && (
            <div className="flex items-start gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="text-blue-900 font-medium mb-1">Available placeholders:</p>
                <div className="flex flex-wrap gap-1">
                  <Badge variant="secondary" className="text-xs">[Position]</Badge>
                  <Badge variant="secondary" className="text-xs">[Salary]</Badge>
                  <Badge variant="secondary" className="text-xs">[Benefits]</Badge>
                  <Badge variant="secondary" className="text-xs">[Date]</Badge>
                  <Badge variant="secondary" className="text-xs">[Time]</Badge>
                </div>
                <p className="text-blue-700 text-xs mt-1">
                  Replace these placeholders with specific information for your message.
                </p>
              </div>
            </div>
          )}
        </div>

        <div className="flex gap-2">
          <Button 
            onClick={handleSend} 
            disabled={createMessage.isPending || !to || !subject || !content}
            className="flex-1"
          >
            {createMessage.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Send Email
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}