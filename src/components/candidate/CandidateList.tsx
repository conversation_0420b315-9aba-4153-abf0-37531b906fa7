import { useEffect, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { MapPin, Mail, Phone } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { useCandidates } from "@/hooks/useCandidates";
import { CandidateType } from "@/types/candidate";
import { useAuth } from "@/contexts/AuthContext";
import { Skeleton } from "@/components/ui/skeleton";

export function CandidateList() {
  const navigate = useNavigate();
  const { user, loading: authLoading } = useAuth();
  const { data: candidates, isLoading: loading, error } = useCandidates();

  // Show loading state while authentication is being checked
  if (authLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-lg text-muted-foreground">Loading...</p>
      </div>
    );
  }

  // Show authentication required message
  if (!user) {
    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-lg text-muted-foreground">Please sign in to view candidates</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-5 w-48" />
                  <Skeleton className="h-4 w-32" />
                  <div className="flex gap-4">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-28" />
                  </div>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <Skeleton className="h-4 w-16" />
                  <div className="flex gap-1">
                    <Skeleton className="h-6 w-12" />
                    <Skeleton className="h-6 w-12" />
                    <Skeleton className="h-6 w-12" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-lg text-muted-foreground">Error loading candidates</p>
      </div>
    );
  }

  if (!candidates || candidates.length === 0) {
    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-lg text-muted-foreground">No candidates found. Create your first candidate to get started!</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {candidates.map((candidate) => (
        <Card 
          key={candidate.id} 
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => navigate(`/candidates/${candidate.id}`)}
        >
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
              <Avatar className="h-12 w-12 sm:h-12 sm:w-12 self-start sm:self-center flex-shrink-0">
                <AvatarImage src={candidate.avatar} alt={candidate.name} />
                <AvatarFallback>{candidate.name.split(" ").map(n => n[0]).join("")}</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-lg truncate">{candidate.name}</h3>
                <p className="text-muted-foreground truncate">{candidate.role}</p>
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mt-2 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1 min-w-0">
                    <MapPin className="w-4 h-4 flex-shrink-0" />
                    <span className="truncate">{candidate.location || 'Location not specified'}</span>
                  </div>
                  <div className="flex items-center gap-1 min-w-0">
                    <Mail className="w-4 h-4 flex-shrink-0" />
                    <span className="truncate">{candidate.email}</span>
                  </div>
                  <div className="flex items-center gap-1 min-w-0">
                    <Phone className="w-4 h-4 flex-shrink-0" />
                    <span className="truncate">{candidate.phone || 'Phone not provided'}</span>
                  </div>
                </div>
              </div>
              <div className="flex flex-row sm:flex-col justify-between sm:items-end gap-2 sm:gap-2">
                <div className="text-sm font-medium">Score: {candidate.relationshipScore}%</div>
                <div className="flex flex-wrap gap-1 justify-end">
                  {candidate.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}