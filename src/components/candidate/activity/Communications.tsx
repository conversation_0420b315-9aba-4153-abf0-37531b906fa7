
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Mail, Phone, MessageSquare } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";

interface CommunicationsProps {
  candidateId: string;
}

export function Communications({ candidateId }: CommunicationsProps) {
  const { user } = useAuth();

  const fetchMessages = async () => {
      if (!user) return [];
      
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
  };
  
  const { records: messages = [], isLoading } = useRealtimeCollection(
    'messages',
    fetchMessages,
    'public'
  );

  // Filter messages that might be related to this candidate (basic implementation)
  const candidateMessages = messages.filter(msg => 
    msg.content.toLowerCase().includes('interview') || 
    msg.content.toLowerCase().includes('candidate') ||
    msg.sender_email.includes('@') // Basic filter - in real app you'd have better candidate linking
  );

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Communications History</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-muted rounded" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Communications History</CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          {candidateMessages.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="text-4xl mb-4">💬</div>
              <p className="text-muted-foreground">
                No communications found. Messages and calls will appear here as you interact with this candidate.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {candidateMessages.map((comm) => (
                <div
                  key={comm.id}
                  className="p-4 border rounded-lg hover:bg-accent/50"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <Mail className="w-4 h-4" />
                      <span className="font-medium">Email</span>
                    </div>
                    <Badge variant="outline">{comm.status}</Badge>
                  </div>
                  <p className="text-sm font-medium mb-1">From: {comm.sender_name}</p>
                  <p className="text-sm text-muted-foreground">
                    {comm.content.substring(0, 100)}
                    {comm.content.length > 100 ? '...' : ''}
                  </p>
                  <p className="text-xs text-muted-foreground mt-2">
                    {new Date(comm.created_at).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
