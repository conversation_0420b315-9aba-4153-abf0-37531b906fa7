
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MessageSquare, Mail, Phone, Calendar, FileText, User } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";

interface RecentActivityProps {
  candidateId: string;
}

interface ActivityItem {
  id: string;
  type: string;
  description: string;
  date: string;
  icon: any;
}

export function RecentActivity({ candidateId }: RecentActivityProps) {
  const { user } = useAuth();

  // Create query functions for each data type
  const fetchInterviews = async () => {
      if (!user) return [];
      
      const { data, error } = await supabase
        .from('candidate_interviews')
        .select('*')
        .eq('candidate_id', candidateId)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;
      return data || [];
  };

  const fetchNotes = async () => {
      if (!user) return [];
      
      const { data, error } = await supabase
        .from('candidate_notes')
        .select('*')
        .eq('candidate_id', candidateId)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;
      return data || [];
  };

  const fetchDocuments = async () => {
      if (!user) return [];
      
      const { data, error } = await supabase
        .from('candidate_documents')
        .select('*')
        .eq('candidate_id', candidateId)
        .order('uploaded_at', { ascending: false })
        .limit(5);

      if (error) throw error;
      return data || [];
  };

  // Use realtime subscriptions for each data type
  const { records: interviews = [] } = useRealtimeCollection(
    'candidate_interviews',
    fetchInterviews,
    'public',
    `candidate_id=eq.${candidateId}`
  );
  
  const { records: notes = [] } = useRealtimeCollection(
    'candidate_notes',
    fetchNotes,
    'public',
    `candidate_id=eq.${candidateId}`
  );
  
  const { records: documents = [] } = useRealtimeCollection(
    'candidate_documents',
    fetchDocuments,
    'public',
    `candidate_id=eq.${candidateId}`
  );

  // Combine and sort all activities
  const activities: ActivityItem[] = [
    ...interviews.map(interview => ({
      id: interview.id,
      type: "interview",
      description: `${interview.status === 'scheduled' ? 'Scheduled' : 'Completed'} ${interview.interview_type}`,
      date: new Date(interview.created_at).toLocaleString(),
      icon: Calendar,
    })),
    ...notes.map(note => ({
      id: note.id,
      type: "note",
      description: `Added note: ${note.content.substring(0, 50)}${note.content.length > 50 ? '...' : ''}`,
      date: new Date(note.created_at).toLocaleString(),
      icon: MessageSquare,
    })),
    ...documents.map(doc => ({
      id: doc.id,
      type: "document",
      description: `Uploaded document: ${doc.name}`,
      date: new Date(doc.uploaded_at).toLocaleString(),
      icon: FileText,
    })),
  ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 10);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          {activities.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="text-4xl mb-4">📋</div>
              <p className="text-muted-foreground">
                No recent activity found. Activity will appear here as you interact with this candidate.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {activities.map((activity) => (
                <div
                  key={activity.id}
                  className="flex items-start gap-4 pb-4 border-b last:border-0"
                >
                  <div className="p-2 bg-muted rounded-full">
                    <activity.icon className="w-4 h-4" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm">{activity.description}</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {activity.date}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
