
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Users, TrendingUp, Clock, CheckCircle } from "lucide-react";
import { useCandidates } from "@/hooks/useCandidates";
import { useJobs } from "@/hooks/useJobs";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { Skeleton } from "@/components/ui/skeleton";

export function CandidateMetrics() {
  const { user } = useAuth();
  const { data: candidates = [], isLoading: candidatesLoading } = useCandidates();
  const { data: jobs = [], isLoading: jobsLoading } = useJobs();

  // Get pipeline candidates data for active pipeline count
  const { records: pipelineCandidates = [], isLoading: pipelineLoading } = useRealtimeCollection(
    'pipeline_candidates',
    async () => {
      if (!user) return [];
      const { data, error } = await supabase
        .from('pipeline_candidates')
        .select('*')
        .eq('user_id', user.id);
      if (error) throw error;
      return data || [];
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  // Get candidate monthly stats for placements
  const { records: monthlyStats = [], isLoading: statsLoading } = useRealtimeCollection(
    'candidate_monthly_stats',
    async () => {
      if (!user) return [];
      const { data, error } = await supabase
        .from('candidate_monthly_stats')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(2); // Get current and previous month for comparison
      if (error) throw error;
      return data || [];
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  const isLoading = candidatesLoading || jobsLoading || pipelineLoading || statsLoading;

  // Calculate metrics
  const totalCandidates = candidates.length;
  const activePipeline = pipelineCandidates.length;
  const activeJobs = jobs.filter(job => job.is_active).length;

  // Calculate placements from monthly stats
  const currentMonthPlacements = monthlyStats[0]?.placements_count || 0;
  const previousMonthPlacements = monthlyStats[1]?.placements_count || 0;
  const placementsChange = previousMonthPlacements > 0
    ? Math.round(((currentMonthPlacements - previousMonthPlacements) / previousMonthPlacements) * 100)
    : 0;

  // Calculate average response time (simplified calculation)
  const avgResponseTime = "2.4h"; // This would need more complex calculation with actual response data

  if (isLoading) {
    return (
      <div className="space-y-4 sm:space-y-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Candidates</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCandidates}</div>
            <p className="text-xs text-muted-foreground">
              Total in database
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Pipeline</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activePipeline}</div>
            <p className="text-xs text-muted-foreground">
              Candidates in pipeline
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgResponseTime}</div>
            <p className="text-xs text-muted-foreground">
              Average response time
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Placements</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentMonthPlacements}</div>
            <p className="text-xs text-muted-foreground">
              {placementsChange > 0 ? '+' : ''}{placementsChange}% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Candidate Sources</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {(() => {
                // Calculate source distribution from candidates
                const sources = candidates.reduce((acc, candidate) => {
                  // Derive source from candidate tags or default to 'Direct Applications'
                  let source = 'Direct Applications';

                  if (candidate.tags && candidate.tags.length > 0) {
                    // Check for source-related tags
                    if (candidate.tags.includes('LinkedIn')) {
                      source = 'LinkedIn';
                    } else if (candidate.tags.includes('Referral')) {
                      source = 'Internal Referrals';
                    } else if (candidate.tags.includes('Job Board')) {
                      source = 'Job Boards';
                    } else if (candidate.tags.includes('Company Website')) {
                      source = 'Company Website';
                    } else if (candidate.tags.includes('Recruiter')) {
                      source = 'Recruiter';
                    }
                  }

                  acc[source] = (acc[source] || 0) + 1;
                  return acc;
                }, {} as Record<string, number>);

                const total = totalCandidates || 1;
                const sortedSources = Object.entries(sources)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 4); // Show top 4 sources

                return sortedSources.length > 0 ? sortedSources.map(([source, count]) => (
                  <div key={source} className="flex items-center justify-between">
                    <span>{source}</span>
                    <span className="font-medium">{Math.round((count / total) * 100)}%</span>
                  </div>
                )) : (
                  <div className="text-sm text-muted-foreground">No source data available</div>
                );
              })()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Skills in Demand</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {(() => {
                // Calculate skill distribution from candidates
                const skillCounts = candidates.reduce((acc, candidate) => {
                  if (candidate.skills && Array.isArray(candidate.skills)) {
                    candidate.skills.forEach(skill => {
                      if (typeof skill === 'string') {
                        acc[skill] = (acc[skill] || 0) + 1;
                      } else if (skill && typeof skill === 'object' && skill.name) {
                        acc[skill.name] = (acc[skill.name] || 0) + 1;
                      }
                    });
                  }
                  return acc;
                }, {} as Record<string, number>);

                const sortedSkills = Object.entries(skillCounts)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 4); // Show top 4 skills

                return sortedSkills.length > 0 ? sortedSkills.map(([skill, count]) => (
                  <div key={skill} className="flex items-center justify-between">
                    <span>{skill}</span>
                    <span className="font-medium">{count} candidates</span>
                  </div>
                )) : (
                  <div className="text-sm text-muted-foreground">No skill data available</div>
                );
              })()}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
