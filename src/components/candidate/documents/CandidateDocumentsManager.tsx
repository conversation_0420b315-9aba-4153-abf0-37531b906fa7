
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { DocumentUpload } from "./DocumentUpload";
import { DocumentList } from "./DocumentList";
import { LoadingState } from "./LoadingState";
import { EmptyState } from "./EmptyState";
import { useDocumentManagement } from "./useDocumentManagement";
import { useState } from "react";

interface CandidateDocumentsManagerProps {
  candidateId: string;
}

export function CandidateDocumentsManager({ candidateId }: CandidateDocumentsManagerProps) {
  const { documents, isLoading, deleteMutation, handleFileUpload } = useDocumentManagement(candidateId);
  const [isUploading, setIsUploading] = useState(false);

  const handleUpload = async (file: File) => {
    setIsUploading(true);
    try {
      await handleFileUpload(file);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDownload = async (fileUrl: string, fileName: string) => {
    try {
      const response = await fetch(fileUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const handleDelete = (documentId: string) => {
    deleteMutation.mutate(documentId);
  };

  if (isLoading) {
    return <LoadingState />;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Document Management</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="upload" className="space-y-4">
          <TabsList>
            <TabsTrigger value="upload">Upload Documents</TabsTrigger>
            <TabsTrigger value="manage">
              Manage Documents ({documents?.length || 0})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload">
            <DocumentUpload 
              candidateId={candidateId} 
              onUpload={handleUpload}
              isUploading={isUploading}
            />
          </TabsContent>

          <TabsContent value="manage">
            {!documents || documents.length === 0 ? (
              <EmptyState />
            ) : (
              <DocumentList
                documents={documents}
                onDownload={handleDownload}
                onDelete={handleDelete}
                isDeleting={deleteMutation.isPending}
              />
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
