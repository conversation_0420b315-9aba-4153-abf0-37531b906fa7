
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { useCreateTimelineEntry } from "@/hooks/useCreateTimelineEntry";
import { useDeleteDocument } from "@/hooks/useDeleteDocument";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";

export const useDocumentManagement = (candidateId: string) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const createTimelineEntry = useCreateTimelineEntry(); 
  const deleteDocumentMutation = useDeleteDocument(candidateId);

  // Validate UUID format
  const isValidUUID = (uuid: string) => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  };

  // Real-time documents subscription
  const fetchDocuments = async () => {
    if (!isValidUUID(candidateId) || !user) {
      console.error('Invalid UUID format for candidate ID or no authenticated user:', candidateId);
      return [];
    }

    const { data, error } = await supabase
      .from('candidate_documents')
      .select('*')
      .eq('candidate_id', candidateId)
      .order('uploaded_at', { ascending: false });

    if (error) throw error;
    return data || [];
  };
  
  const { records: documents = [], isLoading } = useRealtimeCollection(
    'candidate_documents',
    fetchDocuments,
    'public',
    `candidate_id=eq.${candidateId}`
  );

  // Delete document function
  const deleteMutation = {
    mutateAsync: async (documentId: string) => {
      return deleteDocumentMutation.mutateAsync(documentId);
    }
  };

  const handleFileUpload = async (file: File) => {
    try {
      if (!isValidUUID(candidateId)) {
        throw new Error('Invalid candidate ID format');
      }

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Sanitize filename - remove non-ASCII characters
      const sanitizedFileName = file.name.replace(/[^\x20-\x7F]/g, '_');
      const filePath = `${candidateId}/${sanitizedFileName}`;

      // Upload to storage
      const { error: uploadError } = await supabase.storage
        .from('candidate-documents')
        .upload(filePath, file, {
          upsert: true,
          contentType: file.type
        });

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('candidate-documents')
        .getPublicUrl(filePath);

      // Save to database
      const { error: dbError } = await supabase
        .from('candidate_documents')
        .insert({
          candidate_id: candidateId,
          name: sanitizedFileName,
          type: file.type,
          size: file.size,
          file_url: publicUrl,
          uploaded_by: user.id
        });

      if (dbError) throw dbError;

      // Real-time subscription will automatically update the UI
      
      // Create timeline entry for document upload
      createTimelineEntry.mutate({
        candidate_id: candidateId,
        event_type: 'document',
        title: `Document Uploaded: ${sanitizedFileName}`,
        description: `Document "${sanitizedFileName}" was uploaded`,
        status: 'completed',
      });
      
      toast({
        title: "Upload successful",
        description: "Document has been uploaded successfully."
      });
    } catch (error: unknown) {
      console.error('File upload error:', error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: "destructive"
      });
    }
  };

  return {
    documents,
    isLoading,
    deleteMutation,
    handleFileUpload
  };
};
