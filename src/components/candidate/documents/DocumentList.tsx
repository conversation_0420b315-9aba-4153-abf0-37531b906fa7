
import { ScrollArea } from "@/components/ui/scroll-area";
import { FileText, Download, Trash2, Eye, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";

interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  uploaded_at: string;
  file_url: string;
}

interface DocumentListProps {
  documents: Document[];
  onDownload: (fileUrl: string, fileName: string) => void;
  onDelete: (documentId: string) => void;
  isDeleting: boolean;
}

export function DocumentList({ documents, onDownload, onDelete, isDeleting }: DocumentListProps) {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getFileTypeLabel = (mimeType: string) => {
    if (mimeType.includes('pdf')) return 'PDF';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'Document';
    if (mimeType.includes('image')) return 'Image';
    if (mimeType.includes('text')) return 'Text';
    return 'File';
  };

  return (
    <ScrollArea className="h-[400px] pr-4">
      <div className="space-y-4">
        {documents?.map((doc) => (
          <div
            key={doc.id}
            className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50"
          >
            <div className="flex items-center space-x-4">
              <FileText className="w-8 h-8 text-blue-500" />
              <div>
                <p className="font-medium">{doc.name}</p>
                <p className="text-sm text-muted-foreground">
                  {getFileTypeLabel(doc.type)} • {formatFileSize(doc.size)} • Uploaded {formatDate(doc.uploaded_at)}
                </p>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => window.open(doc.file_url, '_blank')}
                title="View document"
              >
                <Eye className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onDownload(doc.file_url, doc.name)}
                title="Download document"
              >
                <Download className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onDelete(doc.id)}
                disabled={isDeleting}
                title="Delete document"
              >
                {isDeleting ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>
        ))}
      </div>
    </ScrollArea>
  );
}
