import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, Clock, Loader2, Video, MapPin } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { CandidateType } from "@/types/candidate";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface BulkInterviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  candidates: CandidateType[];
}

export function BulkInterviewDialog({ open, onOpenChange, candidates }: BulkInterviewDialogProps) {
  const [selectedCandidates, setSelectedCandidates] = useState<Set<string>>(new Set());
  const [interviewType, setInterviewType] = useState("phone");
  const [duration, setDuration] = useState("60");
  const [location, setLocation] = useState("");
  const [notes, setNotes] = useState("");
  const [scheduledDate, setScheduledDate] = useState("");
  const [scheduledTime, setScheduledTime] = useState("");
  const [isScheduling, setIsScheduling] = useState(false);
  
  const { toast } = useToast();
  const { user } = useAuth();

  const handleCandidateToggle = (candidateId: string, checked: boolean) => {
    const newSelected = new Set(selectedCandidates);
    if (checked) {
      newSelected.add(candidateId);
    } else {
      newSelected.delete(candidateId);
    }
    setSelectedCandidates(newSelected);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCandidates(new Set(candidates.map(c => c.id)));
    } else {
      setSelectedCandidates(new Set());
    }
  };

  const handleSchedule = async () => {
    if (selectedCandidates.size === 0) {
      toast({
        title: "No Candidates Selected",
        description: "Please select candidates to schedule interviews for.",
        variant: "destructive"
      });
      return;
    }

    if (!scheduledDate || !scheduledTime) {
      toast({
        title: "Missing Schedule Information",
        description: "Please provide both date and time for the interviews.",
        variant: "destructive"
      });
      return;
    }

    if (!user) {
      toast({
        title: "Authentication Error",
        description: "You must be logged in to schedule interviews.",
        variant: "destructive"
      });
      return;
    }

    setIsScheduling(true);
    let successCount = 0;
    let errorCount = 0;

    try {
      const selectedCandidatesList = candidates.filter(c => selectedCandidates.has(c.id));
      const scheduledDateTime = new Date(`${scheduledDate}T${scheduledTime}`);
      
      for (const candidate of selectedCandidatesList) {
        try {
          // Create interview record in database
          const { error } = await supabase
            .from('candidate_interviews')
            .insert({
              candidate_id: candidate.id,
              user_id: user.id,
              interview_type: interviewType,
              scheduled_date: scheduledDateTime.toISOString(),
              duration_minutes: parseInt(duration),
              location: location || (interviewType === 'video' ? 'Video Call' : 'Phone Call'),
              status: 'scheduled',
              notes: notes || `Bulk scheduled interview for ${candidate.name}`,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (error) {
            console.error(`Failed to schedule interview for ${candidate.name}:`, error);
            errorCount++;
          } else {
            successCount++;
          }
        } catch (error) {
          console.error(`Failed to schedule interview for ${candidate.name}:`, error);
          errorCount++;
        }
      }

      if (successCount > 0) {
        toast({
          title: "Interviews Scheduled",
          description: `Successfully scheduled ${successCount} interviews${errorCount > 0 ? ` (${errorCount} failed)` : ''}.`
        });
      }

      if (errorCount === selectedCandidatesList.length) {
        toast({
          title: "All Interviews Failed",
          description: "Failed to schedule any interviews. Please try again.",
          variant: "destructive"
        });
      }

      // Reset form on success
      if (successCount > 0) {
        setSelectedCandidates(new Set());
        setInterviewType("phone");
        setDuration("60");
        setLocation("");
        setNotes("");
        setScheduledDate("");
        setScheduledTime("");
        onOpenChange(false);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to schedule bulk interviews. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsScheduling(false);
    }
  };

  const selectedCount = selectedCandidates.size;
  const allSelected = selectedCount === candidates.length;

  // Generate time slots for the select dropdown
  const timeSlots = [];
  for (let hour = 8; hour <= 18; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      const displayTime = new Date(`2000-01-01T${timeString}`).toLocaleTimeString([], { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      });
      timeSlots.push({ value: timeString, label: displayTime });
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Bulk Schedule Interviews</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Candidate Selection */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">Select Candidates</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={allSelected}
                  onCheckedChange={handleSelectAll}
                />
                <Label htmlFor="select-all" className="text-sm">
                  Select All ({candidates.length})
                </Label>
              </div>
            </div>
            
            <ScrollArea className="h-48 border rounded-md p-4">
              <div className="space-y-2">
                {candidates.map((candidate) => (
                  <div key={candidate.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={candidate.id}
                      checked={selectedCandidates.has(candidate.id)}
                      onCheckedChange={(checked) => handleCandidateToggle(candidate.id, checked as boolean)}
                    />
                    <Label htmlFor={candidate.id} className="flex-1 cursor-pointer">
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="font-medium">{candidate.name}</span>
                          <span className="text-muted-foreground ml-2">({candidate.role})</span>
                        </div>
                        <span className="text-sm text-muted-foreground">{candidate.email}</span>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </ScrollArea>
            
            {selectedCount > 0 && (
              <Badge variant="secondary">
                {selectedCount} candidate{selectedCount > 1 ? 's' : ''} selected
              </Badge>
            )}
          </div>

          {/* Interview Details */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Interview Details</Label>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="interview-type">Interview Type</Label>
                <Select value={interviewType} onValueChange={setInterviewType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="phone">
                      <div className="flex items-center">
                        <Clock className="mr-2 h-4 w-4" />
                        Phone Interview
                      </div>
                    </SelectItem>
                    <SelectItem value="video">
                      <div className="flex items-center">
                        <Video className="mr-2 h-4 w-4" />
                        Video Interview
                      </div>
                    </SelectItem>
                    <SelectItem value="in-person">
                      <div className="flex items-center">
                        <MapPin className="mr-2 h-4 w-4" />
                        In-Person Interview
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="duration">Duration (minutes)</Label>
                <Select value={duration} onValueChange={setDuration}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 minutes</SelectItem>
                    <SelectItem value="45">45 minutes</SelectItem>
                    <SelectItem value="60">60 minutes</SelectItem>
                    <SelectItem value="90">90 minutes</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="scheduled-date">Date</Label>
                <Input
                  id="scheduled-date"
                  type="date"
                  value={scheduledDate}
                  onChange={(e) => setScheduledDate(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
              
              <div>
                <Label htmlFor="scheduled-time">Time</Label>
                <Select value={scheduledTime} onValueChange={setScheduledTime}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select time" />
                  </SelectTrigger>
                  <SelectContent>
                    {timeSlots.map((slot) => (
                      <SelectItem key={slot.value} value={slot.value}>
                        {slot.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            {(interviewType === 'video' || interviewType === 'in-person') && (
              <div>
                <Label htmlFor="location">
                  {interviewType === 'video' ? 'Meeting Link/Platform' : 'Location'}
                </Label>
                <Input
                  id="location"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  placeholder={
                    interviewType === 'video' 
                      ? "e.g., Zoom, Google Meet, or meeting link" 
                      : "e.g., Office address or meeting room"
                  }
                />
              </div>
            )}
            
            <div>
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Additional notes for the interviews..."
                rows={3}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleSchedule} 
              disabled={isScheduling || selectedCount === 0 || !scheduledDate || !scheduledTime}
              className="flex-1"
            >
              {isScheduling ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Scheduling {selectedCount} interviews...
                </>
              ) : (
                <>
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule {selectedCount} interview{selectedCount > 1 ? 's' : ''}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
