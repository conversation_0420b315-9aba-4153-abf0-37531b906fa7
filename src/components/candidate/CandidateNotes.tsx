
import { useState } from "react";
import { CandidateType } from "@/types/candidate";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Star, StarOff, Wand2, Loader2 } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { generateText } from "@/utils/gemini";

interface CandidateNotesProps {
  candidate: CandidateType;
}

interface Note {
  id: string;
  content: string;
  author_name: string;
  created_at: string;
  is_important: boolean;
}

export function CandidateNotes({ candidate }: CandidateNotesProps) {
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [noteContent, setNoteContent] = useState("");
  const [isGeneratingNote, setIsGeneratingNote] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  const fetchNotes = async () => {
      if (!user) return [];
      
      const { data, error } = await supabase
        .from('candidate_notes')
        .select('*')
        .eq('candidate_id', candidate.id)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
  };
  
  const { records: notes = [], isLoading } = useRealtimeCollection(
    'candidate_notes',
    fetchNotes,
    'public',
    `candidate_id=eq.${candidate.id}`
  );

  const addNote = async (content: string) => {
    try {
      if (!user) throw new Error('Not authenticated');
      
      const { error } = await supabase
        .from('candidate_notes')
        .insert({
          candidate_id: candidate.id,
          user_id: user.id,
          content,
          author_name: user.email?.split('@')[0] || 'Unknown'
        });

      if (error) throw error;
      
      // Real-time subscription will automatically update the UI
      setIsAddingNote(false);
      setNoteContent("");
      toast({
        title: "Note added",
        description: "Your note has been saved successfully."
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save note. Please try again.",
        variant: "destructive"
      });
    }
  };

  const toggleImportant = async (noteId: string, isImportant: boolean) => {
    try {
      const { error } = await supabase
        .from('candidate_notes')
        .update({ is_important: !isImportant })
        .eq('id', noteId);

      if (error) throw error;
      // Real-time subscription will automatically update the UI
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update note. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleSaveNote = () => {
    if (noteContent.trim()) {
      addNote(noteContent.trim());
    }
  };

  const handleGenerateNote = async () => {
    setIsGeneratingNote(true);
    try {
      // Create a prompt for Gemini
      const prompt = `
Generate a professional note about a candidate named ${candidate.name} who is a ${candidate.role}.

CANDIDATE INFORMATION:
- Experience: ${candidate.experience || 'Not specified'}
- Skills: ${candidate.skills.map(s => typeof s === 'string' ? s : s.name).join(', ')}
- Location: ${candidate.location || 'Not specified'}
- Industry: ${candidate.industry || 'Not specified'}

The note should be:
1. Professional and concise (2-3 sentences)
2. Highlight key strengths or observations
3. Include a specific follow-up action or recommendation

Write the note in first person as if you are a recruiter who just reviewed this candidate.
`;

      const systemPrompt = "You are an expert recruiter writing professional notes about candidates. Your notes are concise, insightful, and action-oriented.";
      
      // Call Gemini API
      const response = await generateText(prompt, systemPrompt);
      
      // Set the note content
      setNoteContent(response);
      setIsAddingNote(true);
      
    } catch (error) {
      console.error('Error generating note:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate note. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingNote(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="animate-pulse space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-20 bg-muted rounded" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Notes</CardTitle>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={handleGenerateNote}
              disabled={isGeneratingNote}
            >
              {isGeneratingNote ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Wand2 className="w-4 h-4 mr-2" />
                  Generate Note
                </>
              )}
            </Button>
            <Button onClick={() => setIsAddingNote(!isAddingNote)}>
              <Plus className="w-4 h-4 mr-2" />
              Add Note
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px] pr-4">
            <div className="space-y-4">
              {isAddingNote && (
                <div className="space-y-4 p-4 border rounded-lg">
                  <Textarea
                    placeholder="Enter your note here..."
                    className="min-h-[100px]"
                    value={noteContent}
                    onChange={(e) => setNoteContent(e.target.value)}
                  />
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsAddingNote(false);
                        setNoteContent("");
                      }}
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleSaveNote}
                      disabled={!noteContent.trim()}
                    >
                      Save Note
                    </Button>
                  </div>
                </div>
              )}
              
              {notes.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <div className="text-4xl mb-4">📝</div>
                  <p className="text-muted-foreground">
                    No notes yet. Add your first note to keep track of important information about this candidate.
                  </p>
                </div>
              ) : (
                notes.map((note) => (
                  <div
                    key={note.id}
                    className="p-4 border rounded-lg hover:bg-accent/50"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="text-sm mb-2">{note.content}</p>
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <span>{note.author_name}</span>
                          <span>•</span>
                          <span>{new Date(note.created_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={() => toggleImportant(note.id, note.is_important)}
                      >
                        {note.is_important ? (
                          <Star className="w-4 h-4 text-yellow-500" />
                        ) : (
                          <StarOff className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
