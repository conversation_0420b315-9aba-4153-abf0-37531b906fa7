import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CandidateOverview } from "./CandidateOverview";
import { CandidateScreening } from "./CandidateScreening";
import { CandidateActivity } from "./CandidateActivity";
import { CandidateDocuments } from "./CandidateDocuments";
import { CandidateInterviews } from "./CandidateInterviews";
import { CandidateNotes } from "./CandidateNotes";
import { CandidateTimeline } from "./CandidateTimeline";
import { CandidateComparison } from "./CandidateComparison";
import { CandidateJobs } from "./CandidateJobs";
import { CandidateType } from "@/types/candidate";

interface CandidateProfileProps {
  candidate: CandidateType;
}

export function CandidateProfile({ candidate }: CandidateProfileProps) {
  return (
    <Card className="border-none shadow-none">
      <CardHeader>
        <CardTitle>Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="profile" className="space-y-4">
          <TabsList className="grid grid-cols-9 w-full">
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="screening">Screening</TabsTrigger>
            <TabsTrigger value="jobs">Jobs</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="interviews">Interviews</TabsTrigger>
            <TabsTrigger value="notes">Notes</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
            <TabsTrigger value="compare">Compare</TabsTrigger>
          </TabsList>
          
          <TabsContent value="profile">
            <CandidateOverview candidate={candidate} />
          </TabsContent>

          <TabsContent value="screening">
            <CandidateScreening candidate={candidate} />
          </TabsContent>

          <TabsContent value="jobs">
            <CandidateJobs candidate={candidate} />
          </TabsContent>

          <TabsContent value="activity">
            <CandidateActivity candidate={candidate} />
          </TabsContent>

          <TabsContent value="documents">
            <CandidateDocuments candidate={candidate} />
          </TabsContent>

          <TabsContent value="interviews">
            <CandidateInterviews candidate={candidate} />
          </TabsContent>

          <TabsContent value="notes">
            <CandidateNotes candidate={candidate} />
          </TabsContent>

          <TabsContent value="timeline">
            <CandidateTimeline candidate={candidate} />
          </TabsContent>

          <TabsContent value="compare">
            <CandidateComparison mainCandidate={candidate} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}