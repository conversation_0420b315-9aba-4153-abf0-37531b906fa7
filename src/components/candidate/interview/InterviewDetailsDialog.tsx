
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { Calendar, Clock, Users, Video, MapPin, Edit, Save, X } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useUpdateInterview } from "@/hooks/useUpdateInterview";
import { InterviewFeedbackAnalysis } from "./InterviewFeedbackAnalysis";

interface Interview {
  id: string;
  interview_type: string;
  scheduled_date: string;
  duration_minutes: number;
  status: string;
  interviewers: string[];
  location: string;
  meeting_platform: string;
  meeting_link: string;
  feedback: string;
}

interface InterviewDetailsDialogProps {
  interview: Interview | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function InterviewDetailsDialog({ interview, open, onOpenChange }: InterviewDetailsDialogProps) {
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    interview_type: "",
    scheduled_date: "",
    scheduled_time: "",
    duration_minutes: 60,
    status: "",
    interviewers: "",
    location: "",
    meeting_platform: "",
    meeting_link: "",
    feedback: ""
  });
  const [showFeedbackAnalysis, setShowFeedbackAnalysis] = useState(false);
  const [feedbackAnalysis, setFeedbackAnalysis] = useState<any>(null);

  const updateInterview = useUpdateInterview();

  useEffect(() => {
    if (interview) {
      const date = new Date(interview.scheduled_date);
      const dateStr = date.toISOString().split('T')[0];
      const timeStr = date.toTimeString().slice(0, 5);
      
      setFormData({
        interview_type: interview.interview_type || "",
        scheduled_date: dateStr,
        scheduled_time: timeStr,
        duration_minutes: interview.duration_minutes || 60,
        status: interview.status || "scheduled",
        interviewers: interview.interviewers?.join(", ") || "",
        location: interview.location || "",
        meeting_platform: interview.meeting_platform || "",
        meeting_link: interview.meeting_link || "",
        feedback: interview.feedback || ""
      });
    }
  }, [interview]);

  const handleSave = async () => {
    if (!interview) return;

    const scheduledDateTime = `${formData.scheduled_date}T${formData.scheduled_time}:00`;
    const interviewerArray = formData.interviewers.split(',').reduce((acc, name) => {
      const trimmed = name.trim();
      if (trimmed) acc.push(trimmed);
      return acc;
    }, [] as string[]);

    await updateInterview.mutateAsync({
      id: interview.id,
      interview_type: formData.interview_type,
      scheduled_date: scheduledDateTime,
      duration_minutes: formData.duration_minutes,
      status: formData.status,
      interviewers: interviewerArray,
      location: formData.location || null,
      meeting_platform: formData.meeting_platform || null,
      meeting_link: formData.meeting_link || null,
      feedback: formData.feedback || null
    });

    setIsEditing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-500";
      case "completed":
        return "bg-green-500";
      case "cancelled":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  if (!interview) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle>Interview Details</DialogTitle>
              <DialogDescription>View and edit interview information</DialogDescription>
            </div>
            <div className="flex gap-2">
              {!isEditing ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(true)}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
              ) : (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(false)}
                  >
                    <X className="w-4 h-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSave}
                    disabled={updateInterview.isPending}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {updateInterview.isPending ? "Saving..." : "Save"}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Interview Type and Status */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Interview Type</label>
              {isEditing ? (
                <Select
                  value={formData.interview_type}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, interview_type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Initial Interview">Initial Interview</SelectItem>
                    <SelectItem value="Technical Interview">Technical Interview</SelectItem>
                    <SelectItem value="Cultural Fit">Cultural Fit</SelectItem>
                    <SelectItem value="Final Interview">Final Interview</SelectItem>
                    <SelectItem value="Panel Interview">Panel Interview</SelectItem>
                    <SelectItem value="Phone Screen">Phone Screen</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <div className="p-2 border rounded-md bg-muted/50">
                  {formData.interview_type}
                </div>
              )}
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              {isEditing ? (
                <Select
                  value={formData.status}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="rescheduled">Rescheduled</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <Badge className={`${getStatusColor(formData.status)} text-white`}>
                  {formData.status}
                </Badge>
              )}
            </div>
          </div>

          {/* Date, Time, Duration */}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Date
              </label>
              {isEditing ? (
                <Input
                  type="date"
                  value={formData.scheduled_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, scheduled_date: e.target.value }))}
                />
              ) : (
                <div className="p-2 border rounded-md bg-muted/50">
                  {new Date(interview.scheduled_date).toLocaleDateString()}
                </div>
              )}
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Time
              </label>
              {isEditing ? (
                <Input
                  type="time"
                  value={formData.scheduled_time}
                  onChange={(e) => setFormData(prev => ({ ...prev, scheduled_time: e.target.value }))}
                />
              ) : (
                <div className="p-2 border rounded-md bg-muted/50">
                  {new Date(interview.scheduled_date).toLocaleTimeString()}
                </div>
              )}
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Duration</label>
              {isEditing ? (
                <Select
                  value={formData.duration_minutes.toString()}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, duration_minutes: parseInt(value) }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 min</SelectItem>
                    <SelectItem value="45">45 min</SelectItem>
                    <SelectItem value="60">60 min</SelectItem>
                    <SelectItem value="90">90 min</SelectItem>
                    <SelectItem value="120">120 min</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <div className="p-2 border rounded-md bg-muted/50">
                  {formData.duration_minutes} minutes
                </div>
              )}
            </div>
          </div>

          {/* Interviewers */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <Users className="w-4 h-4" />
              Interviewers
            </label>
            {isEditing ? (
              <Input
                placeholder="Enter interviewer names separated by commas"
                value={formData.interviewers}
                onChange={(e) => setFormData(prev => ({ ...prev, interviewers: e.target.value }))}
              />
            ) : (
              <div className="p-2 border rounded-md bg-muted/50">
                {formData.interviewers || "Not assigned"}
              </div>
            )}
          </div>

          {/* Platform and Location */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-2">
                <Video className="w-4 h-4" />
                Meeting Platform
              </label>
              {isEditing ? (
                <Select
                  value={formData.meeting_platform}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, meeting_platform: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select platform" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Zoom">Zoom</SelectItem>
                    <SelectItem value="Google Meet">Google Meet</SelectItem>
                    <SelectItem value="Microsoft Teams">Microsoft Teams</SelectItem>
                    <SelectItem value="In Person">In Person</SelectItem>
                    <SelectItem value="Phone">Phone</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <div className="p-2 border rounded-md bg-muted/50">
                  {formData.meeting_platform || "Not specified"}
                </div>
              )}
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                Location
              </label>
              {isEditing ? (
                <Input
                  placeholder="Meeting room, address, etc."
                  value={formData.location}
                  onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                />
              ) : (
                <div className="p-2 border rounded-md bg-muted/50">
                  {formData.location || "Not specified"}
                </div>
              )}
            </div>
          </div>

          {/* Meeting Link */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Meeting Link</label>
            {isEditing ? (
              <Input
                placeholder="https://zoom.us/j/123456789"
                value={formData.meeting_link}
                onChange={(e) => setFormData(prev => ({ ...prev, meeting_link: e.target.value }))}
              />
            ) : (
              <div className="p-2 border rounded-md bg-muted/50">
                {formData.meeting_link ? (
                  <a 
                    href={formData.meeting_link} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    {formData.meeting_link}
                  </a>
                ) : (
                  "No meeting link provided"
                )}
              </div>
            )}
          </div>

          {/* Feedback */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Feedback</label>
            {isEditing ? (
              <div className="space-y-2">
                <Textarea
                  placeholder="Interview feedback and notes..."
                  value={formData.feedback}
                  onChange={(e) => setFormData(prev => ({ ...prev, feedback: e.target.value }))}
                  rows={4}
                />
                {formData.feedback && (
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={() => setShowFeedbackAnalysis(true)}
                  >
                    <span className="h-4 w-4 mr-2">🧠</span>
                    Analyze Feedback
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <div className="p-3 border rounded-md bg-muted/50 min-h-[100px]">
                  {formData.feedback || "No feedback provided yet"}
                </div>
                {formData.feedback && (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setShowFeedbackAnalysis(true)}
                  >
                    <span className="h-4 w-4 mr-2">🧠</span>
                    Analyze Feedback
                  </Button>
                )}
                {feedbackAnalysis && (
                  <div className="p-3 border rounded-md bg-blue-50">
                    <h4 className="font-medium mb-2">AI Analysis Summary</h4>
                    <p className="text-sm">{feedbackAnalysis.summary}</p>
                    {feedbackAnalysis.recommendation && (
                      <div className="mt-2">
                        <Badge className={
                          feedbackAnalysis.recommendation.toLowerCase().includes('hire') 
                            ? "bg-green-500" 
                            : feedbackAnalysis.recommendation.toLowerCase().includes('reject')
                              ? "bg-red-500"
                              : "bg-yellow-500"
                        }>
                          {feedbackAnalysis.recommendation.split(' ')[0]}
                        </Badge>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        
        {showFeedbackAnalysis && (
          <Dialog open={showFeedbackAnalysis} onOpenChange={setShowFeedbackAnalysis}>
            <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Feedback Analysis</DialogTitle>
              </DialogHeader>
              <InterviewFeedbackAnalysis 
                initialFeedback={formData.feedback} 
                onAnalysisComplete={(analysis) => {
                  setFeedbackAnalysis(analysis);
                  setShowFeedbackAnalysis(false);
                }}
              />
            </DialogContent>
          </Dialog>
        )}
      </DialogContent>
    </Dialog>
  );
}
