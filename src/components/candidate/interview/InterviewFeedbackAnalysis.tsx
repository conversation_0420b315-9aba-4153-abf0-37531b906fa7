import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, <PERSON>, <PERSON>humbsUp, <PERSON>hum<PERSON>Down, Al<PERSON><PERSON>riangle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { analyzeInterviewFeedback } from "@/utils/gemini";
import { useToast } from "@/hooks/use-toast";

interface InterviewFeedbackAnalysisProps {
  initialFeedback?: string;
  onAnalysisComplete?: (analysis: any) => void;
}

export function InterviewFeedbackAnalysis({ initialFeedback = "", onAnalysisComplete }: InterviewFeedbackAnalysisProps) {
  const [feedback, setFeedback] = useState(initialFeedback);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState<any>(null);
  const { toast } = useToast();

  const handleAnalyze = async () => {
    if (!feedback.trim()) {
      toast({
        title: "Empty Feedback",
        description: "Please enter interview feedback to analyze.",
        variant: "destructive"
      });
      return;
    }

    setIsAnalyzing(true);
    try {
      const result = await analyzeInterviewFeedback(feedback);
      setAnalysis(result);
      
      if (onAnalysisComplete) {
        onAnalysisComplete(result);
      }
      
      toast({
        title: "Analysis Complete",
        description: "Interview feedback has been analyzed successfully."
      });
    } catch (error) {
      console.error("Error analyzing feedback:", error);
      toast({
        title: "Analysis Failed",
        description: "Failed to analyze the interview feedback. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getSentimentIcon = (sentiment: string) => {
    if (!sentiment) return null;
    
    if (sentiment.includes('positive')) {
      return <ThumbsUp className="h-5 w-5 text-green-500" />;
    } else if (sentiment.includes('negative')) {
      return <ThumbsDown className="h-5 w-5 text-red-500" />;
    } else {
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getSentimentScore = (sentiment: string) => {
    if (!sentiment) return null;
    
    // Extract score from sentiment string (e.g., "positive with a score from 85")
    const match = sentiment.match(/(\d+)/);
    if (match && match[1]) {
      return parseInt(match[1], 10);
    }
    return null;
  };

  const getRecommendationBadge = (recommendation: string) => {
    if (!recommendation) return null;
    
    if (recommendation.toLowerCase().includes('hire')) {
      return <Badge className="bg-green-500">Recommend Hire</Badge>;
    } else if (recommendation.toLowerCase().includes('reject')) {
      return <Badge className="bg-red-500">Do Not Hire</Badge>;
    } else {
      return <Badge className="bg-yellow-500">Consider Further</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          Interview Feedback Analysis
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Textarea
            placeholder="Enter interview feedback to analyze..."
            value={feedback}
            onChange={(e) => setFeedback(e.target.value)}
            className="min-h-[150px]"
            disabled={isAnalyzing}
          />
          <Button 
            onClick={handleAnalyze} 
            disabled={isAnalyzing || !feedback.trim()}
            className="w-full"
          >
            {isAnalyzing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Brain className="mr-2 h-4 w-4" />
                Analyze Feedback
              </>
            )}
          </Button>
        </div>

        {analysis && (
          <div className="space-y-4 border rounded-lg p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-lg">Analysis Results</h3>
              <div className="flex items-center gap-2">
                {getSentimentIcon(analysis.sentiment)}
                {getRecommendationBadge(analysis.recommendation)}
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-1">Summary</h4>
              <p className="text-sm text-muted-foreground">{analysis.summary}</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Strengths</h4>
                <ul className="list-disc list-inside space-y-1">
                  {analysis.strengths.map((strength: string, index: number) => (
                    <li key={index} className="text-sm text-muted-foreground">{strength}</li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Areas for Improvement</h4>
                <ul className="list-disc list-inside space-y-1">
                  {analysis.weaknesses.map((weakness: string, index: number) => (
                    <li key={index} className="text-sm text-muted-foreground">{weakness}</li>
                  ))}
                </ul>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-1">Recommendation</h4>
              <p className="text-sm text-muted-foreground">{analysis.recommendation}</p>
            </div>
            
            {getSentimentScore(analysis.sentiment) !== null && (
              <div>
                <h4 className="font-medium mb-1">Sentiment Score</h4>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div 
                    className="bg-blue-600 h-2.5 rounded-full" 
                    style={{ width: `${getSentimentScore(analysis.sentiment)}%` }}
                  ></div>
                </div>
                <p className="text-xs text-right mt-1 text-muted-foreground">
                  {getSentimentScore(analysis.sentiment)}/100
                </p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}