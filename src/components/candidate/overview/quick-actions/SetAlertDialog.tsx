import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { AlertTriangle } from "lucide-react";

export function SetAlertDialog() {
  const { toast } = useToast();
  const [open, setOpen] = useState(false);

  const handleSetAlert = () => {
    toast({
      title: "Alert Set",
      description: "You will be notified according to your settings.",
    });
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <Button 
        variant="outline" 
        size="sm"
        className="hover:bg-gradient-to-r from-[#8E9196] to-[#403E43] hover:text-white transition-all duration-300"
        onClick={() => setOpen(true)}
      >
        <AlertTriangle className="w-4 h-4 mr-2" />
        Set Alert
      </Button>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Set Alert</DialogTitle>
          <DialogDescription>Create an alert for this candidate</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Alert Type</label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select alert type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="follow_up">Follow-up Required</SelectItem>
                <SelectItem value="document">Document Expiring</SelectItem>
                <SelectItem value="deadline">Application Deadline</SelectItem>
                <SelectItem value="custom">Custom Alert</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Due Date</label>
            <Input type="date" />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Reminder</label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select reminder time" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1day">1 day before</SelectItem>
                <SelectItem value="3days">3 days before</SelectItem>
                <SelectItem value="1week">1 week before</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Notes</label>
            <Textarea placeholder="Add any additional notes..." />
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSetAlert}>Set Alert</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}