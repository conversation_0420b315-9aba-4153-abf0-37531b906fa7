
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { MessageSquarePlus } from "lucide-react";
import { useCreateNote } from "@/hooks/useCreateNote";

interface AddNoteDialogProps {
  candidateId: string;
}

export function AddNoteDialog({ candidateId }: AddNoteDialogProps) {
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [noteText, setNoteText] = useState("");
  const [isImportant, setIsImportant] = useState(false);
  const createNote = useCreateNote();

  const handleAddNote = async () => {
    if (noteText.trim()) {
      await createNote.mutateAsync({
        candidate_id: candidateId,
        content: noteText.trim(),
        is_important: isImportant,
      });
      
      setOpen(false);
      setNoteText("");
      setIsImportant(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <Button 
        variant="outline" 
        size="sm" 
        className="hover:bg-gradient-to-r from-[#6E59A5] to-[#1A1F2C] hover:text-white transition-all duration-300"
        onClick={() => setOpen(true)}
      >
        <MessageSquarePlus className="w-4 h-4 mr-2" />
        Add Note
      </Button>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Note</DialogTitle>
          <DialogDescription>Add a note about this candidate</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <Textarea
            value={noteText}
            onChange={(e) => setNoteText(e.target.value)}
            placeholder="Enter your note here..."
            className="min-h-[100px]"
          />
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="important"
              checked={isImportant}
              onCheckedChange={(checked) => setIsImportant(checked === true)}
            />
            <label htmlFor="important" className="text-sm font-medium">
              Mark as important
            </label>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleAddNote}
              disabled={createNote.isPending || !noteText.trim()}
            >
              {createNote.isPending ? "Saving..." : "Save Note"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
