
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Calendar as CalendarIcon, Plus } from "lucide-react";
import { useCreateInterview } from "@/hooks/useCreateInterview";
import { Textarea } from "@/components/ui/textarea";
import { splitTrimFilter } from "@/utils/arrayOptimizations";

interface ScheduleMeetingDialogProps {
  candidateId: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function ScheduleMeetingDialog({ candidateId, open: controlledOpen, onOpenChange }: ScheduleMeetingDialogProps) {
  const { toast } = useToast();
  const [internalOpen, setInternalOpen] = useState(false);
  const [meetingDate, setMeetingDate] = useState("");
  const [meetingTime, setMeetingTime] = useState("");
  const [meetingType, setMeetingType] = useState("");
  const [duration, setDuration] = useState("60");
  const [location, setLocation] = useState("");
  const [meetingPlatform, setMeetingPlatform] = useState("");
  const [meetingLink, setMeetingLink] = useState("");
  const [interviewers, setInterviewers] = useState("");
  const createInterview = useCreateInterview();

  // Use controlled or uncontrolled state
  const isOpen = controlledOpen !== undefined ? controlledOpen : internalOpen;
  const setOpen = onOpenChange || setInternalOpen;

  const resetForm = () => {
    setMeetingDate("");
    setMeetingTime("");
    setMeetingType("");
    setDuration("60");
    setLocation("");
    setMeetingPlatform("");
    setMeetingLink("");
    setInterviewers("");
  };

  const handleScheduleMeeting = async () => {
    if (meetingDate && meetingTime && meetingType) {
      const scheduledDate = `${meetingDate}T${meetingTime}:00`;
      
      // Parse interviewers from comma-separated string to array (optimized)
      const interviewerArray = splitTrimFilter(interviewers, ',');
      
      await createInterview.mutateAsync({
        candidate_id: candidateId,
        interview_type: meetingType,
        scheduled_date: scheduledDate,
        duration_minutes: parseInt(duration),
        location: location || undefined,
        meeting_platform: meetingPlatform || undefined,
        meeting_link: meetingLink || undefined,
        interviewers: interviewerArray.length > 0 ? interviewerArray : undefined,
      });
      
      setOpen(false);
      resetForm();
    }
  };

  // If being used as a standalone button (for QuickActions)
  if (controlledOpen === undefined) {
    return (
      <Dialog open={isOpen} onOpenChange={setOpen}>
        <Button 
          variant="outline" 
          size="sm"
          className="hover:bg-gradient-to-r from-[#0EA5E9] to-[#221F26] hover:text-white transition-all duration-300"
          onClick={() => setOpen(true)}
        >
          <CalendarIcon className="w-4 h-4 mr-2" />
          Schedule Meeting
        </Button>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Schedule Meeting</DialogTitle>
            <DialogDescription>Schedule a meeting with the candidate</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Meeting Type</label>
                <Select onValueChange={setMeetingType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select meeting type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Initial Interview">Initial Interview</SelectItem>
                    <SelectItem value="Technical Interview">Technical Interview</SelectItem>
                    <SelectItem value="Cultural Fit">Cultural Fit</SelectItem>
                    <SelectItem value="Final Interview">Final Interview</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Duration (minutes)</label>
                <Select onValueChange={setDuration} defaultValue="60">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 minutes</SelectItem>
                    <SelectItem value="45">45 minutes</SelectItem>
                    <SelectItem value="60">60 minutes</SelectItem>
                    <SelectItem value="90">90 minutes</SelectItem>
                    <SelectItem value="120">120 minutes</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Date</label>
                <Input
                  type="date"
                  value={meetingDate}
                  onChange={(e) => setMeetingDate(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Time</label>
                <Input
                  type="time"
                  value={meetingTime}
                  onChange={(e) => setMeetingTime(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Interviewers</label>
              <Input
                placeholder="Enter interviewer names separated by commas"
                value={interviewers}
                onChange={(e) => setInterviewers(e.target.value)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Meeting Platform</label>
                <Select onValueChange={setMeetingPlatform}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select platform (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Zoom">Zoom</SelectItem>
                    <SelectItem value="Google Meet">Google Meet</SelectItem>
                    <SelectItem value="Microsoft Teams">Microsoft Teams</SelectItem>
                    <SelectItem value="In Person">In Person</SelectItem>
                    <SelectItem value="Phone">Phone</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Location</label>
                <Input
                  placeholder="Meeting room, address, etc."
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Meeting Link (Optional)</label>
              <Input
                placeholder="https://zoom.us/j/123456789"
                value={meetingLink}
                onChange={(e) => setMeetingLink(e.target.value)}
              />
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleScheduleMeeting}
                disabled={createInterview.isPending}
              >
                {createInterview.isPending ? "Scheduling..." : "Schedule"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // If being used as a controlled dialog (for CandidateInterviews)
  return (
    <Dialog open={isOpen} onOpenChange={setOpen}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Schedule Interview</DialogTitle>
          <DialogDescription>Schedule an interview with the candidate</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Interview Type</label>
              <Select onValueChange={setMeetingType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select interview type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Initial Interview">Initial Interview</SelectItem>
                  <SelectItem value="Technical Interview">Technical Interview</SelectItem>
                  <SelectItem value="Cultural Fit">Cultural Fit</SelectItem>
                  <SelectItem value="Final Interview">Final Interview</SelectItem>
                  <SelectItem value="Panel Interview">Panel Interview</SelectItem>
                  <SelectItem value="Phone Screen">Phone Screen</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Duration (minutes)</label>
              <Select onValueChange={setDuration} defaultValue="60">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="30">30 minutes</SelectItem>
                  <SelectItem value="45">45 minutes</SelectItem>
                  <SelectItem value="60">60 minutes</SelectItem>
                  <SelectItem value="90">90 minutes</SelectItem>
                  <SelectItem value="120">120 minutes</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Date</label>
              <Input
                type="date"
                value={meetingDate}
                onChange={(e) => setMeetingDate(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Time</label>
              <Input
                type="time"
                value={meetingTime}
                onChange={(e) => setMeetingTime(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Interviewers</label>
            <Input
              placeholder="Enter interviewer names separated by commas"
              value={interviewers}
              onChange={(e) => setInterviewers(e.target.value)}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Meeting Platform</label>
              <Select onValueChange={setMeetingPlatform}>
                <SelectTrigger>
                  <SelectValue placeholder="Select platform (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Zoom">Zoom</SelectItem>
                  <SelectItem value="Google Meet">Google Meet</SelectItem>
                  <SelectItem value="Microsoft Teams">Microsoft Teams</SelectItem>
                  <SelectItem value="In Person">In Person</SelectItem>
                  <SelectItem value="Phone">Phone</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Location</label>
              <Input
                placeholder="Meeting room, address, etc."
                value={location}
                onChange={(e) => setLocation(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Meeting Link (Optional)</label>
            <Input
              placeholder="https://zoom.us/j/123456789"
              value={meetingLink}
              onChange={(e) => setMeetingLink(e.target.value)}
            />
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleScheduleMeeting}
              disabled={createInterview.isPending || !meetingDate || !meetingTime || !meetingType}
            >
              {createInterview.isPending ? "Scheduling..." : "Schedule Interview"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
