
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card } from "@/components/ui/card";
import {
  TrendingUp,
  TrendingDown,
  Minus,
  Users,
  Calendar,
  Mail,
  Phone,
  Star,
  Award,
  Loader2,
} from "lucide-react";
import { CandidateType } from "@/types/candidate";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { useState, useEffect } from "react";
import { generateText } from "@/utils/gemini";

interface RelationshipScoreDialogProps {
  candidate: CandidateType;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const defaultTimelineData = [
  { month: "Jan", score: 65 },
  { month: "Feb", score: 70 },
  { month: "Mar", score: 68 },
  { month: "Apr", score: 75 },
  { month: "May", score: 82 },
  { month: "Jun", score: 85 },
];

const defaultMetrics = [
  {
    name: "Communication Frequency",
    score: 85,
    icon: Mail,
    description: "Regular email and message exchanges",
  },
  {
    name: "Meeting Engagement",
    score: 92,
    icon: Users,
    description: "Active participation in interviews and discussions",
  },
  {
    name: "Response Time",
    score: 78,
    icon: Phone,
    description: "Average response time to communications",
  },
  {
    name: "Professional Growth",
    score: 88,
    icon: Award,
    description: "Career development and skill advancement",
  },
  {
    name: "Cultural Alignment",
    score: 90,
    icon: Star,
    description: "Alignment with company values and culture",
  },
];

export function RelationshipScoreDialog({
  candidate,
  open,
  onOpenChange,
}: RelationshipScoreDialogProps) {
  const [timelineData, setTimelineData] = useState(defaultTimelineData);
  const [metrics, setMetrics] = useState(defaultMetrics);
  const [isLoading, setIsLoading] = useState(false);
  const [analysis, setAnalysis] = useState<string | null>(null);
  
  useEffect(() => {
    if (open) {
      generateRelationshipAnalysis();
    }
  }, [open, candidate.id]);
  
  const generateRelationshipAnalysis = async () => {
    setIsLoading(true);
    try {
      // Create a prompt for Gemini
      const prompt = `
Analyze this candidate's profile and generate a relationship analysis:

CANDIDATE PROFILE:
Name: ${candidate.name}
Role: ${candidate.role}
Experience: ${candidate.experience}
Industry: ${candidate.industry || 'Not specified'}
Skills: ${candidate.skills.map(s => typeof s === 'string' ? s : s.name).join(', ')}
Current Relationship Score: ${candidate.relationshipScore}%

Provide a detailed analysis of this candidate's relationship with the company, including:
1. Strengths in the relationship
2. Areas for improvement
3. Recommended next steps to strengthen the relationship

Format your response as a concise, professional analysis that would be helpful for a recruiter.
`;

      const systemPrompt = "You are an expert recruitment relationship analyst. Provide insightful, actionable analysis of candidate relationships to help recruiters build stronger connections.";
      
      // Call Gemini API
      const response = await generateText(prompt, systemPrompt);
      setAnalysis(response);
    } catch (error) {
      console.error('Error generating relationship analysis:', error);
      setAnalysis(null);
    } finally {
      setIsLoading(false);
    }
  };

  const getTrendIcon = (trend?: string) => {
    if (trend === "increasing")
      return <TrendingUp className="w-4 h-4 text-green-500" />;
    if (trend === "decreasing")
      return <TrendingDown className="w-4 h-4 text-red-500" />;
    return <Minus className="w-4 h-4 text-gray-500" />;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Relationship Analysis</DialogTitle>
          <DialogDescription>
            Comprehensive breakdown of candidate relationship metrics and trends
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-8 mt-4">
          {/* Score Timeline */}
          <div className="bg-card rounded-lg p-4">
            <h4 className="text-sm font-semibold mb-4">Score Timeline</h4>
            <div className="h-[200px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={timelineData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="score"
                    stroke="#2563eb"
                    strokeWidth={2}
                    dot={{ fill: "#2563eb" }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Detailed Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {metrics.map((metric) => {
              const Icon = metric.icon;
              return (
                <div
                  key={metric.name}
                  className="bg-card p-4 rounded-lg space-y-3 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center gap-2">
                    <Icon className="w-5 h-5 text-primary" />
                    <h4 className="font-medium">{metric.name}</h4>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        {metric.description}
                      </span>
                      <span className="font-semibold">{metric.score}%</span>
                    </div>
                    <div className="h-2 bg-secondary rounded-full overflow-hidden">
                      <div
                        className="h-full bg-primary transition-all duration-500 ease-in-out"
                        style={{ width: `${metric.score}%` }}
                      />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Overall Status */}
          <div className="bg-card p-4 rounded-lg space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-semibold">Overall Status</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  Strong relationship with consistent growth
                </p>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-3xl font-bold text-primary">{candidate.relationshipScore}</span>
                {getTrendIcon("increasing")}
              </div>
            </div>
            
            {isLoading ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
              </div>
            ) : analysis ? (
              <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                <h4 className="font-medium mb-2">AI Relationship Analysis</h4>
                <p className="text-sm whitespace-pre-line">{analysis}</p>
              </div>
            ) : null}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
