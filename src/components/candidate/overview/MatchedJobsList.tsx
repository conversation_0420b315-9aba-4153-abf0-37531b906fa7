
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useState, useEffect } from "react";
import { JobMatchCard } from "./JobMatchCard";
import { JobDetailsDialog } from "./JobDetailsDialog";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface JobMatch {
  id: string;
  title: string;
  company?: string;
  location: string;
  salary_range?: string;
  match: number;
  description: string;
  requirements?: string[];
  benefits?: string[];
  department: string;
  job_type: string;
  experience_required?: string;
  is_urgent?: boolean;
  status: string;
}

interface MatchedJobsListProps {
  jobs?: JobMatch[];
}

export function MatchedJobsList({ jobs }: MatchedJobsListProps) {
  const [selectedJob, setSelectedJob] = useState<JobMatch | null>(null);
  const [matchedJobs, setMatchedJobs] = useState<JobMatch[]>([]);
  const { user } = useAuth();

  useEffect(() => {
    const fetchJobs = async () => {
      if (!user) return;

      try {
        const { data: jobsData, error } = await supabase
          .from('jobs')
          .select('*')
          .eq('user_id', user.id)
          .eq('is_active', true);

        if (error) {
          console.error('Error fetching jobs:', error);
          return;
        }

        if (jobsData) {
          // Transform jobs to include match scores and status
          const jobsWithMatches: JobMatch[] = jobsData.map(job => ({
            id: job.id,
            title: job.title,
            company: 'Your Company', // Could be made configurable
            location: job.location,
            salary_range: job.salary_range,
            match: Math.floor(Math.random() * 30) + 70, // Random match between 70-100
            description: job.description,
            requirements: job.requirements,
            benefits: job.benefits,
            department: job.department,
            job_type: job.job_type,
            experience_required: job.experience_required,
            is_urgent: job.is_urgent,
            status: job.is_urgent ? 'Urgent' : 'Active'
          }));

          // Sort by match score (highest first)
          setMatchedJobs(jobsWithMatches.sort((a, b) => b.match - a.match));
        }
      } catch (error) {
        console.error('Error in fetchJobs:', error);
      }
    };

    fetchJobs();
  }, [user]);

  const jobsToShow = jobs || matchedJobs;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Matched Jobs</CardTitle>
          <Badge variant="outline" className="font-normal">
            {jobsToShow.length} matches
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-4">
            {jobsToShow.length === 0 ? (
              <p className="text-center text-muted-foreground py-8">
                No matching jobs found
              </p>
            ) : (
              jobsToShow.map((job) => (
                <Dialog key={job.id} open={selectedJob?.id === job.id} onOpenChange={() => setSelectedJob(null)}>
                  <JobMatchCard
                    job={{
                      id: job.id,
                      title: job.title,
                      company: job.company || 'Your Company',
                      location: job.location,
                      match: job.match,
                      status: job.status
                    }}
                    onClick={() => setSelectedJob(job)}
                  />
                  {selectedJob?.id === job.id && (
                    <JobDetailsDialog
                      job={job}
                      onClose={() => setSelectedJob(null)}
                    />
                  )}
                </Dialog>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
