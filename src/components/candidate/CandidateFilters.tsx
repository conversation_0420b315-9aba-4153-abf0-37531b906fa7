
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON>lide<PERSON> } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";

export function CandidateFilters() {
  return (
    <div className="space-y-6 mt-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Experience Level</CardTitle>
        </CardHeader>
        <CardContent>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select experience level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="junior">Junior (0-2 years)</SelectItem>
              <SelectItem value="mid">Mid-level (3-5 years)</SelectItem>
              <SelectItem value="senior">Senior (6-10 years)</SelectItem>
              <SelectItem value="lead">Lead (10+ years)</SelectItem>
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Location Preference</CardTitle>
        </CardHeader>
        <CardContent>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select location preference" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="remote">Remote</SelectItem>
              <SelectItem value="hybrid">Hybrid</SelectItem>
              <SelectItem value="onsite">On-site</SelectItem>
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Skills</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {["React", "TypeScript", "Node.js", "Python", "Java", "AWS"].map((skill) => (
            <div key={skill} className="flex items-center space-x-2">
              <Checkbox id={skill} />
              <Label htmlFor={skill}>{skill}</Label>
            </div>
          ))}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Relationship Score</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Slider defaultValue={[70]} max={100} step={1} />
            <div className="text-sm text-muted-foreground">Minimum score: 70%</div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-2">
        <Button variant="outline" className="flex-1">Clear Filters</Button>
        <Button className="flex-1">Apply Filters</Button>
      </div>
    </div>
  );
}
