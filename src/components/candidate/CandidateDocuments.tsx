
import { useState } from "react";
import { CandidateType } from "@/types/candidate";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Upload, Loader2 } from "lucide-react";
import { DocumentList } from "./documents/DocumentList";
import { LoadingState } from "./documents/LoadingState";
import { EmptyState } from "./documents/EmptyState";
import { useDocumentManagement } from "./documents/useDocumentManagement";

interface CandidateDocumentsProps {
  candidate: CandidateType;
}

export function CandidateDocuments({ candidate }: CandidateDocumentsProps) {
  const [isUploading, setIsUploading] = useState(false);
  const { documents, isLoading, deleteMutation, handleFileUpload } = useDocumentManagement(candidate.id);

  const handleFileInputChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    try {
      await handleFileUpload(file);
    } finally {
      setIsUploading(false);
      event.target.value = '';
    }
  };

  const handleDownload = async (fileUrl: string, fileName: string) => {
    try {
      const response = await fetch(fileUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const handleDelete = (id: string) => {
    deleteMutation.mutate(id);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Documents</CardTitle>
        <div className="relative">
          <input
            type="file"
            id="file-upload"
            className="hidden"
            onChange={handleFileInputChange}
            disabled={isUploading}
            accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg"
          />
          <Button asChild disabled={isUploading}>
            <label htmlFor="file-upload" className="cursor-pointer">
              {isUploading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Upload className="w-4 h-4 mr-2" />
              )}
              Upload Document
            </label>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <LoadingState />
        ) : !documents || documents.length === 0 ? (
          <EmptyState />
        ) : (
          <DocumentList 
            documents={documents}
            onDownload={handleDownload}
            onDelete={handleDelete}
            isDeleting={deleteMutation.isPending}
          />
        )}
      </CardContent>
    </Card>
  );
}
