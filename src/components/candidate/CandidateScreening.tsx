import { useState } from "react";
import { CandidateType, ScreeningQuestion, Requirements } from "@/types/candidate";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Save } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CandidateRequirements } from "./screening/CandidateRequirements";
import { CandidateEvaluations } from "./screening/CandidateEvaluations";
import { AIInterviewQuestions } from "./screening/AIInterviewQuestions";
import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useCreateTimelineEntry } from "@/hooks/useCreateTimelineEntry";

interface CandidateScreeningProps {
  candidate: CandidateType;
}

export function CandidateScreening({ candidate }: CandidateScreeningProps) {
  const [isEditing, setIsEditing] = useState(false);
  const { toast } = useToast();
  const [screeningData, setScreeningData] = useState(candidate.screening || {
    dayToDay: "",
    skills: [],
    location: "",
    compensation: { current: 0, expected: 0 },
    education: [],
    questions: [],
    status: "pending" as const,
    lastUpdated: new Date().toISOString(),
    requirements: {
      workAuthorization: "pending" as const,
      backgroundCheck: "pending" as const,
      drugScreening: "not_required" as const,
      references: "pending" as const,
    },
    evaluations: [
      {
        category: "Technical Skills",
        score: 4,
        notes: "Strong frontend development skills",
      },
      {
        category: "Communication",
        score: 5,
        notes: "Excellent verbal and written communication",
      },
      {
        category: "Problem Solving",
        score: 4,
        notes: "Good analytical thinking",
      },
    ],
  });

  const { user } = useAuth();
	const createTimelineEntry = useCreateTimelineEntry();

  const screeningMutation = useMutation({
    mutationFn: async (data: any) => {
      const { data: updatedScreening, error } = await supabase
        .from("candidates")
        .update({ screening: data })
        .eq("id", candidate.id)
        .select();

      if (error) {
        throw new Error(error.message);
      }
      return updatedScreening;
    },
    onSuccess: async () => {
      toast({
        title: "Screening Updated",
        description: "The screening information has been saved successfully.",
      });
      setIsEditing(false);
      // Real-time subscription will automatically update the UI
      await createTimelineEntry.mutateAsync({
        candidate_id: candidate.id,
        event_type: 'update',
        title: 'Updated screening notes',
        description: 'Candidate screening information has been updated',
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSave = async () => {
    await screeningMutation.mutateAsync(screeningData);
  };

  const addQuestion = () => {
    const newQuestion: ScreeningQuestion = {
      id: Math.random().toString(),
      question: "",
      category: "technical",
    };
    setScreeningData({
      ...screeningData,
      questions: [...(screeningData.questions || []), newQuestion],
    });
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Screening Details</CardTitle>
            <div className="flex gap-2">
              {isEditing ? (
                <Button onClick={handleSave}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              ) : (
                <Button onClick={() => setIsEditing(true)}>Edit Screening</Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-2">Day to Day</h4>
                {isEditing ? (
                  <Textarea
                    value={screeningData.dayToDay}
                    onChange={(e) =>
                      setScreeningData({
                        ...screeningData,
                        dayToDay: e.target.value,
                      })
                    }
                    placeholder="Describe the candidate's day-to-day responsibilities..."
                  />
                ) : (
                  <p className="text-sm text-muted-foreground">
                    {screeningData.dayToDay || "No information provided"}
                  </p>
                )}
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">Skills</h4>
                {isEditing ? (
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add skill and press Enter"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          const input = e.target as HTMLInputElement;
                          setScreeningData({
                            ...screeningData,
                            skills: [...screeningData.skills, input.value],
                          });
                          input.value = "";
                        }
                      }}
                    />
                  </div>
                ) : (
                  <div className="flex flex-wrap gap-2">
                    {screeningData.skills.map((skill) => (
                      <Badge key={skill} variant="secondary">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">Screening Questions</h4>
                <div className="space-y-4">
                  {screeningData.questions?.map((question, index) => (
                    <div key={question.id} className="space-y-2">
                      {isEditing ? (
                        <div className="space-y-2">
                          <Input
                            value={question.question}
                            onChange={(e) => {
                              const updatedQuestions = [
                                ...(screeningData.questions || []),
                              ];
                              updatedQuestions[index] = {
                                ...question,
                                question: e.target.value,
                              };
                              setScreeningData({
                                ...screeningData,
                                questions: updatedQuestions,
                              });
                            }}
                            placeholder="Enter screening question..."
                          />
                          <Select
                            value={question.category}
                            onValueChange={(value) => {
                              const updatedQuestions = [
                                ...(screeningData.questions || []),
                              ];
                              updatedQuestions[index] = {
                                ...question,
                                category: value as
                                  | "technical"
                                  | "behavioral"
                                  | "cultural"
                                  | "experience",
                              };
                              setScreeningData({
                                ...screeningData,
                                questions: updatedQuestions,
                              });
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="technical">Technical</SelectItem>
                              <SelectItem value="behavioral">
                                Behavioral
                              </SelectItem>
                              <SelectItem value="cultural">Cultural</SelectItem>
                              <SelectItem value="experience">
                                Experience
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      ) : (
                        <div>
                          <p className="text-sm">{question.question}</p>
                          <Badge variant="outline" className="mt-1">
                            {question.category}
                          </Badge>
                          {question.response && (
                            <p className="text-sm text-muted-foreground mt-2">
                              Response: {question.response}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                  {isEditing && (
                    <Button variant="outline" onClick={addQuestion}>
                      <Plus className="w-4 h-4 mr-2" />
                      Add Question
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="space-y-6">
          <CandidateRequirements requirements={screeningData.requirements} />
          <CandidateEvaluations evaluations={screeningData.evaluations} />
        </div>
      </div>

      <AIInterviewQuestions candidate={candidate} />
    </div>
  );
}