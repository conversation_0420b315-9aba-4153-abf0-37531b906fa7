
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { X, Plus, User, Mail, Phone, MapPin, Globe, Award } from "lucide-react";
import { useCreateCandidate } from "@/hooks/useCreateCandidate";
import { useToast } from "@/hooks/use-toast";

const candidateSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  role: z.string().min(2, "Role is required"),
  phone: z.string().optional(),
  location: z.string().optional(),
  experience: z.string().optional(),
  industry: z.string().optional(),
  remote_preference: z.string().optional(),
  visa_status: z.string().optional(),
  linkedin_url: z.string().url("Invalid LinkedIn URL").optional().or(z.literal("")),
  github_url: z.string().url("Invalid GitHub URL").optional().or(z.literal("")),
  ai_summary: z.string().optional(),
});

type CandidateFormData = z.infer<typeof candidateSchema>;

interface AddCandidateFormProps {
  onSuccess?: () => void;
}

export function AddCandidateForm({ onSuccess }: AddCandidateFormProps) {
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");
  const [skills, setSkills] = useState<{ name: string; level: string; years: number }[]>([]);
  const [newSkill, setNewSkill] = useState({ name: "", level: "Beginner", years: 0 });

  const { toast } = useToast();
  const createCandidate = useCreateCandidate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<CandidateFormData>({
    resolver: zodResolver(candidateSchema),
  });

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const addSkill = () => {
    if (newSkill.name.trim() && newSkill.years > 0) {
      setSkills([...skills, { ...newSkill, name: newSkill.name.trim() }]);
      setNewSkill({ name: "", level: "Beginner", years: 0 });
    }
  };

  const removeSkill = (index: number) => {
    setSkills(skills.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: CandidateFormData) => {
    try {
      console.log("🚀 Form submission started with data:", data);
      
      // Clean and validate required fields
      if (!data.name?.trim() || !data.email?.trim() || !data.role?.trim()) {
        throw new Error("Name, email, and role are required fields");
      }
      
      // Ensure required fields are present and properly typed
      const candidateData = {
        name: data.name.trim(),
        email: data.email.trim(),
        role: data.role.trim(),
        phone: data.phone?.trim() || null,
        location: data.location?.trim() || null,
        experience: data.experience?.trim() || null,
        industry: data.industry?.trim() || null,
        remote_preference: data.remote_preference?.trim() || null,
        visa_status: data.visa_status?.trim() || null,
        linkedin_url: data.linkedin_url?.trim() || null,
        github_url: data.github_url?.trim() || null,
        ai_summary: data.ai_summary?.trim() || null,
        tags: tags.length > 0 ? tags : [],
        skills: skills.length > 0 ? skills : [],
      };

      console.log("🔄 Processed candidate data for submission:", candidateData);
      
      const result = await createCandidate.mutateAsync(candidateData);
      console.log("✅ Candidate creation mutation completed:", result);
      
      // Reset form state
      reset();
      setTags([]);
      setSkills([]);
      
      // Call success callback
      onSuccess?.();
      
      toast({
        title: "Success",
        description: "Candidate has been added successfully!",
      });
    } catch (error) {
      console.error("❌ Form submission error:", error);
      toast({
        title: "Error",
        description: `Failed to create candidate: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Add New Candidate
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Full Name *</Label>
              <Input
                id="name"
                {...register("name")}
                placeholder="John Doe"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                {...register("email")}
                placeholder="<EMAIL>"
                className={errors.email ? "border-red-500" : ""}
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="role">Role *</Label>
              <Input
                id="role"
                {...register("role")}
                placeholder="Senior Frontend Developer"
                className={errors.role ? "border-red-500" : ""}
              />
              {errors.role && (
                <p className="text-sm text-red-500">{errors.role.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                {...register("phone")}
                placeholder="+****************"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                {...register("location")}
                placeholder="San Francisco, CA"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="experience">Years of Experience</Label>
              <Input
                id="experience"
                {...register("experience")}
                placeholder="5 years"
              />
            </div>
          </div>

          {/* Professional Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="industry">Industry</Label>
              <Select onValueChange={(value) => setValue("industry", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select industry" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="technology">Technology</SelectItem>
                  <SelectItem value="finance">Finance</SelectItem>
                  <SelectItem value="healthcare">Healthcare</SelectItem>
                  <SelectItem value="education">Education</SelectItem>
                  <SelectItem value="retail">Retail</SelectItem>
                  <SelectItem value="manufacturing">Manufacturing</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="remote_preference">Remote Work Preference</Label>
              <Select onValueChange={(value) => setValue("remote_preference", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select preference" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="remote">Fully Remote</SelectItem>
                  <SelectItem value="hybrid">Hybrid</SelectItem>
                  <SelectItem value="onsite">On-site Only</SelectItem>
                  <SelectItem value="flexible">Flexible</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="visa_status">Visa Status</Label>
              <Select onValueChange={(value) => setValue("visa_status", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="citizen">US Citizen</SelectItem>
                  <SelectItem value="permanent_resident">Permanent Resident</SelectItem>
                  <SelectItem value="h1b">H1B</SelectItem>
                  <SelectItem value="opt">OPT</SelectItem>
                  <SelectItem value="requires_sponsorship">Requires Sponsorship</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Social Links */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="linkedin_url">LinkedIn URL</Label>
              <Input
                id="linkedin_url"
                {...register("linkedin_url")}
                placeholder="https://linkedin.com/in/johndoe"
                className={errors.linkedin_url ? "border-red-500" : ""}
              />
              {errors.linkedin_url && (
                <p className="text-sm text-red-500">{errors.linkedin_url.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="github_url">GitHub URL</Label>
              <Input
                id="github_url"
                {...register("github_url")}
                placeholder="https://github.com/johndoe"
                className={errors.github_url ? "border-red-500" : ""}
              />
              {errors.github_url && (
                <p className="text-sm text-red-500">{errors.github_url.message}</p>
              )}
            </div>
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => removeTag(tag)}
                  />
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag"
                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTag())}
              />
              <Button type="button" variant="outline" size="sm" onClick={addTag}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Skills */}
          <div className="space-y-2">
            <Label>Skills</Label>
            <div className="space-y-2 mb-4">
              {skills.map((skill, index) => (
                <div key={index} className="flex items-center gap-2 p-2 border rounded">
                  <span className="font-medium">{skill.name}</span>
                  <Badge variant="outline">{skill.level}</Badge>
                  <span className="text-sm text-muted-foreground">{skill.years} years</span>
                  <X
                    className="h-4 w-4 cursor-pointer ml-auto"
                    onClick={() => removeSkill(index)}
                  />
                </div>
              ))}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
              <Input
                value={newSkill.name}
                onChange={(e) => setNewSkill({ ...newSkill, name: e.target.value })}
                placeholder="Skill name"
              />
              <Select
                value={newSkill.level}
                onValueChange={(value) => setNewSkill({ ...newSkill, level: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Beginner">Beginner</SelectItem>
                  <SelectItem value="Intermediate">Intermediate</SelectItem>
                  <SelectItem value="Advanced">Advanced</SelectItem>
                  <SelectItem value="Expert">Expert</SelectItem>
                </SelectContent>
              </Select>
              <Input
                type="number"
                min="0"
                value={newSkill.years}
                onChange={(e) => setNewSkill({ ...newSkill, years: parseInt(e.target.value) || 0 })}
                placeholder="Years"
              />
              <Button type="button" variant="outline" size="sm" onClick={addSkill}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* AI Summary */}
          <div className="space-y-2">
            <Label htmlFor="ai_summary">Professional Summary</Label>
            <Textarea
              id="ai_summary"
              {...register("ai_summary")}
              placeholder="Brief professional summary or notes about the candidate..."
              rows={3}
            />
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={createCandidate.isPending}
          >
            {createCandidate.isPending ? "Adding Candidate..." : "Add Candidate"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
