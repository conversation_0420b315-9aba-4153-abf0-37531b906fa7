
import { CandidateType } from "@/types/candidate";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useCreateTimelineEntry } from "@/hooks/useCreateTimelineEntry";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

interface CandidateTimelineProps {
  candidate: CandidateType;
}

interface TimelineEvent {
  id: string;
  event_date: string;
  event_type: string;
  title: string;
  description: string;
  status: string;
}

export function CandidateTimeline({ candidate }: CandidateTimelineProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const createTimelineEntry = useCreateTimelineEntry();
  const [isAddingEntry, setIsAddingEntry] = useState(false);
  const [newEntry, setNewEntry] = useState({
    title: "",
    description: "",
    event_type: "general",
    event_date: new Date().toISOString().split('T')[0],
    status: "completed"
  });

  const fetchTimeline = async () => {
      if (!user) return [];
      
      const { data, error } = await supabase
        .from('candidate_timeline')
        .select('*')
        .eq('candidate_id', candidate.id)
        .eq('user_id', user.id)
        .order('event_date', { ascending: false });

      if (error) throw error;
      return data || [];
  };
  
  const { records: timeline = [], isLoading } = useRealtimeCollection(
    'candidate_timeline',
    fetchTimeline,
    'public',
    `candidate_id=eq.${candidate.id}`
  );

  const getTimelineIcon = (type: string) => {
    switch (type) {
      case "interview":
        return "🎯";
      case "document":
        return "📄";
      case "screening":
        return "✅";
      case "application":
        return "📝";
      default:
        return "•";
    }
  };

  const handleAddEntry = async () => {
    if (!newEntry.title.trim()) {
      toast({
        title: "Title Required",
        description: "Please enter a title for the timeline entry",
        variant: "destructive"
      });
      return;
    }
    
    try {
      await createTimelineEntry.mutateAsync({
        candidate_id: candidate.id,
        event_type: newEntry.event_type,
        title: newEntry.title,
        description: newEntry.description,
        event_date: newEntry.event_date,
        status: newEntry.status
      });
      
      setIsAddingEntry(false);
      setNewEntry({
        title: "",
        description: "",
        event_type: "general",
        event_date: new Date().toISOString().split('T')[0],
        status: "completed"
      });
      
      toast({
        title: "Entry Added",
        description: "Timeline entry has been added successfully"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add timeline entry",
        variant: "destructive"
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "upcoming":
        return "bg-blue-500";
      case "completed":
        return "bg-green-500";
      case "pending":
        return "bg-yellow-500";
      default:
        return "bg-gray-500";
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="animate-pulse space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-16 bg-muted rounded" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Candidate Timeline</CardTitle>
          <Button onClick={() => setIsAddingEntry(!isAddingEntry)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Entry
          </Button>
        </CardHeader>
        <CardContent>
          {isAddingEntry && (
            <div className="mb-6 space-y-4 p-4 border rounded-lg">
              <div className="space-y-2">
                <label className="text-sm font-medium">Title</label>
                <Input
                  value={newEntry.title}
                  onChange={(e) => setNewEntry({...newEntry, title: e.target.value})}
                  placeholder="Entry title"
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  value={newEntry.description}
                  onChange={(e) => setNewEntry({...newEntry, description: e.target.value})}
                  placeholder="Entry description"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Event Type</label>
                  <Select
                    value={newEntry.event_type}
                    onValueChange={(value) => setNewEntry({...newEntry, event_type: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select event type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">General</SelectItem>
                      <SelectItem value="interview">Interview</SelectItem>
                      <SelectItem value="document">Document</SelectItem>
                      <SelectItem value="note">Note</SelectItem>
                      <SelectItem value="application">Application</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Date</label>
                  <Input
                    type="date"
                    value={newEntry.event_date}
                    onChange={(e) => setNewEntry({...newEntry, event_date: e.target.value})}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select
                  value={newEntry.status}
                  onValueChange={(value) => setNewEntry({...newEntry, status: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="upcoming">Upcoming</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsAddingEntry(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddEntry}>
                  Add Entry
                </Button>
              </div>
            </div>
          )}
          
          <ScrollArea className="h-[500px] pr-4">
            {timeline.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <div className="text-4xl mb-4">📅</div>
                <p className="text-muted-foreground">
                  No timeline events yet. Timeline events will appear here as you interact with this candidate.
                </p>
              </div>
            ) : (
              <div className="relative">
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-border" />
                <div className="space-y-8">
                  {timeline.map((event) => (
                    <div key={event.id} className="relative pl-10">
                      <div className="absolute left-0 w-8 h-8 bg-background border-2 rounded-full flex items-center justify-center">
                        {getTimelineIcon(event.event_type)}
                      </div>
                      <div className="flex flex-col space-y-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium">
                            {new Date(event.event_date).toLocaleDateString()}
                          </span>
                          <Badge
                            variant="secondary"
                            className={`${getStatusColor(event.status)} text-white`}
                          >
                            {event.status}
                          </Badge>
                        </div>
                        <h4 className="text-base font-medium">{event.title}</h4>
                        <p className="text-sm text-muted-foreground">
                          {event.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
