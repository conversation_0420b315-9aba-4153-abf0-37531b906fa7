
import { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from "recharts";
import { 
  useCandidateMonthlyChartData,
  useCandidateSkillsChartData,
  useInitializeCandidateAnalytics
} from "@/hooks/useCandidateAnalytics";
import { useAuth } from "@/contexts/AuthContext";

export function CandidateAnalytics() {
  const { user } = useAuth();
  const userId = user?.id;

  const { 
    data: monthlyData = [], 
    isLoading: monthlyLoading, 
    error: monthlyError 
  } = useCandidateMonthlyChartData(userId || '');

  const { 
    data: skillsData = [], 
    isLoading: skillsLoading, 
    error: skillsError 
  } = useCandidateSkillsChartData(userId || '');

  const { 
    initializeAllData, 
    isInitializing 
  } = useInitializeCandidateAnalytics();

  // Initialize data if empty
  useEffect(() => {
    if (userId && monthlyData.length === 0 && skillsData.length === 0 && !monthlyLoading && !skillsLoading && !monthlyError && !skillsError) {
      initializeAllData(userId);
    }
  }, [userId, monthlyData.length, skillsData.length, monthlyLoading, skillsLoading, monthlyError, skillsError, initializeAllData]);

  const isLoading = monthlyLoading || skillsLoading || isInitializing;
  const hasError = monthlyError || skillsError;

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Candidates & Placements</CardTitle>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-[300px] w-full" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Skills Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-[300px] w-full" />
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Candidate Pipeline Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[400px] w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (hasError) {
    return (
      <Alert className="mb-4">
        <AlertDescription>
          Failed to load candidate analytics: {(monthlyError || skillsError)?.message}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Monthly Candidates & Placements</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="candidates" fill="#8884d8" name="Candidates" />
                <Bar dataKey="placements" fill="#82ca9d" name="Placements" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Skills Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={skillsData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {skillsData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Candidate Pipeline Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={monthlyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="candidates" stroke="#8884d8" strokeWidth={2} />
              <Line type="monotone" dataKey="placements" stroke="#82ca9d" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}
