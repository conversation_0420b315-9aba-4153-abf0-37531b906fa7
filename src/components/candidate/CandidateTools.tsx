import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ResumeParser } from "@/components/candidate/ResumeParser";
import { InterviewAssistant } from "@/components/ai/InterviewAssistant";
import { Button } from "@/components/ui/button";
import { FileText, MessageSquare, Calendar, Mail, Download } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export function CandidateTools() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("resume-parser");
  
  const handleExportData = () => {
    toast({
      title: "Data Export Started",
      description: "Your candidate data is being prepared for export."
    });
    
    // Simulate export process
    setTimeout(() => {
      toast({
        title: "Data Export Complete",
        description: "Your candidate data has been exported successfully."
      });
    }, 2000);
  };
  
  const handleBulkEmail = () => {
    toast({
      title: "Bulk Email",
      description: "Opening bulk email composer..."
    });
  };
  
  const handleBulkInterview = () => {
    toast({
      title: "Bulk Interview Scheduler",
      description: "Opening bulk interview scheduler..."
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Candidate Management Tools</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="resume-parser">Resume Parser</TabsTrigger>
            <TabsTrigger value="interview-assistant">Interview Assistant</TabsTrigger>
            <TabsTrigger value="bulk-actions">Bulk Actions</TabsTrigger>
          </TabsList>
          
          <TabsContent value="resume-parser">
            <ResumeParser />
          </TabsContent>
          
          <TabsContent value="interview-assistant">
            <InterviewAssistant />
          </TabsContent>
          
          <TabsContent value="bulk-actions">
            <Card>
              <CardHeader>
                <CardTitle>Bulk Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">
                  Perform actions on multiple candidates at once to save time and streamline your workflow.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button 
                    variant="outline" 
                    className="flex flex-col h-auto py-4 px-6 items-center justify-center gap-2"
                    onClick={handleBulkEmail}
                  >
                    <Mail className="h-8 w-8" />
                    <span className="font-medium">Bulk Email</span>
                    <span className="text-xs text-muted-foreground text-center">
                      Send emails to multiple candidates at once
                    </span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="flex flex-col h-auto py-4 px-6 items-center justify-center gap-2"
                    onClick={handleBulkInterview}
                  >
                    <Calendar className="h-8 w-8" />
                    <span className="font-medium">Bulk Interview</span>
                    <span className="text-xs text-muted-foreground text-center">
                      Schedule interviews for multiple candidates
                    </span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="flex flex-col h-auto py-4 px-6 items-center justify-center gap-2"
                    onClick={handleExportData}
                  >
                    <Download className="h-8 w-8" />
                    <span className="font-medium">Export Data</span>
                    <span className="text-xs text-muted-foreground text-center">
                      Export candidate data to CSV or Excel
                    </span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}