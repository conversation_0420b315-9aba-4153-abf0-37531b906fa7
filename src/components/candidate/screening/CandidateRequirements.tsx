import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { CheckCircle2, XCircle, AlertCircle } from "lucide-react";

interface RequirementsType {
  workAuthorization: "verified" | "pending" | "not_required";
  backgroundCheck: "verified" | "pending" | "not_required";
  drugScreening: "verified" | "pending" | "not_required";
  references: "verified" | "pending" | "not_required";
}

interface CandidateRequirementsProps {
  requirements: RequirementsType;
}

export function CandidateRequirements({ requirements }: CandidateRequirementsProps) {
  const getRequirementStatus = (status: string) => {
    switch (status) {
      case "verified":
        return (
          <Badge className="bg-success text-success-foreground">
            <CheckCircle2 className="w-3 h-3 mr-1" />
            Verified
          </Badge>
        );
      case "pending":
        return (
          <Badge variant="outline" className="text-warning border-warning">
            <AlertCircle className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        );
      case "not_required":
        return (
          <Badge variant="secondary">
            <XCircle className="w-3 h-3 mr-1" />
            Not Required
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Requirements</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Work Authorization</span>
            {getRequirementStatus(requirements.workAuthorization)}
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Background Check</span>
            {getRequirementStatus(requirements.backgroundCheck)}
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Drug Screening</span>
            {getRequirementStatus(requirements.drugScreening)}
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">References</span>
            {getRequirementStatus(requirements.references)}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}