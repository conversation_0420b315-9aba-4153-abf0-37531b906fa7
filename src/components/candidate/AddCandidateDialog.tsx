import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AddCandidateForm } from "./AddCandidateForm";

interface AddCandidateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AddCandidateDialog({ open, onOpenChange }: AddCandidateDialogProps) {
  const handleSuccess = () => {
    console.log("Candidate creation successful, closing dialog");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Candidate</DialogTitle>
        </DialogHeader>
        <AddCandidateForm onSuccess={handleSuccess} />
      </DialogContent>
    </Dialog>
  );
}