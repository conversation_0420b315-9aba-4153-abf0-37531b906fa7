
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Mail, Phone, MapPin, Calendar, Star } from "lucide-react";

interface CandidateDetailsDialogProps {
  candidate: {
    id: number;
    name: string;
    role: string;
    stage: string;
    rating: number;
    lastActivity: string;
  };
  onClose: () => void;
}

export function CandidateDetailsDialog({
  candidate,
  onClose,
}: CandidateDetailsDialogProps) {
  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Candidate Profile</DialogTitle>
          <DialogDescription>
            Detailed information about {candidate.name}
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="h-[500px] pr-4">
          <div className="space-y-6">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <Avatar className="h-16 w-16">
                  <AvatarImage src="/placeholder.svg" />
                  <AvatarFallback>
                    {candidate.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="text-lg font-semibold">{candidate.name}</h3>
                  <p className="text-muted-foreground">{candidate.role}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge>{candidate.stage}</Badge>
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-400 fill-yellow-400" />
                      <span className="ml-1 text-sm">{candidate.rating}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="space-x-2">
                <Button variant="outline" size="sm">
                  <Mail className="w-4 h-4 mr-2" />
                  Contact
                </Button>
                <Button size="sm">
                  <Calendar className="w-4 h-4 mr-2" />
                  Schedule
                </Button>
              </div>
            </div>

            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="text-4xl mb-4">📋</div>
              <p className="text-muted-foreground">
                Detailed candidate information will be loaded from your database once you have real candidate data.
              </p>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
