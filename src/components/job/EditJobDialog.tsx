
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Job } from "@/hooks/useJobs";
import { Plus, X } from "lucide-react";

interface EditJobDialogProps {
  job: Job;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedJob: Job) => void;
}

export function EditJobDialog({ job, isOpen, onClose, onSave }: EditJobDialogProps) {
  const [formData, setFormData] = useState<Job>(job);
  const [requirements, setRequirements] = useState<string[]>(job.requirements || [""]);
  const [benefits, setBenefits] = useState<string[]>(job.benefits || [""]);
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const filteredRequirements = requirements.filter(req => req.trim() !== "");
    const filteredBenefits = benefits.filter(benefit => benefit.trim() !== "");
    
    onSave({
      ...formData,
      requirements: filteredRequirements.length > 0 ? filteredRequirements : undefined,
      benefits: filteredBenefits.length > 0 ? filteredBenefits : undefined,
    });
    onClose();
  };

  const handleChange = (field: keyof Job, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addRequirement = () => {
    setRequirements([...requirements, ""]);
  };

  const removeRequirement = (index: number) => {
    setRequirements(requirements.filter((_, i) => i !== index));
  };

  const updateRequirement = (index: number, value: string) => {
    const updated = [...requirements];
    updated[index] = value;
    setRequirements(updated);
  };

  const addBenefit = () => {
    setBenefits([...benefits, ""]);
  };

  const removeBenefit = (index: number) => {
    setBenefits(benefits.filter((_, i) => i !== index));
  };

  const updateBenefit = (index: number, value: string) => {
    const updated = [...benefits];
    updated[index] = value;
    setBenefits(updated);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Job Posting</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Job Title</label>
              <Input
                value={formData.title}
                onChange={(e) => handleChange('title', e.target.value)}
                placeholder="Job Title"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Department</label>
              <Input
                value={formData.department}
                onChange={(e) => handleChange('department', e.target.value)}
                placeholder="Department"
                required
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Location</label>
              <Input
                value={formData.location}
                onChange={(e) => handleChange('location', e.target.value)}
                placeholder="Location"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Job Type</label>
              <Select value={formData.job_type} onValueChange={(value) => handleChange('job_type', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select job type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Full-time">Full-time</SelectItem>
                  <SelectItem value="Part-time">Part-time</SelectItem>
                  <SelectItem value="Contract">Contract</SelectItem>
                  <SelectItem value="Internship">Internship</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Salary Range</label>
              <Input
                value={formData.salary_range || ""}
                onChange={(e) => handleChange('salary_range', e.target.value)}
                placeholder="e.g., $120k - $160k"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Experience Required</label>
              <Input
                value={formData.experience_required || ""}
                onChange={(e) => handleChange('experience_required', e.target.value)}
                placeholder="e.g., 5+ years"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Job Description</label>
            <Textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder="Job Description"
              className="min-h-[120px]"
              required
            />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Requirements</label>
              <Button type="button" variant="outline" size="sm" onClick={addRequirement}>
                <Plus className="w-4 h-4 mr-1" />
                Add
              </Button>
            </div>
            {requirements.map((req, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  placeholder="Requirement"
                  value={req}
                  onChange={(e) => updateRequirement(index, e.target.value)}
                />
                {requirements.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removeRequirement(index)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Benefits</label>
              <Button type="button" variant="outline" size="sm" onClick={addBenefit}>
                <Plus className="w-4 h-4 mr-1" />
                Add
              </Button>
            </div>
            {benefits.map((benefit, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  placeholder="Benefit"
                  value={benefit}
                  onChange={(e) => updateBenefit(index, e.target.value)}
                />
                {benefits.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removeBenefit(index)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="urgent"
              checked={formData.is_urgent}
              onChange={(e) => handleChange('is_urgent', e.target.checked)}
              className="rounded"
            />
            <label htmlFor="urgent" className="text-sm font-medium">
              Mark as urgent
            </label>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              Save Changes
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
