
import { Button } from "@/components/ui/button";
import {
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  Building,
  MapPin,
  Briefcase,
  Calendar,
  DollarSign,
  CheckCircle2,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface JobDetailsDialogProps {
  job: any;
  onClose: () => void;
}

export function JobDetailsDialog({ job, onClose }: JobDetailsDialogProps) {
  const { toast } = useToast();

  const handleApply = () => {
    toast({
      title: "Application Submitted",
      description: "The candidate has been submitted for this position.",
    });
    onClose();
  };

  const getMatchColor = (match: number) => {
    if (match >= 90) return "text-green-500";
    if (match >= 70) return "text-yellow-500";
    return "text-orange-500";
  };

  return (
    <DialogContent className="max-w-2xl">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          {job.title}
          {job.match && (
            <Badge variant="secondary" className={getMatchColor(job.match)}>
              {job.match}% Match
            </Badge>
          )}
        </DialogTitle>
        <DialogDescription>
          Detailed job information and requirements
        </DialogDescription>
      </DialogHeader>
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium">Company Details</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                <Building className="w-4 h-4" />
                <span>{job.company}</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                <span>{job.location}</span>
              </div>
              <div className="flex items-center gap-2">
                <Briefcase className="w-4 h-4" />
                <span>{job.department}</span>
              </div>
            </div>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium">Job Overview</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                <DollarSign className="w-4 h-4" />
                <span>{job.salary}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>Posted: {job.postedDate}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">Key Requirements</h4>
          <ul className="grid grid-cols-2 gap-2">
            {job.requirements && job.requirements.map((req: string, index: number) => (
              <li key={index} className="flex items-center gap-2 text-sm">
                <CheckCircle2 className="w-4 h-4 text-green-500" />
                {req}
              </li>
            ))}
          </ul>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">Benefits</h4>
          <ul className="grid grid-cols-2 gap-2">
            {job.benefits && job.benefits.map((benefit: string, index: number) => (
              <li key={index} className="flex items-center gap-2 text-sm">
                <CheckCircle2 className="w-4 h-4 text-green-500" />
                {benefit}
              </li>
            ))}
          </ul>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button onClick={handleApply}>Submit Candidate</Button>
        </div>
      </div>
    </DialogContent>
  );
}
