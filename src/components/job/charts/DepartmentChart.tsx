import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>s, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer, Cell, PieChart, Pie } from 'recharts';

interface DepartmentChartProps {
  data: Array<{ name: string; count: number }>;
  chartType: 'bar' | 'pie';
}

export const DepartmentChart = ({ data, chartType }: DepartmentChartProps) => {
  if (chartType === 'bar') {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          layout="vertical"
          margin={{ top: 10, right: 30, left: 80, bottom: 10 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            type="number"
            axisLine={false}
            tickLine={false}
            style={{
              fontSize: '12px',
              fontFamily: 'inherit',
            }}
          />
          <YAxis
            type="category"
            dataKey="name"
            axisLine={false}
            tickLine={false}
            style={{
              fontSize: '12px',
              fontFamily: 'inherit',
            }}
          />
          <Tooltip
            cursor={{ fill: 'rgba(236, 237, 254, 0.4)' }}
            contentStyle={{
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              borderRadius: '8px',
              border: '1px solid #e2e8f0',
              boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
            }}
          />
          <Bar dataKey="count" radius={[0, 4, 4, 0]}>
            {data.map((_, index) => (
              <Cell 
                key={`cell-${index}`}
                fill={`url(#departmentGradient-${index})`}
              />
            ))}
          </Bar>
          <defs>
            {data.map((_, index) => (
              <linearGradient
                key={`departmentGradient-${index}`}
                id={`departmentGradient-${index}`}
                x1="0"
                y1="0"
                x2="1"
                y2="0"
              >
                <stop offset="0%" stopColor="#9b87f5" stopOpacity={0.8}/>
                <stop offset="100%" stopColor="#7E69AB" stopOpacity={0.3}/>
              </linearGradient>
            ))}
          </defs>
        </BarChart>
      </ResponsiveContainer>
    );
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          data={data}
          dataKey="count"
          nameKey="name"
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={80}
          paddingAngle={5}
          label
        >
          {data.map((_, index) => (
            <Cell 
              key={`cell-${index}`}
              fill={`url(#colorGradient-${index})`}
            />
          ))}
        </Pie>
        <Tooltip />
        <defs>
          {[
            ['#60A5FA', '#3B82F6'],
            ['#34D399', '#10B981'],
            ['#A78BFA', '#8B5CF6'],
            ['#F472B6', '#EC4899'],
            ['#FBBF24', '#D97706'],
          ].map((colors, index) => (
            <linearGradient
              key={`colorGradient-${index}`}
              id={`colorGradient-${index}`}
              x1="0"
              y1="0"
              x2="0"
              y2="1"
            >
              <stop offset="0%" stopColor={colors[0]} />
              <stop offset="100%" stopColor={colors[1]} />
            </linearGradient>
          ))}
        </defs>
      </PieChart>
    </ResponsiveContainer>
  );
};