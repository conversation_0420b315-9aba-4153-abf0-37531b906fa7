import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Globe,
  Check,
  AlertTriangle,
  RefreshCw,
  Settings,
  Loader2
} from "lucide-react";
import { useJobIntegrations, useConnectPlatform, useDisconnectPlatform, useSyncJobs } from "@/hooks/useJobIntegrations";

export function JobIntegrations() {
  const { toast } = useToast();
  const [isConnecting, setIsConnecting] = useState<string | null>(null);
  const [apiKey, setApiKey] = useState("");
  const [webhookUrl, setWebhookUrl] = useState("");
  const [autoSync, setAutoSync] = useState(true);

  // Use real hooks for job integrations
  const { data: integrations = [] } = useJobIntegrations();
  const connectPlatform = useConnectPlatform();
  const disconnectPlatform = useDisconnectPlatform();
  const syncJobs = useSyncJobs();

  // Helper function to get integration status
  const getIntegrationStatus = (platformName: string) => {
    const integration = integrations.find(i => i.name.toLowerCase() === platformName.toLowerCase());
    return integration?.status || 'disconnected';
  };
  
  const handleConnect = async (platform: string) => {
    setIsConnecting(platform);

    try {
      // Basic settings for the platform - in a real app, this would come from a form
      const settings = {
        apiKey: apiKey || 'demo-key',
        webhookUrl: webhookUrl || '',
        autoSync: autoSync
      };

      await connectPlatform.mutateAsync({ platform, settings });
    } catch (error) {
      console.error('Connection failed:', error);
    } finally {
      setIsConnecting(null);
    }
  };
  
  const handleDisconnect = async (platform: string) => {
    try {
      await disconnectPlatform.mutateAsync(platform);
    } catch (error) {
      console.error('Disconnection failed:', error);
    }
  };
  
  const handleSaveAPISettings = () => {
    toast({
      title: "API Settings Saved",
      description: "Your API settings have been updated successfully."
    });
  };
  
  const handleSyncNow = async () => {
    try {
      toast({
        title: "Sync Started",
        description: "Syncing job postings with external platforms..."
      });

      await syncJobs.mutateAsync(undefined);
    } catch (error) {
      console.error('Sync failed:', error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Job Board Integrations</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="job-boards">
          <TabsList className="mb-4">
            <TabsTrigger value="job-boards">Job Boards</TabsTrigger>
            <TabsTrigger value="api-settings">API Settings</TabsTrigger>
            <TabsTrigger value="sync-settings">Sync Settings</TabsTrigger>
          </TabsList>
          
          <TabsContent value="job-boards" className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="bg-[#0077B5] p-2 rounded-full">
                    <Globe className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium">LinkedIn</h3>
                    <p className="text-sm text-muted-foreground">
                      Post jobs directly to LinkedIn and receive applications
                    </p>
                  </div>
                </div>
                {getIntegrationStatus('linkedin') === 'connected' ? (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
                      <Check className="h-3 w-3" />
                      Connected
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnect('linkedin')}
                      disabled={disconnectPlatform.isPending}
                    >
                      {disconnectPlatform.isPending ? 'Disconnecting...' : 'Disconnect'}
                    </Button>
                  </div>
                ) : (
                  <Button
                    onClick={() => handleConnect('linkedin')}
                    disabled={isConnecting === 'linkedin' || connectPlatform.isPending}
                  >
                    {isConnecting === 'linkedin' || connectPlatform.isPending ? 'Connecting...' : 'Connect'}
                  </Button>
                )}
              </div>
              
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="bg-[#003A9B] p-2 rounded-full">
                    <Globe className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium">Indeed</h3>
                    <p className="text-sm text-muted-foreground">
                      Publish job listings on Indeed and track applications
                    </p>
                  </div>
                </div>
                {getIntegrationStatus('indeed') === 'connected' ? (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
                      <Check className="h-3 w-3" />
                      Connected
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnect('indeed')}
                      disabled={disconnectPlatform.isPending}
                    >
                      {disconnectPlatform.isPending ? 'Disconnecting...' : 'Disconnect'}
                    </Button>
                  </div>
                ) : (
                  <Button
                    onClick={() => handleConnect('indeed')}
                    disabled={isConnecting === 'indeed' || connectPlatform.isPending}
                  >
                    {isConnecting === 'indeed' || connectPlatform.isPending ? 'Connecting...' : 'Connect'}
                  </Button>
                )}
              </div>
              
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="bg-[#0CAA41] p-2 rounded-full">
                    <Globe className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium">Glassdoor</h3>
                    <p className="text-sm text-muted-foreground">
                      Share job openings on Glassdoor and manage responses
                    </p>
                  </div>
                </div>
                {getIntegrationStatus('glassdoor') === 'connected' ? (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
                      <Check className="h-3 w-3" />
                      Connected
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnect('glassdoor')}
                      disabled={disconnectPlatform.isPending}
                    >
                      {disconnectPlatform.isPending ? 'Disconnecting...' : 'Disconnect'}
                    </Button>
                  </div>
                ) : (
                  <Button
                    onClick={() => handleConnect('glassdoor')}
                    disabled={isConnecting === 'glassdoor' || connectPlatform.isPending}
                  >
                    {isConnecting === 'glassdoor' || connectPlatform.isPending ? 'Connecting...' : 'Connect'}
                  </Button>
                )}
              </div>
            </div>
            
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-5 w-5 text-warning shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium">Integration Notice</h4>
                  <p className="text-sm text-muted-foreground">
                    Connecting to job boards requires approval from each platform. The integration process may take 1-2 business days to complete.
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="api-settings" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="api-key">API Key</Label>
              <Input
                id="api-key"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter your API key"
              />
              <p className="text-xs text-muted-foreground">
                This API key is used for all job board integrations.
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="webhook-url">Webhook URL</Label>
              <Input
                id="webhook-url"
                value={webhookUrl}
                onChange={(e) => setWebhookUrl(e.target.value)}
                placeholder="https://your-webhook-url.com/jobs"
              />
              <p className="text-xs text-muted-foreground">
                Receive real-time notifications when candidates apply through job boards.
              </p>
            </div>
            
            <Button onClick={handleSaveAPISettings}>
              Save API Settings
            </Button>
          </TabsContent>
          
          <TabsContent value="sync-settings" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="auto-sync">Automatic Synchronization</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically sync job postings with connected platforms
                </p>
              </div>
              <Switch
                id="auto-sync"
                checked={autoSync}
                onCheckedChange={setAutoSync}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Sync Frequency</Label>
                <p className="text-sm text-muted-foreground">
                  How often to sync job postings with external platforms
                </p>
              </div>
              <Select defaultValue="daily">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hourly">Hourly</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Last Sync</Label>
                <p className="text-sm text-muted-foreground">
                  Last successful synchronization with job boards
                </p>
              </div>
              <div className="text-sm">Today, 10:45 AM</div>
            </div>
            
            <div className="flex justify-between items-center pt-4">
              <Button
                variant="outline"
                onClick={handleSyncNow}
                disabled={syncJobs.isPending}
              >
                {syncJobs.isPending ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="mr-2 h-4 w-4" />
                )}
                {syncJobs.isPending ? 'Syncing...' : 'Sync Now'}
              </Button>
              
              <Button variant="outline">
                <Settings className="mr-2 h-4 w-4" />
                Advanced Settings
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}