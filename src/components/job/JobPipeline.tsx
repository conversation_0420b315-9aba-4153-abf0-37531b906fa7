import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { PipelineStageDialog } from "./pipeline/PipelineStageDialog";
import { CandidateDetailsDialog } from "./pipeline/CandidateDetailsDialog";
import { 
  usePipelineStages, 
  usePipelineCandidates, 
  useInitializePipelineData 
} from "@/hooks/usePipeline";
import { useAuth } from "@/contexts/AuthContext";

export function JobPipeline() {
  const { user } = useAuth();
  const userId = user?.id;

  const { 
    data: pipelineStages = [], 
    isLoading: stagesLoading, 
    error: stagesError 
  } = usePipelineStages(userId || '');

  const { 
    data: candidates = [], 
    isLoading: candidatesLoading, 
    error: candidatesError 
  } = usePipelineCandidates(userId || '');

  const { initializeDefaultStages, isLoading: initLoading } = useInitializePipelineData();

  const [selectedStage, setSelectedStage] = useState<any>(null);
  const [selectedCandidate, setSelectedCandidate] = useState<any>(null);

  // Initialize data if empty
  useEffect(() => {
    if (userId && pipelineStages.length === 0 && !stagesLoading && !stagesError) {
      initializeDefaultStages(userId);
    }
  }, [userId, pipelineStages.length, stagesLoading, stagesError, initializeDefaultStages]);

  if (stagesLoading || candidatesLoading) {
    return (
      <div className="space-y-4">
        <div className="grid gap-4 md:grid-cols-5">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-12 mb-2" />
                <Skeleton className="h-2 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Recent Candidates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (stagesError || candidatesError) {
    return (
      <Alert className="mb-4">
        <AlertDescription>
          Failed to load pipeline data: {(stagesError || candidatesError)?.message}
        </AlertDescription>
      </Alert>
    );
  }

  // Calculate max count for progress bar
  const maxCount = Math.max(...pipelineStages.map(stage => stage.count), 1);

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-5">
        {pipelineStages.map((stage) => (
          <Card 
            key={stage.id}
            className="cursor-pointer transition-all hover:shadow-md"
            onClick={() => setSelectedStage(stage)}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                {stage.name}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stage.count}</div>
              <div className="mt-2 h-2 rounded-full bg-gray-200">
                <div
                  className={`h-2 rounded-full ${stage.color}`}
                  style={{ width: `${(stage.count / maxCount) * 100}%` }}
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Candidates</CardTitle>
        </CardHeader>
        <CardContent>
          {candidates.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Stage</TableHead>
                  <TableHead>Rating</TableHead>
                  <TableHead>Last Activity</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {candidates.map((candidate) => (
                  <TableRow 
                    key={candidate.id}
                    className="cursor-pointer hover:bg-accent/50"
                    onClick={() => setSelectedCandidate(candidate)}
                  >
                    <TableCell className="font-medium">
                      {candidate.candidate_name}
                    </TableCell>
                    <TableCell>{candidate.role}</TableCell>
                    <TableCell>
                      <Badge variant="secondary">{candidate.stage}</Badge>
                    </TableCell>
                    <TableCell>{candidate.rating}</TableCell>
                    <TableCell>{candidate.last_activity}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">No candidates in pipeline yet</p>
              <Button variant="outline" size="sm">
                Add Candidate
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {selectedStage && (
        <PipelineStageDialog
          stage={selectedStage}
          onClose={() => setSelectedStage(null)}
        />
      )}

      {selectedCandidate && (
        <CandidateDetailsDialog
          candidate={selectedCandidate}
          onClose={() => setSelectedCandidate(null)}
        />
      )}
    </div>
  );
}