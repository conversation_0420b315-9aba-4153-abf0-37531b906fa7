import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";

export function JobFilters() {
  const [salaryRange, setSalaryRange] = useState([40000, 150000]);

  return (
    <div className="py-4 space-y-6 overflow-y-auto max-h-[80vh]">
      <div className="space-y-6">
        <div className="space-y-3">
          <Label className="text-sm font-medium">Job Type</Label>
          <RadioGroup defaultValue="all" className="space-y-2">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="all" id="all" />
              <Label htmlFor="all" className="text-sm">All</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="fulltime" id="fulltime" />
              <Label htmlFor="fulltime" className="text-sm">Full-time</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="parttime" id="parttime" />
              <Label htmlFor="parttime" className="text-sm">Part-time</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="contract" id="contract" />
              <Label htmlFor="contract" className="text-sm">Contract</Label>
            </div>
          </RadioGroup>
        </div>

        <div className="space-y-3">
          <Label className="text-sm font-medium">Experience Level</Label>
          <Select>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="entry">Entry Level</SelectItem>
              <SelectItem value="mid">Mid Level</SelectItem>
              <SelectItem value="senior">Senior Level</SelectItem>
              <SelectItem value="lead">Lead</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-3">
          <Label className="text-sm font-medium">
            Salary Range (${salaryRange[0].toLocaleString()} - ${salaryRange[1].toLocaleString()})
          </Label>
          <div className="px-2">
            <Slider
              defaultValue={[40000, 150000]}
              max={300000}
              min={0}
              step={5000}
              value={salaryRange}
              onValueChange={setSalaryRange}
              className="w-full"
            />
          </div>
        </div>

        <div className="space-y-3">
          <Label className="text-sm font-medium">Location</Label>
          <Select>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select location" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="remote">Remote</SelectItem>
              <SelectItem value="hybrid">Hybrid</SelectItem>
              <SelectItem value="onsite">On-site</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="urgent" className="text-sm font-medium">Urgent Positions Only</Label>
            <Switch id="urgent" />
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="featured" className="text-sm font-medium">Featured Jobs Only</Label>
            <Switch id="featured" />
          </div>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
        <Button className="flex-1 w-full">Apply Filters</Button>
        <Button variant="outline" className="flex-1 w-full">Reset</Button>
      </div>
    </div>
  );
}