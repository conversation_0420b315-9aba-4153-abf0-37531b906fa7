import { useState, useEffect, use<PERSON>emo } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { toast } from "sonner";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useAuth } from "@/contexts/AuthContext";
import { 
  Flag, 
  Users, 
  BarChart3, 
  Plus, 
  Trash2,
  RefreshCw,
  Settings,
  UserPlus,
  Percent
} from "lucide-react";
import { featureFlagService, FeatureFlag } from "@/services";
import { supabase } from "@/integrations/supabase/client";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Ta<PERSON>,
  Ta<PERSON><PERSON>ontent,
  <PERSON><PERSON>List,
  TabsTrigger,
} from "@/components/ui/tabs";

interface PilotUser {
  id: string;
  email: string;
  full_name?: string;
}

export function FeatureFlagManager() {
  const { user } = useAuth();
  const [selectedFlag, setSelectedFlag] = useState<FeatureFlag | null>(null);
  const [pilotUsers, setPilotUsers] = useState<PilotUser[]>([]);
  const [newPilotEmail, setNewPilotEmail] = useState("");
  const [stats, setStats] = useState<any>({});
  const [isLoadingStats, setIsLoadingStats] = useState(false);

  // Real-time feature flags subscription
  const { records: flags = [], isLoading: loading } = useRealtimeCollection(
    'feature_flags',
    async () => {
      try {
        const { data, error } = await supabase
          .from('feature_flags')
          .select('*')
          .order('name');

        if (error) {
          console.error('Error loading feature flags:', error);
          return [];
        }

        return data || [];
      } catch (error) {
        console.error('Error in feature flags fetch:', error);
        return [];
      }
    },
    'public'
  );

  // Load stats when flags change
  useEffect(() => {
    if (flags.length > 0) {
      loadStats();
    }
  }, [flags]);

  const loadStats = async () => {
    try {
      setIsLoadingStats(true);
      // Load stats for each flag
      const statsPromises = flags.map(flag => 
        featureFlagService.getFeatureUsageStats(flag.name)
      );
      
      const allStats = await Promise.all(statsPromises);
      const statsMap: any = {};
      flags.forEach((flag, index) => {
        statsMap[flag.name] = allStats[index];
      });
      setStats(statsMap);
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setIsLoadingStats(false);
    }
  };

  const loadPilotUsers = async (flag: FeatureFlag) => {
    try {
      const { data, error } = await supabase
        .from('auth.users')
        .select('id, email, raw_user_meta_data')
        .in('id', flag.pilot_user_ids);

      if (error) throw error;

      setPilotUsers(data?.map(user => ({
        id: user.id,
        email: user.email || '',
        full_name: user.raw_user_meta_data?.full_name
      })) || []);
    } catch (error) {
      console.error('Error loading pilot users:', error);
      toast.error("Failed to load pilot users");
    }
  };

  const togglePilot = async (flag: FeatureFlag) => {
    try {
      const { error } = await supabase
        .from('feature_flags')
        .update({ 
          pilot_enabled: !flag.pilot_enabled,
          updated_at: new Date().toISOString()
        })
        .eq('id', flag.id);

      if (error) throw error;

      toast.success(flag.pilot_enabled ? "Pilot disabled" : "Pilot enabled");
    } catch (error) {
      console.error('Error toggling pilot:', error);
      toast.error("Failed to update pilot status");
    }
  };

  const toggleGlobal = async (flag: FeatureFlag) => {
    try {
      const { error } = await supabase
        .from('feature_flags')
        .update({ 
          enabled: !flag.enabled,
          updated_at: new Date().toISOString()
        })
        .eq('id', flag.id);

      if (error) throw error;

      toast.success(flag.enabled ? "Feature disabled globally" : "Feature enabled globally");
    } catch (error) {
      console.error('Error toggling global status:', error);
      toast.error("Failed to update global status");
    }
  };

  const updateRolloutPercentage = async (flag: FeatureFlag, percentage: number) => {
    try {
      await featureFlagService.setRolloutPercentage(flag.name, percentage);
      toast.success(`Rollout percentage updated to ${percentage}%`);
    } catch (error) {
      console.error('Error updating rollout:', error);
      toast.error("Failed to update rollout percentage");
    }
  };

  const addPilotUser = async () => {
    if (!selectedFlag || !newPilotEmail) return;

    try {
      // Find user by email
      const { data: users, error: userError } = await supabase
        .from('auth.users')
        .select('id')
        .eq('email', newPilotEmail)
        .single();

      if (userError || !users) {
        toast.error("User not found");
        return;
      }

      await featureFlagService.addPilotUser(selectedFlag.name, users.id);
      toast.success("Pilot user added");
      setNewPilotEmail("");
      
      // Update selected flag from real-time data
      if (selectedFlag) {
        const updatedFlag = flags.find(f => f.id === selectedFlag.id);
        if (updatedFlag) {
          setSelectedFlag(updatedFlag);
          loadPilotUsers(updatedFlag);
        }
      }
    } catch (error) {
      console.error('Error adding pilot user:', error);
      toast.error("Failed to add pilot user");
    }
  };

  const removePilotUser = async (userId: string) => {
    if (!selectedFlag) return;

    try {
      await featureFlagService.removePilotUser(selectedFlag.name, userId);
      toast.success("Pilot user removed");
      
      // Update selected flag from real-time data
      if (selectedFlag) {
        const updatedFlag = flags.find(f => f.id === selectedFlag.id);
        if (updatedFlag) {
          setSelectedFlag(updatedFlag);
          loadPilotUsers(updatedFlag);
        }
      }
    } catch (error) {
      console.error('Error removing pilot user:', error);
      toast.error("Failed to remove pilot user");
    }
  };

  const getStatusBadge = (flag: FeatureFlag) => {
    if (flag.enabled) {
      return <Badge className="bg-green-500">Enabled</Badge>;
    }
    if (flag.pilot_enabled) {
      return <Badge className="bg-blue-500">Pilot</Badge>;
    }
    if (flag.rollout_percentage > 0) {
      return <Badge className="bg-yellow-500">{flag.rollout_percentage}% Rollout</Badge>;
    }
    return <Badge variant="secondary">Disabled</Badge>;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Flag className="h-6 w-6" />
          Feature Flags
        </h2>
        <div className="flex items-center gap-2">
          {loading && <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />}
          <span className="text-sm text-muted-foreground">Real-time updates</span>
        </div>
      </div>

      <div className="grid gap-4">
        {flags.map((flag) => (
          <Card key={flag.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{flag.name}</CardTitle>
                  <p className="text-sm text-muted-foreground">{flag.description}</p>
                </div>
                {getStatusBadge(flag)}
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="settings" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="settings">Settings</TabsTrigger>
                  <TabsTrigger value="users">Pilot Users</TabsTrigger>
                  <TabsTrigger value="stats">Stats</TabsTrigger>
                </TabsList>

                <TabsContent value="settings" className="space-y-4">
                  <div className="space-y-4">
                    {/* Global Enable */}
                    <div className="flex items-center justify-between">
                      <Label htmlFor={`global-${flag.id}`} className="flex items-center gap-2">
                        <Settings className="h-4 w-4" />
                        Enable Globally
                      </Label>
                      <Switch
                        id={`global-${flag.id}`}
                        checked={flag.enabled}
                        onCheckedChange={() => toggleGlobal(flag)}
                      />
                    </div>

                    {/* Pilot Enable */}
                    <div className="flex items-center justify-between">
                      <Label htmlFor={`pilot-${flag.id}`} className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Enable for Pilot Users
                      </Label>
                      <Switch
                        id={`pilot-${flag.id}`}
                        checked={flag.pilot_enabled}
                        onCheckedChange={() => togglePilot(flag)}
                        disabled={flag.enabled}
                      />
                    </div>

                    {/* Rollout Percentage */}
                    <div className="space-y-2">
                      <Label className="flex items-center gap-2">
                        <Percent className="h-4 w-4" />
                        Rollout Percentage: {flag.rollout_percentage}%
                      </Label>
                      <Slider
                        value={[flag.rollout_percentage]}
                        onValueChange={([value]) => updateRolloutPercentage(flag, value)}
                        max={100}
                        step={5}
                        disabled={flag.enabled || flag.pilot_enabled}
                        className="w-full"
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="users" className="space-y-4">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        {flag.pilot_user_ids.length} Pilot Users
                      </span>
                    </div>

                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            setSelectedFlag(flag);
                            loadPilotUsers(flag);
                          }}
                        >
                          <UserPlus className="h-4 w-4 mr-2" />
                          Manage Pilot Users
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Manage Pilot Users - {flag.name}</DialogTitle>
                          <DialogDescription>
                            Add or remove users from the pilot program for this feature
                          </DialogDescription>
                        </DialogHeader>

                        <div className="space-y-4">
                          {/* Add User */}
                          <div className="flex gap-2">
                            <Input
                              placeholder="Enter user email"
                              value={newPilotEmail}
                              onChange={(e) => setNewPilotEmail(e.target.value)}
                              onKeyPress={(e) => e.key === 'Enter' && addPilotUser()}
                            />
                            <Button onClick={addPilotUser}>
                              <Plus className="h-4 w-4 mr-2" />
                              Add
                            </Button>
                          </div>

                          {/* User List */}
                          <div className="space-y-2 max-h-[300px] overflow-y-auto">
                            {pilotUsers.map((user) => (
                              <div
                                key={user.id}
                                className="flex items-center justify-between p-2 rounded-lg border"
                              >
                                <div>
                                  <p className="font-medium">{user.email}</p>
                                  {user.full_name && (
                                    <p className="text-sm text-muted-foreground">
                                      {user.full_name}
                                    </p>
                                  )}
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removePilotUser(user.id)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </TabsContent>

                <TabsContent value="stats" className="space-y-4">
                  {stats[flag.name] ? (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Total Accesses</p>
                        <p className="text-2xl font-bold">{stats[flag.name].totalAccesses}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Unique Users</p>
                        <p className="text-2xl font-bold">{stats[flag.name].uniqueUsers}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Pilot Users</p>
                        <p className="text-2xl font-bold">{stats[flag.name].pilotUsers}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Access Rate</p>
                        <p className="text-2xl font-bold">
                          {Math.round(stats[flag.name].accessRate * 100)}%
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center p-8">
                      <BarChart3 className="h-12 w-12 text-muted-foreground" />
                      <p className="ml-2 text-muted-foreground">No usage data yet</p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
