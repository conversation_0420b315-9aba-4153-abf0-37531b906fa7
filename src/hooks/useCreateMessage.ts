import { useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { MessagingService } from "@/services";

interface CreateMessageData {
  sender_name: string;
  sender_email: string;
  sender_role?: string;
  sender_avatar?: string;
  content: string;
  status?: 'unread' | 'read' | 'archived';
  is_starred?: boolean;
  follow_up?: boolean;
  reminder?: boolean;
}

export const useCreateMessage = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (messageData: CreateMessageData) => {
      if (!user) {
        throw new Error('User must be authenticated to create a message');
      }

      return await MessagingService.createMessage({
        ...messageData,
        user_id: user.id
      });
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Message Created",
        description: "Your message has been successfully created.",
      });
    },
    onError: (error) => {
      console.error('Error creating message:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create message. Please try again.",
        variant: "destructive",
      });
    },
  });
};