import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface NotificationSettings {
  emailNotifications: boolean;
  applicationUpdates: boolean;
  marketing: boolean;
}

export const useNotificationSettings = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [settings, setSettings] = useState<NotificationSettings>({
    emailNotifications: true,
    applicationUpdates: true,
    marketing: false,
  });
  const [isLoading, setIsLoading] = useState(false);

  // Load settings from localStorage on mount
  useEffect(() => {
    const loadSettings = async () => {
      if (!user) return;
      
      try {
        // Try to load from Supabase first
        const { data, error } = await supabase
          .from('user_settings')
          .select('settings')
          .eq('user_id', user.id)
          .eq('settings_type', 'notifications')
          .maybeSingle();
          
        if (error) {
          // If no settings found in Supabase, try localStorage as fallback
          const savedSettings = localStorage.getItem('notificationSettings');
          if (savedSettings) {
            setSettings(JSON.parse(savedSettings));
          }
          return;
        }
        
        if (data?.settings) {
          setSettings(data.settings);
        }
      } catch (error) {
        console.error('Error loading notification settings:', error);
      }
    }
    
    loadSettings();
  }, [user]);

  const updateSetting = (key: keyof NotificationSettings, value: boolean) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    
    // Still update localStorage as a fallback
    try {
      localStorage.setItem('notificationSettings', JSON.stringify(newSettings));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  };

  const saveSettings = async () => {
    setIsLoading(true);
    try {
      if (!user) throw new Error('User not authenticated');
      
      // Upsert settings to Supabase
      const { error } = await supabase
        .from('user_settings')
        .upsert({
          user_id: user.id,
          settings_type: 'notifications',
          settings: settings,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id,settings_type'
        });
        
      if (error) throw error;
      
      // Also update localStorage as fallback
      localStorage.setItem('notificationSettings', JSON.stringify(settings));
      
      toast({
        title: "Settings Saved",
        description: "Your notification preferences have been updated.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save notification settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    settings,
    updateSetting,
    saveSettings,
    isLoading,
  };
};