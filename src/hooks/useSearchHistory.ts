import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useMutation } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

export interface SavedSearch {
  id: string;
  name: string;
  query: string;
  filters: Record<string, any>;
  category?: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

export interface CreateSearchData {
  name: string;
  query: string;
  filters: Record<string, any>;
  category?: string;
}

export const useSavedSearches = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  // Real-time saved searches subscription
  const { records: savedSearches = [], isLoading, setRecords } = useRealtimeCollection(
    'saved_searches',
    async () => {
      if (!user?.id) return [];

      try {
        const { data, error } = await supabase
          .from('saved_searches')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) throw error;
        return data || [];
      } catch (error) {
        console.error('Error fetching saved searches:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return { data: savedSearches, isLoading, error: null, setRecords };
};

export const useCreateSavedSearch = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (searchData: CreateSearchData) => {
      if (!user?.id) throw new Error("User not authenticated");

      const { data, error } = await supabase
        .from('saved_searches')
        .insert({
          ...searchData,
          user_id: user.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Search Saved",
        description: "You can access this search from your saved searches"
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to Save Search",
        description: "There was an error saving your search",
        variant: "destructive"
      });
    }
  });
};

export const useDeleteSavedSearch = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (searchId: string) => {
      const { error } = await supabase
        .from('saved_searches')
        .delete()
        .eq('id', searchId);

      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: "Search Deleted",
        description: "The saved search has been removed"
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to Delete Search",
        description: "There was an error deleting the search",
        variant: "destructive"
      });
    }
  });
}; 