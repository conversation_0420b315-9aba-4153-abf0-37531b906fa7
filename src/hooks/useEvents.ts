
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useAuth } from "@/contexts/AuthContext";
import { EventsService } from "@/services";

export interface Event {
  id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  location?: string;
  meeting_link?: string;
  event_type: 'meeting' | 'interview' | 'call' | 'presentation' | 'other';
  category: 'general' | 'recruitment' | 'client' | 'internal';
  priority: 'low' | 'medium' | 'high';
  created_at: string;
  updated_at: string;
}

export const useEvents = () => {
  const { user } = useAuth();

  // Real-time events subscription
  const { records: events = [], isLoading } = useRealtimeCollection(
    'events',
    async () => {
      if (!user) {
        console.log('No authenticated user, returning empty array');
        return [];
      }

      try {
        return await EventsService.getEvents(user.id);
      } catch (error) {
        console.error('Error in useEvents:', error);
        // Return empty array instead of throwing to prevent UI crashes
        return [];
      }
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return { data: events, isLoading, error: null };
};

// Export the useCreateEvent hook
export { useCreateEvent } from './useCreateEvent';
