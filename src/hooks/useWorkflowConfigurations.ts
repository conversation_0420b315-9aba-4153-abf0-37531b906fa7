import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";

export interface WorkflowConfiguration {
  id: string;
  name: string;
  description: string | null;
  config: any;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export const useWorkflowConfigurations = () => {
  const { user } = useAuth();

  // Real-time workflow configurations subscription
  const { records: configurations = [], isLoading } = useRealtimeCollection(
    'workflow_configurations',
    async () => {
      if (!user) {
        console.log('No authenticated user, returning empty array');
        return [];
      }

      try {
        const { data, error } = await supabase
          .from('workflow_configurations')
          .select('*')
          .eq('created_by', user.id)
          .eq('is_active', true)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching workflow configurations:', error);
          return [];
        }

        return data || [];
      } catch (error) {
        console.error('Error in useWorkflowConfigurations:', error);
        return [];
      }
    },
    'public',
    `created_by=eq.${user?.id}`
  );

  return { data: configurations, isLoading, error: null };
};

export const useCreateWorkflowConfiguration = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (workflowData: {
      name: string;
      description?: string;
      config: any;
      is_active?: boolean;
    }) => {
      if (!user) {
        throw new Error('User must be authenticated to create a workflow');
      }

      const { data, error } = await supabase
        .from('workflow_configurations')
        .insert({
          name: workflowData.name,
          description: workflowData.description || null,
          config: workflowData.config,
          is_active: workflowData.is_active !== undefined ? workflowData.is_active : true,
          created_by: user.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Workflow Created",
        description: "Your workflow has been successfully created.",
      });
    },
    onError: (error) => {
      console.error('Error creating workflow:', error);
      toast({
        title: "Error",
        description: "Failed to create workflow. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateWorkflowConfiguration = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (workflowData: {
      id: string;
      name?: string;
      description?: string;
      config?: any;
      is_active?: boolean;
    }) => {
      const { id, ...updateData } = workflowData;
      
      const { data, error } = await supabase
        .from('workflow_configurations')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Workflow Updated",
        description: "Your workflow has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error('Error updating workflow:', error);
      toast({
        title: "Error",
        description: "Failed to update workflow. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteWorkflowConfiguration = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      // Soft delete by setting is_active to false
      const { data, error } = await supabase
        .from('workflow_configurations')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Workflow Deleted",
        description: "Your workflow has been successfully deleted.",
      });
    },
    onError: (error) => {
      console.error('Error deleting workflow:', error);
      toast({
        title: "Error",
        description: "Failed to delete workflow. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useWorkflowConfiguration = (id: string) => {
  const { user } = useAuth();

  // Real-time individual workflow configuration subscription
  const { records: configurations = [], isLoading } = useRealtimeCollection(
    'workflow_configurations',
    async () => {
      if (!id || !user) return [];

      try {
        const { data, error } = await supabase
          .from('workflow_configurations')
          .select('*')
          .eq('id', id)
          .eq('created_by', user.id)
          .eq('is_active', true)
          .single();

        if (error) {
          console.error('Error fetching workflow configuration:', error);
          return [];
        }

        return data ? [data] : [];
      } catch (error) {
        console.error('Error in useWorkflowConfiguration:', error);
        return [];
      }
    },
    'public',
    `id=eq.${id}`
  );

  return { data: configurations[0] || null, isLoading, error: null };
};