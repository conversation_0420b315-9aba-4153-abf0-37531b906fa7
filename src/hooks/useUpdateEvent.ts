import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Event } from "./useEvents";

interface UpdateEventData {
  id: string;
  title?: string;
  description?: string;
  start_time?: string;
  end_time?: string;
  location?: string;
  meeting_link?: string;
  event_type?: 'meeting' | 'interview' | 'call' | 'presentation' | 'other';
  priority?: 'low' | 'medium' | 'high';
  category?: 'general' | 'recruitment' | 'client' | 'internal';
}

export const useUpdateEvent = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (eventData: UpdateEventData) => {
      const { id, ...updateData } = eventData;
      
      // Validate event data
      if (updateData.title === '') {
        throw new Error('Event title cannot be empty');
      }
      
      if (updateData.start_time && updateData.end_time) {
        // Ensure end time is after start time
        if (new Date(updateData.end_time) <= new Date(updateData.start_time)) {
          throw new Error('End time must be after start time');
        }
      }

      const { data, error } = await supabase
        .from('events')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Event Updated",
        description: "Your event has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error('Error updating event:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update event. Please try again.",
        variant: "destructive",
      });
    },
  });
};