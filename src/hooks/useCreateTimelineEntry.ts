import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";

interface CreateTimelineEntryData {
  candidate_id: string;
  event_type: string;
  title: string;
  description: string;
  event_date?: string;
  status?: string;
}

export const useCreateTimelineEntry = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (entryData: CreateTimelineEntryData) => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('candidate_timeline')
        .insert({
          ...entryData,
          user_id: user.id,
          event_date: entryData.event_date ? new Date(entryData.event_date).toISOString() : new Date().toISOString(),
          status: entryData.status || 'completed',
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      
      // Success handled by real-time subscription
    },
    onError: (error) => {
      console.error('Error creating timeline entry:', error);
      toast({
        title: "Error",
        description: "Failed to update timeline. Please try again.",
        variant: "destructive"
      });
    },
  });
};