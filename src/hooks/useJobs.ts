
import { useAuth } from "@/contexts/AuthContext";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";

export interface Job {
  id: string;
  title: string;
  department: string;
  location: string;
  job_type: string;
  salary_range?: string;
  experience_required?: string;
  description: string;
  requirements?: string[];
  benefits?: string[];
  is_urgent: boolean;
  is_active: boolean;
  applicant_count: number;
  created_at: string;
  updated_at: string;
  user_id: string;
}

export const useJobs = () => {
  const { user } = useAuth();

  // Real-time jobs subscription
  const { records: jobs = [], isLoading } = useRealtimeCollection(
    'jobs',
    async () => {
      if (!user) {
        console.log('No authenticated user, returning empty array');
        return [];
      }

      const { data, error } = await supabase
        .from('jobs')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching jobs:', error);
        throw error;
      }

      return data || [];
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return { data: jobs, isLoading, error: null };
};
