import { useMutation } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { 
  ReportsService, 
  ReportTemplate, 
  ScheduledReport, 
  GeneratedReport,
  CreateReportTemplateData,
  CreateScheduledReportData,
  UpdateReportTemplateData,
  UpdateScheduledReportData,
  ReportParameters
} from "@/services/ReportsService";

export const useReportTemplates = () => {
  const { user } = useAuth();

  // Real-time report templates subscription
  const { records: templates = [], isLoading } = useRealtimeCollection(
    'report_templates',
    async (): Promise<ReportTemplate[]> => {
      if (!user) {
        console.log('No authenticated user, returning empty array');
        return [];
      }

      try {
        return await ReportsService.getReportTemplates(user.id);
      } catch (error) {
        console.error('Error in useReportTemplates:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return { data: templates, isLoading, error: null };
};

export const useReportTemplate = (templateId: string) => {
  const { user } = useAuth();

  // Real-time individual report template subscription
  const { records: templates = [], isLoading } = useRealtimeCollection(
    'report_templates',
    async (): Promise<ReportTemplate[]> => {
      if (!templateId || !user) return [];
      
      try {
        const template = await ReportsService.getReportTemplate(templateId);
        return template ? [template] : [];
      } catch (error) {
        console.error('Error in useReportTemplate:', error);
        return [];
      }
    },
    'public',
    `id=eq.${templateId}`
  );

  return { data: templates[0] || null, isLoading, error: null };
};

export const useCreateReportTemplate = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (templateData: CreateReportTemplateData) => {
      return await ReportsService.createReportTemplate(templateData);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Report Template Created",
        description: "Your report template has been successfully created.",
      });
    },
    onError: (error) => {
      console.error('Error creating report template:', error);
      toast({
        title: "Error",
        description: "Failed to create report template. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateReportTemplate = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ templateId, updateData }: { templateId: string; updateData: UpdateReportTemplateData }) => {
      return await ReportsService.updateReportTemplate(templateId, updateData);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Report Template Updated",
        description: "Your report template has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error('Error updating report template:', error);
      toast({
        title: "Error",
        description: "Failed to update report template. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteReportTemplate = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (templateId: string) => {
      return await ReportsService.deleteReportTemplate(templateId);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Report Template Deleted",
        description: "Your report template has been successfully deleted.",
      });
    },
    onError: (error) => {
      console.error('Error deleting report template:', error);
      toast({
        title: "Error",
        description: "Failed to delete report template. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useGenerateReport = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      templateId, 
      parameters, 
      format 
    }: { 
      templateId: string; 
      parameters?: ReportParameters; 
      format?: string; 
    }) => {
      return await ReportsService.generateReport(templateId, parameters, format);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Report Generated",
        description: "Your report has been successfully generated and will be available shortly.",
      });
    },
    onError: (error) => {
      console.error('Error generating report:', error);
      toast({
        title: "Error",
        description: "Failed to generate report. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useScheduledReports = () => {
  const { user } = useAuth();

  // Real-time scheduled reports subscription
  const { records: reports = [], isLoading } = useRealtimeCollection(
    'scheduled_reports',
    async (): Promise<ScheduledReport[]> => {
      if (!user) {
        console.log('No authenticated user, returning empty array');
        return [];
      }

      try {
        return await ReportsService.getScheduledReports(user.id);
      } catch (error) {
        console.error('Error in useScheduledReports:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return { data: reports, isLoading, error: null };
};

export const useCreateScheduledReport = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (scheduleData: CreateScheduledReportData) => {
      return await ReportsService.createScheduledReport(scheduleData);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Report Scheduled",
        description: "Your report has been successfully scheduled.",
      });
    },
    onError: (error) => {
      console.error('Error creating scheduled report:', error);
      toast({
        title: "Error",
        description: "Failed to schedule report. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateScheduledReport = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ scheduleId, updateData }: { scheduleId: string; updateData: UpdateScheduledReportData }) => {
      return await ReportsService.updateScheduledReport(scheduleId, updateData);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Scheduled Report Updated",
        description: "Your scheduled report has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error('Error updating scheduled report:', error);
      toast({
        title: "Error",
        description: "Failed to update scheduled report. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteScheduledReport = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (scheduleId: string) => {
      return await ReportsService.deleteScheduledReport(scheduleId);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Scheduled Report Deleted",
        description: "Your scheduled report has been successfully deleted.",
      });
    },
    onError: (error) => {
      console.error('Error deleting scheduled report:', error);
      toast({
        title: "Error",
        description: "Failed to delete scheduled report. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useGeneratedReports = (limit?: number) => {
  const { user } = useAuth();

  // Real-time generated reports subscription
  const { records: allReports = [], isLoading } = useRealtimeCollection(
    'generated_reports',
    async (): Promise<GeneratedReport[]> => {
      if (!user) {
        console.log('No authenticated user, returning empty array');
        return [];
      }

      try {
        return await ReportsService.getGeneratedReports(user.id, limit);
      } catch (error) {
        console.error('Error in useGeneratedReports:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  // Apply limit if specified
  const reports = limit ? allReports.slice(0, limit) : allReports;

  return { data: reports, isLoading, error: null };
};

export const useGeneratedReport = (reportId: string) => {
  const { user } = useAuth();

  // Real-time individual generated report subscription
  const { records: reports = [], isLoading } = useRealtimeCollection(
    'generated_reports',
    async (): Promise<GeneratedReport[]> => {
      if (!reportId || !user) return [];
      
      try {
        const report = await ReportsService.getGeneratedReport(reportId);
        return report ? [report] : [];
      } catch (error) {
        console.error('Error in useGeneratedReport:', error);
        return [];
      }
    },
    'public',
    `id=eq.${reportId}`
  );

  return { data: reports[0] || null, isLoading, error: null };
};

export const useGetReportAccessUrl = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (reportId: string) => {
      // Track the access
      await ReportsService.trackReportAccess(reportId);
      // Get the URL
      return await ReportsService.getReportAccessUrl(reportId);
    },
    onSuccess: (url) => {
      // Open the report in a new tab
      window.open(url, '_blank');
      toast({
        title: "Opening Report",
        description: "Your report is opening in a new tab.",
      });
    },
    onError: (error) => {
      console.error('Error accessing report:', error);
      toast({
        title: "Error",
        description: "Failed to access report. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useScheduleReportWithFunction = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      name, 
      templateId, 
      cronExpression, 
      parameters, 
      recipients, 
      outputFormat 
    }: { 
      name: string; 
      templateId: string; 
      cronExpression: string; 
      parameters?: ReportParameters; 
      recipients?: Array<{ email: string; name?: string }>; 
      outputFormat?: string; 
    }) => {
      return await ReportsService.scheduleReportWithFunction(
        name, 
        templateId, 
        cronExpression, 
        parameters, 
        recipients, 
        outputFormat
      );
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Report Scheduled",
        description: "Your report has been successfully scheduled using the advanced scheduler.",
      });
    },
    onError: (error) => {
      console.error('Error scheduling report with function:', error);
      toast({
        title: "Error",
        description: "Failed to schedule report. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useCreatePredefinedTemplates = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async () => {
      if (!user) throw new Error('User not authenticated');
      return await ReportsService.createPredefinedTemplates(user.id);
    },
    onSuccess: (templates) => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Templates Created",
        description: `${templates.length} predefined report templates have been created successfully.`,
      });
    },
    onError: (error) => {
      console.error('Error creating predefined templates:', error);
      toast({
        title: "Error",
        description: "Failed to create predefined templates. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const usePredefinedTemplates = () => {
  return {
    templates: ReportsService.getPredefinedTemplates(),
    createAll: useCreatePredefinedTemplates()
  };
};

export const useDueScheduledReports = () => {
  // Real-time due scheduled reports subscription
  const { records: dueReports = [], isLoading } = useRealtimeCollection(
    'due_scheduled_reports',
    async () => {
      try {
        return await ReportsService.getDueScheduledReports();
      } catch (error) {
        console.error('Error in useDueScheduledReports:', error);
        return [];
      }
    },
    'public'
  );
  return { data: dueReports, isLoading, error: null };
};

export const useMyReportHistory = () => {
  const { user } = useAuth();
  // Real-time my report history subscription
  const { records: history = [], isLoading } = useRealtimeCollection(
    'my_report_history',
    async () => {
      if (!user) return [];
      try {
        return await ReportsService.getMyReportHistory(user.id);
      } catch (error) {
        console.error('Error in useMyReportHistory:', error);
        return [];
      }
    },
    'public',
    `generated_by=eq.${user?.id}`
  );
  return { data: history, isLoading, error: null };
};

export const useReportGenerationQueue = () => {
  // Real-time report generation queue subscription
  const { records: queue = [], isLoading } = useRealtimeCollection(
    'report_generation_queue',
    async () => {
      try {
        return await ReportsService.getReportGenerationQueue();
      } catch (error) {
        console.error('Error in useReportGenerationQueue:', error);
        return [];
      }
    },
    'public'
  );
  return { data: queue, isLoading, error: null };
};