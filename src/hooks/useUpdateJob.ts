
import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Tables } from '@/types/database.types';

export const useUpdateJob = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (updatedJob: Partial<Tables<'jobs'>> & { id: string }) => {
      const { data, error } = await supabase
        .from('jobs')
        .update({
          ...updatedJob,
          updated_at: new Date().toISOString(),
        })
        .eq('id', updatedJob.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Job Updated",
        description: "The job posting has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error('Error updating job:', error);
      toast({
        title: "Error",
        description: "Failed to update job. Please try again.",
        variant: "destructive",
      });
    },
  });
};
