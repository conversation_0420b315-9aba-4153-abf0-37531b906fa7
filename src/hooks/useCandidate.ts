
import { useRealtimeRecord } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { CandidateType } from "@/types/candidate";
import { useAuth } from "@/contexts/AuthContext";

export const useCandidate = (id: string) => {
  const { user } = useAuth();

  // Real-time individual candidate subscription
  const { record: candidateData, isLoading } = useRealtimeRecord(
    'candidates',
    id,
    'public'
  );

  // Transform the database data to match the CandidateType interface
  const transformedCandidate: CandidateType | null = candidateData ? {
    id: candidateData.id,
    name: candidateData.name,
    role: candidateData.role,
    email: candidateData.email,
    phone: candidateData.phone || '',
    location: candidateData.location || '',
    avatar: candidateData.avatar || '/placeholder.svg',
    recruiter: {
      id: candidateData.recruiter_id || '',
      name: candidateData.recruiter_name || '',
      avatar: candidateData.recruiter_avatar || '/placeholder.svg'
    },
    tags: candidateData.tags || [],
    socialLinks: {
      github: candidateData.github_url || '',
      linkedin: candidateData.linkedin_url || '',
      twitter: candidateData.twitter_url || ''
    },
    relationshipScore: candidateData.relationship_score || 0,
    experience: candidateData.experience || '',
    industry: candidateData.industry || '',
    remotePreference: candidateData.remote_preference || '',
    visaStatus: candidateData.visa_status || '',
    skills: Array.isArray(candidateData.skills) ? candidateData.skills as { name: string; level: string; years: number; }[] : [],
    aiSummary: candidateData.ai_summary || '',
    matchedJobs: [], // This would be populated by a separate query
    createdAt: candidateData.created_at,
    updatedAt: candidateData.updated_at
  } : null;

  return {
    data: transformedCandidate,
    isLoading,
    error: null
  };
};
