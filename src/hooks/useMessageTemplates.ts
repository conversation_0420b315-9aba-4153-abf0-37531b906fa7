
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";

export interface MessageTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  template_category: 'general' | 'interview' | 'follow_up' | 'rejection' | 'offer';
  created_at: string;
  updated_at: string;
}

export const useMessageTemplates = () => {
  const { user } = useAuth();

  // Real-time message templates subscription
  const { records: templates = [], isLoading } = useRealtimeCollection(
    'message_templates',
    async () => {
      if (!user) {
        console.log('No authenticated user, returning empty array');
        return [];
      }

      try {
        const { data, error } = await supabase
          .from('message_templates')
          .select('*')
          .eq('user_id', user.id)
          .order('name');

        if (error) {
          console.error('Error fetching message templates:', error);
          
          // If table doesn't exist or other error, create sample templates
          if (error.code === '42P01') { // PostgreSQL code for undefined_table
            return createSampleTemplates(user.id);
          }
          
          return [];
        }
        
        // If no templates exist, create sample templates
        if (!data || data.length === 0) {
          return createSampleTemplates(user.id);
        }
        
        return data;
      } catch (error) {
        console.error('Error in useMessageTemplates:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return { data: templates, isLoading, error: null };
};

// Helper function to create sample message templates
async function createSampleTemplates(userId: string): Promise<MessageTemplate[]> {
  const sampleTemplates = [
    {
      name: "Interview Invitation",
      subject: "Interview Invitation for [Position]",
      content: "Dear [Name],\n\nThank you for your application for the [Position] role at [Company]. We were impressed with your background and would like to invite you for an interview.\n\nPlease let me know your availability for next week.\n\nBest regards,\n[Your Name]",
      template_category: "interview",
      user_id: userId
    },
    {
      name: "Follow-up After Interview",
      subject: "Thank You for Your Interview",
      content: "Dear [Name],\n\nThank you for taking the time to interview for the [Position] position. It was a pleasure speaking with you and learning more about your skills and experience.\n\nWe are currently reviewing all candidates and will be in touch soon with next steps.\n\nBest regards,\n[Your Name]",
      template_category: "follow_up",
      user_id: userId
    },
    {
      name: "Job Offer",
      subject: "Job Offer - [Position] at [Company]",
      content: "Dear [Name],\n\nI am pleased to offer you the position of [Position] at [Company]. Based on your experience and skills, we believe you would be a valuable addition to our team.\n\nYour starting salary will be [Salary] per year, and you will be eligible for our benefits package including [Benefits].\n\nPlease review the attached offer letter for complete details.\n\nBest regards,\n[Your Name]",
      template_category: "offer",
      user_id: userId
    }
  ];
  
  try {
    const { data, error } = await supabase
      .from('message_templates')
      .insert(sampleTemplates)
      .select();
      
    if (error) {
      console.error('Error creating sample templates:', error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Error creating sample templates:', error);
    return [];
  }
}