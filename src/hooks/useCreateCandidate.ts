

import { useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { CandidatesService } from "@/services";

interface CreateCandidateData {
  name: string;
  role: string;
  email: string;
  phone?: string;
  location?: string;
  tags?: string[];
  experience?: string;
  industry?: string;
  remote_preference?: string;
  visa_status?: string;
  linkedin_url?: string;
  github_url?: string;
  skills?: { name: string; level: string; years: number; }[];
  ai_summary?: string;
}

export const useCreateCandidate = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (candidateData: CreateCandidateData) => {
      console.log('🔍 Starting candidate creation process...');
      console.log('🔍 Current user:', user?.id);
      console.log('🔍 Raw candidate data received:', candidateData);
      
      if (!user) {
        console.error('❌ No authenticated user found');
        throw new Error('User must be authenticated to create a candidate');
      }

      // Validate required fields
      if (!candidateData.name || !candidateData.email || !candidateData.role) {
        console.error('❌ Missing required fields:', {
          name: candidateData.name,
          email: candidateData.email,
          role: candidateData.role
        });
        throw new Error('Name, email, and role are required');
      }

      const candidate = await CandidatesService.createCandidate({
        ...candidateData,
        userId: user.id
      });
      
      console.log('✅ Candidate successfully created:', candidate);
      return candidate;
    },
    onSuccess: (data) => {
      console.log('Mutation successful, real-time subscription will update UI automatically');
      
      toast({
        title: "Candidate Added",
        description: "New candidate has been successfully added to your talent pool.",
      });
    },
    onError: (error) => {
      console.error('Error creating candidate:', error);
      toast({
        title: "Error",
        description: `Failed to add candidate: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    },
  });
};

