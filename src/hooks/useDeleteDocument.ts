import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useCreateTimelineEntry } from "@/hooks/useCreateTimelineEntry";

export const useDeleteDocument = (candidateId: string) => {
  const { toast } = useToast();
  const createTimelineEntry = useCreateTimelineEntry();

  return useMutation({
    mutationFn: async (documentId: string) => {
      // Get document details before deletion
      const { data: document, error: fetchError } = await supabase
        .from('candidate_documents')
        .select('*')
        .eq('id', documentId)
        .single();
        
      if (fetchError) throw fetchError;
      
      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('candidate-documents')
        .remove([`${candidateId}/${document.name}`]);

      if (storageError) throw storageError;

      // Delete from database
      const { error: dbError } = await supabase
        .from('candidate_documents')
        .delete()
        .eq('id', documentId);

      if (dbError) throw dbError;
      
      return document;
    },
    onSuccess: (document) => {
      // Real-time subscription will automatically update the UI
      
      // Create timeline entry for document deletion
      createTimelineEntry.mutate({
        candidate_id: candidateId,
        event_type: 'document',
        title: `Document Deleted: ${document.name}`,
        description: `Document "${document.name}" was deleted`,
        status: 'completed',
      });
      
      toast({
        title: "Document deleted",
        description: "The document has been successfully deleted."
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete document: " + error.message,
        variant: "destructive"
      });
    }
  });
};