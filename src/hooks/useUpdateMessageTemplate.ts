import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface UpdateMessageTemplateData {
  id: string;
  name?: string;
  subject?: string;
  content?: string;
  template_category?: 'general' | 'interview' | 'follow_up' | 'rejection' | 'offer';
}

export const useUpdateMessageTemplate = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (templateData: UpdateMessageTemplateData) => {
      const { id, ...updateData } = templateData;
      
      // Validate template data
      if (updateData.name === '') {
        throw new Error('Template name cannot be empty');
      }
      
      if (updateData.subject === '') {
        throw new Error('Subject cannot be empty');
      }
      
      if (updateData.content === '') {
        throw new Error('Content cannot be empty');
      }

      const { data, error } = await supabase
        .from('message_templates')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Template Updated",
        description: "Your message template has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error('Error updating message template:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update template. Please try again.",
        variant: "destructive",
      });
    },
  });
};