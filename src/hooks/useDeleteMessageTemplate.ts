import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export const useDeleteMessageTemplate = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (templateId: string) => {
      if (!templateId) {
        throw new Error('Template ID is required');
      }
      
      const { error } = await supabase
        .from('message_templates')
        .delete()
        .eq('id', templateId);

      if (error) throw error;
      return templateId;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Template Deleted",
        description: "Your message template has been successfully deleted.",
      });
    },
    onError: (error) => {
      console.error('Error deleting message template:', error);
      toast({
        title: "Error",
        description: "Failed to delete template. Please try again.",
        variant: "destructive",
      });
    },
  });
};