import { useMutation, useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { JobIntegrationsService, JobBoardIntegration, SyncResult } from '@/services/JobIntegrationsService';
import { useRealtimeCollection } from './useRealtimeSubscription';
import { supabase } from '@/integrations/supabase/client';

/**
 * Hook for managing job board integrations
 */
export const useJobIntegrations = () => {
  const { user } = useAuth();

  // Real-time job integrations subscription
  const { records: integrations = [], isLoading } = useRealtimeCollection(
    'integrations',
    async () => {
      if (!user) return [];
      return await JobIntegrationsService.getJobIntegrations(user.id);
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return {
    data: integrations,
    isLoading,
    error: null
  };
};

/**
 * Hook for connecting to a job board platform
 */
export const useConnectPlatform = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ platform, settings }: { platform: string; settings: Record<string, any> }) => {
      if (!user) throw new Error('User must be authenticated');
      return await JobIntegrationsService.connectPlatform(user.id, platform, settings);
    },
    onSuccess: (_, { platform }) => {
      toast({
        title: "Platform Connected",
        description: `Successfully connected to ${platform}`,
      });
    },
    onError: (error, { platform }) => {
      console.error('Error connecting platform:', error);
      toast({
        title: "Connection Failed",
        description: `Failed to connect to ${platform}. Please try again.`,
        variant: "destructive",
      });
    },
  });
};

/**
 * Hook for disconnecting from a platform
 */
export const useDisconnectPlatform = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (platform: string) => {
      if (!user) throw new Error('User must be authenticated');
      return await JobIntegrationsService.disconnectPlatform(user.id, platform);
    },
    onSuccess: (_, platform) => {
      toast({
        title: "Platform Disconnected",
        description: `Successfully disconnected from ${platform}`,
      });
    },
    onError: (error, platform) => {
      console.error('Error disconnecting platform:', error);
      toast({
        title: "Disconnection Failed",
        description: `Failed to disconnect from ${platform}. Please try again.`,
        variant: "destructive",
      });
    },
  });
};

/**
 * Hook for syncing jobs with platforms
 */
export const useSyncJobs = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (platforms?: string[]) => {
      if (!user) throw new Error('User must be authenticated');
      return await JobIntegrationsService.syncJobs(user.id, platforms);
    },
    onSuccess: (results: SyncResult[]) => {
      const totalPosted = results.reduce((sum, r) => sum + r.jobsPosted, 0);
      const totalUpdated = results.reduce((sum, r) => sum + r.jobsUpdated, 0);
      const totalErrors = results.reduce((sum, r) => sum + r.errors.length, 0);

      if (totalErrors === 0) {
        toast({
          title: "Sync Complete",
          description: `Successfully posted ${totalPosted} jobs and updated ${totalUpdated} jobs across all platforms.`,
        });
      } else {
        toast({
          title: "Sync Completed with Errors",
          description: `Posted ${totalPosted} jobs, updated ${totalUpdated} jobs, but encountered ${totalErrors} errors.`,
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      console.error('Error syncing jobs:', error);
      toast({
        title: "Sync Failed",
        description: "Failed to sync jobs with platforms. Please try again.",
        variant: "destructive",
      });
    },
  });
};

/**
 * Hook for getting sync history
 */
export const useSyncHistory = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['syncHistory', user?.id],
    queryFn: async () => {
      if (!user) return [];
      return await JobIntegrationsService.getSyncHistory(user.id);
    },
    enabled: !!user,
  });
};
