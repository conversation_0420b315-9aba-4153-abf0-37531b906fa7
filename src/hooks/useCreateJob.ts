
import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";

interface CreateJobData {
  title: string;
  department: string;
  location: string;
  job_type: string;
  salary_range?: string;
  experience_required?: string;
  description: string;
  requirements?: string[];
  benefits?: string[];
  is_urgent: boolean;
}

export const useCreateJob = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (jobData: CreateJobData) => {
      if (!user) {
        throw new Error('User must be authenticated to create a job');
      }

      const { data, error } = await supabase
        .from('jobs')
        .insert({
          ...jobData,
          user_id: user.id,
          is_active: true,
          applicant_count: 0
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Job Created",
        description: "Your job posting has been successfully created.",
      });
    },
    onError: (error) => {
      console.error('Error creating job:', error);
      toast({
        title: "Error",
        description: "Failed to create job posting. Please try again.",
        variant: "destructive",
      });
    },
  });
};
