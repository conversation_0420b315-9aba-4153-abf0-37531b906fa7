import { useMutation } from "@tanstack/react-query";
import { NotificationsService, NotificationData, CreateNotificationData, UpdateNotificationData } from "@/services/NotificationsService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useMemo } from "react";

export const useNotifications = (limit = 50) => {
  const { user } = useAuth();
  
  // Real-time notifications subscription
  const { records: allNotifications = [], isLoading } = useRealtimeCollection(
    'notifications',
    async () => {
      if (!user?.id) return [];
      
      try {
        // Try to get existing notifications, or initialize with defaults
        const existingNotifications = await NotificationsService.getNotifications(user.id, limit);
        if (existingNotifications.length > 0) {
          return existingNotifications;
        }
        
        // Initialize with default notifications if none exist
        return NotificationsService.initializeDefaultNotifications(user.id);
      } catch (error) {
        console.error('Error fetching notifications:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  // Apply limit to notifications (since real-time gives us all records)
  const notifications = useMemo(() => {
    return allNotifications.slice(0, limit);
  }, [allNotifications, limit]);

  return { data: notifications, isLoading, error: null };
};

export const useUnreadNotifications = () => {
  const { user } = useAuth();
  
  // Real-time unread notifications subscription
  const { records: unreadNotifications = [], isLoading } = useRealtimeCollection(
    'notifications',
    async () => {
      if (!user?.id) throw new Error("User not authenticated");
      return NotificationsService.getUnreadNotifications(user.id);
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return { data: unreadNotifications, isLoading, error: null };
};

export const useUnreadCount = () => {
  const { user } = useAuth();
  
  // Use real-time notifications to compute unread count
  const { records: notifications = [], isLoading } = useRealtimeCollection(
    'notifications',
    async () => {
      if (!user?.id) return [];
      
      try {
        const { data, error } = await supabase
          .from('notifications')
          .select('*')
          .eq('user_id', user.id)
          .eq('read', false);

        if (error) {
          console.error('Error fetching unread notifications:', error);
          return [];
        }

        return data || [];
      } catch (error) {
        console.error('Error fetching unread notifications:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  // Compute unread count from real-time notifications
  const unreadCount = useMemo(() => {
    return notifications.filter(notification => !notification.read).length;
  }, [notifications]);

  return { data: unreadCount, isLoading, error: null };
};

export const useCreateNotification = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: Omit<CreateNotificationData, 'user_id'>) => {
      if (!user?.id) throw new Error("User not authenticated");
      return NotificationsService.createNotification({
        ...data,
        user_id: user.id,
      });
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Notification Created",
        description: "New notification created successfully.",
      });
    },
    onError: (error) => {
      console.error("Error creating notification:", error);
      toast({
        title: "Error",
        description: "Failed to create notification.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateNotification = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: UpdateNotificationData) => {
      return NotificationsService.updateNotification(data);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
    },
    onError: (error) => {
      console.error("Error updating notification:", error);
      toast({
        title: "Error",
        description: "Failed to update notification.",
        variant: "destructive",
      });
    },
  });
};

export const useMarkAsRead = () => {
  return useMutation({
    mutationFn: (notificationId: string) => {
      return NotificationsService.markAsRead(notificationId);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
    },
    onError: (error) => {
      console.error("Error marking notification as read:", error);
    },
  });
};

export const useMarkAllAsRead = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: () => {
      if (!user?.id) throw new Error("User not authenticated");
      return NotificationsService.markAllAsRead(user.id);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "All Notifications Read",
        description: "All notifications marked as read.",
      });
    },
    onError: (error) => {
      console.error("Error marking all notifications as read:", error);
      toast({
        title: "Error",
        description: "Failed to mark all notifications as read.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteNotification = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => {
      return NotificationsService.deleteNotification(id);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Notification Deleted",
        description: "Notification deleted successfully.",
      });
    },
    onError: (error) => {
      console.error("Error deleting notification:", error);
      toast({
        title: "Error",
        description: "Failed to delete notification.",
        variant: "destructive",
      });
    },
  });
};

export const useNotificationsByType = (type: string) => {
  const { user } = useAuth();
  
  // Real-time notifications by type subscription
  const { records: notificationsByType = [], isLoading } = useRealtimeCollection(
    'notifications',
    async () => {
      if (!user?.id || !type) return [];
      
      try {
        return await NotificationsService.getNotificationsByType(user.id, type);
      } catch (error) {
        console.error('Error in useNotificationsByType:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return { data: notificationsByType, isLoading, error: null };
}; 