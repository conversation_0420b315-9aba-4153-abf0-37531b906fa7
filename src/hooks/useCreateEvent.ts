
import { useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { EventsService } from "@/services";

interface CreateEventData {
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  location?: string;
  meeting_link?: string;
  event_type: 'meeting' | 'interview' | 'call' | 'presentation' | 'other';
  category: 'general' | 'recruitment' | 'client' | 'internal';
  priority: 'low' | 'medium' | 'high';
}

export const useCreateEvent = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (eventData: CreateEventData) => {
      if (!user) {
        throw new Error('User must be authenticated to create an event');
      }

      return await EventsService.createEvent({
        ...eventData,
        user_id: user.id
      });
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Event Created",
        description: "Your event has been successfully created.",
      });
    },
    onError: (error) => {
      console.error('Error creating event:', error);
      toast({
        title: "Error",
        description: "Failed to create event. Please try again.",
        variant: "destructive",
      });
    },
  });
};
