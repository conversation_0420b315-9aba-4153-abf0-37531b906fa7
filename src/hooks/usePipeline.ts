import React from 'react';
import { useMutation } from '@tanstack/react-query';
import { useRealtimeCollection } from '@/hooks/useRealtimeSubscription';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { 
  PipelineService, 
  type PipelineStageData, 
  type CreatePipelineStageData,
  type UpdatePipelineStageData,
  type PipelineCandidateData,
  type CreatePipelineCandidateData,
  type UpdatePipelineCandidateData
} from '@/services/PipelineService';

// Pipeline Stages Hooks
export const usePipelineStages = (userId?: string) => {
  const { user } = useAuth();
  const targetUserId = userId || user?.id;

  // Real-time pipeline stages subscription
  const { records: stages = [], isLoading } = useRealtimeCollection(
    'pipeline_stages',
    async () => {
      if (!targetUserId) {
        console.log('No user ID provided, returning empty array');
        return [];
      }

      try {
        return await PipelineService.getPipelineStages(targetUserId);
      } catch (error) {
        console.error('Error in usePipelineStages:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${targetUserId}`
  );

  return { data: stages, isLoading, error: null };
};

export const useCreatePipelineStage = () => {
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: (stageData: CreatePipelineStageData) => 
      PipelineService.createPipelineStage(stageData),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Pipeline Stage Created",
        description: "Your pipeline stage has been successfully created.",
      });
    },
    onError: (error) => {
      console.error('Error creating pipeline stage:', error);
      toast({
        title: "Error",
        description: "Failed to create pipeline stage. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdatePipelineStage = () => {
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: (updateData: UpdatePipelineStageData) => 
      PipelineService.updatePipelineStage(updateData),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Pipeline Stage Updated",
        description: "Your pipeline stage has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error('Error updating pipeline stage:', error);
      toast({
        title: "Error",
        description: "Failed to update pipeline stage. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeletePipelineStage = () => {
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: (id: string) => PipelineService.deletePipelineStage(id),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Pipeline Stage Deleted",
        description: "Your pipeline stage has been successfully deleted.",
      });
    },
    onError: (error) => {
      console.error('Error deleting pipeline stage:', error);
      toast({
        title: "Error",
        description: "Failed to delete pipeline stage. Please try again.",
        variant: "destructive",
      });
    },
  });
};

// Pipeline Candidates Hooks
export const usePipelineCandidates = (userId?: string) => {
  const { user } = useAuth();
  const targetUserId = userId || user?.id;

  // Real-time pipeline candidates subscription
  const { records: candidates = [], isLoading } = useRealtimeCollection(
    'pipeline_candidates',
    async () => {
      if (!targetUserId) {
        console.log('No user ID provided, returning empty array');
        return [];
      }

      try {
        return await PipelineService.getPipelineCandidates(targetUserId);
      } catch (error) {
        console.error('Error in usePipelineCandidates:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${targetUserId}`
  );

  return { data: candidates, isLoading, error: null };
};

export const useCandidatesByStage = (userId?: string, stage?: string) => {
  const { user } = useAuth();
  const targetUserId = userId || user?.id;

  // Real-time candidates by stage subscription
  const { records: allCandidates = [], isLoading } = useRealtimeCollection(
    'pipeline_candidates',
    async () => {
      if (!targetUserId || !stage) {
        console.log('No user ID or stage provided, returning empty array');
        return [];
      }

      try {
        return await PipelineService.getCandidatesByStage(targetUserId, stage);
      } catch (error) {
        console.error('Error in useCandidatesByStage:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${targetUserId}`
  );

  // Filter by stage in real-time
  const candidatesByStage = React.useMemo(() => {
    if (!stage) return allCandidates;
    return allCandidates.filter(candidate => candidate.stage === stage);
  }, [allCandidates, stage]);

  return { data: candidatesByStage, isLoading, error: null };
};

export const useCreatePipelineCandidate = () => {
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: (candidateData: CreatePipelineCandidateData) => 
      PipelineService.createPipelineCandidate(candidateData),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Pipeline Candidate Added",
        description: "The candidate has been successfully added to the pipeline.",
      });
    },
    onError: (error) => {
      console.error('Error creating pipeline candidate:', error);
      toast({
        title: "Error",
        description: "Failed to add candidate to pipeline. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdatePipelineCandidate = () => {
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: (updateData: UpdatePipelineCandidateData) => 
      PipelineService.updatePipelineCandidate(updateData),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Pipeline Candidate Updated",
        description: "The candidate's pipeline status has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error('Error updating pipeline candidate:', error);
      toast({
        title: "Error",
        description: "Failed to update candidate pipeline status. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeletePipelineCandidate = () => {
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: (id: string) => PipelineService.deletePipelineCandidate(id),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Pipeline Candidate Removed",
        description: "The candidate has been successfully removed from the pipeline.",
      });
    },
    onError: (error) => {
      console.error('Error deleting pipeline candidate:', error);
      toast({
        title: "Error",
        description: "Failed to remove candidate from pipeline. Please try again.",
        variant: "destructive",
      });
    },
  });
};

// Helper hook for initialization
export const useInitializePipelineData = () => {
  const createStage = useCreatePipelineStage();
  const { toast } = useToast();
  
  const initializeDefaultStages = async (userId: string) => {
    const defaultStages = [
      { name: 'Applied', count: 0, color: 'bg-blue-500', stage_order: 1 },
      { name: 'Phone Screen', count: 0, color: 'bg-yellow-500', stage_order: 2 },
      { name: 'Interview', count: 0, color: 'bg-purple-500', stage_order: 3 },
      { name: 'Final Round', count: 0, color: 'bg-orange-500', stage_order: 4 },
      { name: 'Offer', count: 0, color: 'bg-green-500', stage_order: 5 },
    ];

    try {
      for (const stage of defaultStages) {
        await createStage.mutateAsync({
          user_id: userId,
          ...stage
        });
      }
      
      toast({
        title: "Pipeline Initialized",
        description: "Default pipeline stages have been created successfully.",
      });
    } catch (error) {
      console.error('Error initializing pipeline stages:', error);
      toast({
        title: "Error",
        description: "Failed to initialize pipeline stages. Please try again.",
        variant: "destructive",
      });
    }
  };

  return {
    initializeDefaultStages,
    isLoading: createStage.isPending
  };
}; 