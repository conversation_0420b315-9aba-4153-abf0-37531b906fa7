import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useMutation } from "@tanstack/react-query";
import { HiringTrendsService, HiringTrendsData, CreateHiringTrendsData, UpdateHiringTrendsData } from "@/services/HiringTrendsService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";

export const useHiringTrends = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  // Real-time hiring trends subscription
  const { records: hiringTrendsData = [], isLoading } = useRealtimeCollection(
    'hiring_trends',
    async () => {
      if (!user?.id) throw new Error("User not authenticated");
      
      // Try to get existing data, or initialize with defaults
      const existingData = await HiringTrendsService.getHiringTrends(user.id);
      if (existingData.length > 0) {
        return existingData;
      }
      
      // Initialize with default hiring trends data if none exists
      const defaultHiringTrends: CreateHiringTrendsData[] = [
        {
          user_id: user.id,
          period: 'Jun',
          predicted_hires: 45
        },
        {
          user_id: user.id,
          period: 'Jul',
          predicted_hires: 52
        },
        {
          user_id: user.id,
          period: 'Aug',
          predicted_hires: 58
        },
        {
          user_id: user.id,
          period: 'Sep',
          predicted_hires: 63
        }
      ];

      // Create default hiring trends data
      const createdData = await Promise.all(
        defaultHiringTrends.map(trend => 
          HiringTrendsService.createHiringTrend(trend)
        )
      );

      return createdData;
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return { data: hiringTrendsData, isLoading, error: null };
};

export const useRecentHiringTrends = (limit = 6) => {
  const { user } = useAuth();
  
  // Real-time recent hiring trends subscription
  const { records: recentTrendsData = [], isLoading } = useRealtimeCollection(
    'hiring_trends',
    async () => {
      if (!user?.id) throw new Error("User not authenticated");
      return HiringTrendsService.getRecentTrends(user.id, limit);
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return { data: recentTrendsData, isLoading, error: null };
};

export const useHiringTrendsAccuracy = () => {
  const { user } = useAuth();
  
  // Real-time hiring trends accuracy subscription
  const { records: accuracyData = [], isLoading } = useRealtimeCollection(
    'hiring_trends',
    async () => {
      if (!user?.id) throw new Error("User not authenticated");
      const accuracy = await HiringTrendsService.getPredictionAccuracy(user.id);
      return [accuracy]; // Wrap in array for useRealtimeCollection
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return { data: accuracyData[0] || null, isLoading, error: null };
};

export const useCreateHiringTrend = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: CreateHiringTrendsData) => HiringTrendsService.createHiringTrend(data),
    onSuccess: () => {
      toast({
        title: "Hiring Trend Created",
        description: "Hiring trend has been created successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      });
    }
  });
};

export const useUpdateHiringTrend = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: UpdateHiringTrendsData) => HiringTrendsService.updateHiringTrend(data),
    onSuccess: () => {
      toast({
        title: "Hiring Trend Updated",
        description: "Hiring trend has been updated successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      });
    }
  });
}; 