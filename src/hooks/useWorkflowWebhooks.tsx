import React from "react";
import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";

export interface WorkflowWebhook {
  id: string;
  workflow_id: string;
  webhook_secret: string;
  webhook_url: string;
  description: string | null;
  is_active: boolean;
  last_triggered: string | null;
  trigger_count: number;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export const useWorkflowWebhooks = (workflowId?: string) => {
  const { user } = useAuth();

  // Real-time workflow webhooks subscription
  const { records: allWebhooks = [], isLoading } = useRealtimeCollection(
    'workflow_webhooks',
    async () => {
      if (!user) {
        console.log('No authenticated user, returning empty array');
        return [];
      }

      try {
        let query = supabase
          .from('workflow_webhooks')
          .select('*')
          .eq('created_by', user.id);
          
        if (workflowId) {
          query = query.eq('workflow_id', workflowId);
        }
        
        const { data, error } = await query
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching workflow webhooks:', error);
          return [];
        }

        return data || [];
      } catch (error) {
        console.error('Error in useWorkflowWebhooks:', error);
        return [];
      }
    },
    'public',
    `created_by=eq.${user?.id}${workflowId ? `,workflow_id=eq.${workflowId}` : ''}`
  );

  return { data: allWebhooks, isLoading, error: null };
};

export const useCreateWorkflowWebhook = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (webhookData: {
      workflow_id: string;
      description?: string;
    }) => {
      if (!user) {
        throw new Error('User must be authenticated to create a workflow webhook');
      }

      // Generate webhook URL and secret
      const webhookUrl = `webhook_${crypto.getRandomValues(new Uint8Array(16)).reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '')}`;
      const webhookSecret = crypto.getRandomValues(new Uint8Array(32)).reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');

      const { data, error } = await supabase
        .from('workflow_webhooks')
        .insert({
          workflow_id: webhookData.workflow_id,
          webhook_url: webhookUrl,
          webhook_secret: webhookSecret,
          description: webhookData.description || null,
          is_active: true,
          created_by: user.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Real-time subscription will automatically update the UI
      
      // Get the base URL from environment or use the current origin
      const baseUrl = import.meta.env.VITE_SUPABASE_URL || window.location.origin;
      const webhookEndpoint = `${baseUrl}/functions/v1/webhooks/${data.webhook_url}`;
      
      toast({
        title: "Webhook Created",
        description: (
          <div className="space-y-2">
            <p>Your webhook has been successfully created.</p>
            <div className="bg-muted p-2 rounded text-xs">
              <p className="font-semibold">Webhook URL:</p>
              <code className="break-all">{webhookEndpoint}</code>
            </div>
            <div className="bg-muted p-2 rounded text-xs">
              <p className="font-semibold">Secret:</p>
              <code className="break-all">{data.webhook_secret}</code>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Save this secret! It won't be shown again.
            </p>
          </div>
        ),
        duration: 10000,
      });
    },
    onError: (error) => {
      console.error('Error creating workflow webhook:', error);
      toast({
        title: "Error",
        description: "Failed to create workflow webhook. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateWorkflowWebhook = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (webhookData: {
      id: string;
      description?: string;
      is_active?: boolean;
    }) => {
      const { id, ...updateData } = webhookData;
      
      const { data, error } = await supabase
        .from('workflow_webhooks')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Webhook Updated",
        description: "Your workflow webhook has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error('Error updating workflow webhook:', error);
      toast({
        title: "Error",
        description: "Failed to update workflow webhook. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteWorkflowWebhook = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('workflow_webhooks')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return id;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Webhook Deleted",
        description: "Your workflow webhook has been successfully deleted.",
      });
    },
    onError: (error) => {
      console.error('Error deleting workflow webhook:', error);
      toast({
        title: "Error",
        description: "Failed to delete workflow webhook. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useRegenerateWebhookSecret = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      // Generate new secret
      const newSecret = crypto.getRandomValues(new Uint8Array(32)).reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');
      
      const { data, error } = await supabase
        .from('workflow_webhooks')
        .update({
          webhook_secret: newSecret,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Secret Regenerated",
        description: (
          <div className="space-y-2">
            <p>Your webhook secret has been regenerated.</p>
            <div className="bg-muted p-2 rounded text-xs">
              <p className="font-semibold">New Secret:</p>
              <code className="break-all">{data.webhook_secret}</code>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Save this secret! It won't be shown again.
            </p>
          </div>
        ),
        duration: 10000,
      });
    },
    onError: (error) => {
      console.error('Error regenerating webhook secret:', error);
      toast({
        title: "Error",
        description: "Failed to regenerate webhook secret. Please try again.",
        variant: "destructive",
      });
    },
  });
};
