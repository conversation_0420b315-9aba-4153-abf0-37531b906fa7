
import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { useCreateTimelineEntry } from "@/hooks/useCreateTimelineEntry";

interface CreateInterviewData {
  candidate_id: string;
  interview_type: string;
  scheduled_date: string;
  duration_minutes?: number;
  location?: string;
  meeting_platform?: string;
  meeting_link?: string;
  interviewers?: string[];
}

export const useCreateInterview = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const createTimelineEntry = useCreateTimelineEntry();

  return useMutation({
    mutationFn: async (interviewData: CreateInterviewData) => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('candidate_interviews')
        .insert({
          ...interviewData,
          user_id: user.id,
          scheduled_date: new Date(interviewData.scheduled_date).toISOString(),
          duration_minutes: interviewData.duration_minutes || 60,
          status: 'scheduled',
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: async (data) => {
      // Real-time subscription will automatically update the UI
      
      // Create timeline entry
      await createTimelineEntry.mutateAsync({
        candidate_id: data.candidate_id,
        event_type: 'interview',
        title: `${data.interview_type} Scheduled`,
        description: `Interview scheduled for ${new Date(data.scheduled_date).toLocaleDateString()} at ${new Date(data.scheduled_date).toLocaleTimeString()}`,
        event_date: data.scheduled_date,
        status: 'upcoming',
      });

      toast({
        title: "Interview Scheduled",
        description: "The interview has been successfully scheduled.",
      });
    },
    onError: (error) => {
      console.error('Error creating interview:', error);
      toast({
        title: "Error",
        description: "Failed to schedule interview. Please try again.",
        variant: "destructive",
      });
    },
  });
};
