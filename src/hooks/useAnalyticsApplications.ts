import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface AnalyticsApplication {
  id: string;
  user_id: string;
  period: string;
  applications: number;
  interviews: number;
  created_at: string;
  updated_at: string;
}

const transformApplicationsForChart = (applications: AnalyticsApplication[]) => {
  if (!applications || !Array.isArray(applications)) return [];
  
  return applications.map(app => ({
    name: app.period, // Change period to name for chart compatibility
    period: app.period,
    applications: app.applications,
    qualified: Math.floor(app.applications * 0.6), // Mock qualified count as 60% of applications
    interviews: app.interviews,
    offers: Math.floor(app.applications * 0.15), // <PERSON>ck offers as 15% of applications
    conversion_rate: app.applications > 0 ? 
      ((Math.floor(app.applications * 0.15)) / app.applications * 100).toFixed(1) : '0'
  }));
};

export const useAnalyticsApplications = () => {
  const { user } = useAuth();

  // Real-time applications analytics subscription
  const { records: applicationsData = [], isLoading } = useRealtimeCollection(
    'analytics_applications',
    async () => {
      if (!user) {
        console.log('No authenticated user, returning empty array');
        return [];
      }

      const { data, error } = await supabase
        .from('analytics_applications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at');

      if (error) {
        console.error('Error fetching analytics applications:', error);
        throw error;
      }
      
      return transformApplicationsForChart(data || []);
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return {
    data: applicationsData,
    isLoading,
    error: null
  };
};