
import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { useCreateTimelineEntry } from "@/hooks/useCreateTimelineEntry";

interface UpdateInterviewData {
  id: string;
  interview_type?: string;
  scheduled_date?: string;
  duration_minutes?: number;
  status?: string;
  interviewers?: string[];
  location?: string | null;
  meeting_platform?: string | null;
  meeting_link?: string | null;
  feedback?: string | null;
}

export const useUpdateInterview = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const createTimelineEntry = useCreateTimelineEntry();

  return useMutation({
    mutationFn: async (interviewData: UpdateInterviewData) => {
      if (!user) throw new Error('User not authenticated');

      const { id, ...updateData } = interviewData;
      
      const { data, error } = await supabase
        .from('candidate_interviews')
        .update({
          ...updateData,
          scheduled_date: updateData.scheduled_date ? new Date(updateData.scheduled_date).toISOString() : undefined,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: async (data) => {
      // Real-time subscription will automatically update the UI
      
      // Create timeline entry for interview update
      if (data) {
        await createTimelineEntry.mutateAsync({
          candidate_id: data.candidate_id,
          event_type: 'interview',
          title: `Interview Updated: ${data.interview_type}`,
          description: `Interview details were updated for ${new Date(data.scheduled_date).toLocaleDateString()} at ${new Date(data.scheduled_date).toLocaleTimeString()}`,
          event_date: data.scheduled_date,
          status: 'completed',
        });
      }
      
      toast({
        title: "Interview Updated",
        description: "The interview has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error('Error updating interview:', error);
      toast({
        title: "Error",
        description: "Failed to update interview. Please try again.",
        variant: "destructive",
      });
    },
  });
};
