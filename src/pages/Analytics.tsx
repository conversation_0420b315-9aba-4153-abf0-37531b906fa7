
import { AnalyticsDashboard } from "@/components/analytics/AnalyticsDashboard";
import { Button } from "@/components/ui/button";
import { RefreshCw, Loader2 } from "lucide-react";
import { useGenerateAnalyticsData } from "@/hooks/useAnalyticsDataGeneration";

const Analytics = () => {
  const generateAnalytics = useGenerateAnalyticsData();

  const handleRefreshAnalytics = async () => {
    try {
      await generateAnalytics.mutateAsync();
    } catch (error) {
      console.error('Failed to refresh analytics:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Analytics</h1>
        <Button
          variant="outline"
          onClick={handleRefreshAnalytics}
          disabled={generateAnalytics.isPending}
        >
          {generateAnalytics.isPending ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="mr-2 h-4 w-4" />
          )}
          {generateAnalytics.isPending ? 'Refreshing...' : 'Refresh Analytics'}
        </Button>
      </div>
      <AnalyticsDashboard />
    </div>
  );
};

export default Analytics;
