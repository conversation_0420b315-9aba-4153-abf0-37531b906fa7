
import React, { useContext } from "react";
import { CalendarView } from "@/components/calendar/CalendarView";
import { UpcomingEvents } from "@/components/calendar/UpcomingEvents";
import { LocalEventsContext } from "@/components/layout/AppLayout";

const Calendar = () => {
  const { localEvents, setLocalEvents } = useContext(LocalEventsContext);
  return (
    <div className="max-w-screen-2xl mx-auto space-y-4 sm:space-y-6 p-4 sm:p-0">
      <h1 className="text-3xl font-bold">Calendar</h1>
      <CalendarView onEventsChange={setLocalEvents} />
    </div>
  );
};

export default Calendar;
