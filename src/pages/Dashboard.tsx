
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { DashboardStats } from "@/components/dashboard/DashboardStats";
import { RecentCandidates } from "@/components/dashboard/RecentCandidates";
import { RecentJobs } from "@/components/dashboard/RecentJobs";
import { QuickActions } from "@/components/dashboard/QuickActions";
import { HiringPipeline } from "@/components/analytics/HiringPipeline";
import { UpcomingEvents } from "@/components/calendar/UpcomingEvents";
import { DashboardFiltersProvider, useDashboardFilters } from "@/contexts/DashboardFiltersContext";
import React, { useContext } from "react";
import { LocalEventsContext } from "@/components/layout/AppLayout";

function DashboardContent() {
  const { componentVisibility } = useDashboardFilters();
  const { localEvents, setLocalEvents } = useContext(LocalEventsContext);

  // Calculate if we have any main content to show
  const hasMainContent = componentVisibility.recentCandidates ||
                        componentVisibility.recentJobs ||
                        componentVisibility.hiringPipeline;

  // Calculate if we have any sidebar content to show
  const hasSidebarContent = componentVisibility.quickActions ||
                           componentVisibility.upcomingEvents;

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-0">
      <DashboardHeader />

      {componentVisibility.dashboardStats && <DashboardStats />}

      {(hasMainContent || hasSidebarContent) && (
        <div className={`grid gap-4 sm:gap-6 ${hasMainContent && hasSidebarContent ? 'lg:grid-cols-12' : ''}`}>
          {/* Main content area - takes up 8 columns on large screens when sidebar is present */}
          {hasMainContent && (
            <div className={`${hasSidebarContent ? 'lg:col-span-8' : ''} space-y-4 sm:space-y-6 min-w-0`}>
              {/* Recent Candidates and Jobs - stack on mobile, side by side on larger screens */}
              {(componentVisibility.recentCandidates || componentVisibility.recentJobs) && (
                <div className={`grid gap-4 sm:gap-6 ${
                  componentVisibility.recentCandidates && componentVisibility.recentJobs
                    ? 'sm:grid-cols-2'
                    : ''
                }`}>
                  {componentVisibility.recentCandidates && (
                    <div className="min-w-0">
                      <RecentCandidates />
                    </div>
                  )}
                  {componentVisibility.recentJobs && (
                    <div className="min-w-0">
                      <RecentJobs />
                    </div>
                  )}
                </div>
              )}

              {/* Hiring Pipeline gets full width */}
              {componentVisibility.hiringPipeline && (
                <div className="min-w-0">
                  <HiringPipeline />
                </div>
              )}
            </div>
          )}

          {/* Sidebar - takes up 4 columns on large screens, full width on mobile */}
          {hasSidebarContent && (
            <div className={`${hasMainContent ? 'lg:col-span-4' : ''} space-y-4 sm:space-y-6 min-w-0`}>
              {componentVisibility.quickActions && (
                <div className="min-w-0">
                  <QuickActions />
                </div>
              )}
              {componentVisibility.upcomingEvents && (
                <div className="min-w-0">
                  <UpcomingEvents events={localEvents} onDeleteEvent={eventId => setLocalEvents(prev => prev.filter(e => e.id !== eventId))} />
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default function Dashboard() {
  // Removed workflow execution polling - this should be handled by proper background scheduler (Supabase Edge Functions with cron)

  return (
    <DashboardFiltersProvider>
      <DashboardContent />
    </DashboardFiltersProvider>
  );
}
