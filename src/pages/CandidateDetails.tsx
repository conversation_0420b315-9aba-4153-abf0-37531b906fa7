
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { CandidateHeader } from "@/components/candidate/CandidateHeader";
import { CandidateProfile } from "@/components/candidate/CandidateProfile";
import { useToast } from "@/hooks/use-toast";
import { useCandidate } from "@/hooks/useCandidate";
import { useAuth } from "@/contexts/AuthContext";
import { Skeleton } from "@/components/ui/skeleton";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { EmailComposer } from "@/components/communication/EmailComposer";
import { JobMatchingModal } from "@/components/job/JobMatchingModal";
import { useJobs } from "@/hooks/useJobs";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useCreateMessage } from "@/hooks/useCreateMessage";

const CandidateDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, loading: authLoading } = useAuth();
  const { data: candidate, isLoading, error } = useCandidate(id || '');
  const { data: jobs = [] } = useJobs();
  const createMessage = useCreateMessage();
  
  const [isMessageDialogOpen, setIsMessageDialogOpen] = useState(false);
  const [isAddToJobDialogOpen, setIsAddToJobDialogOpen] = useState(false);
  const [selectedJobId, setSelectedJobId] = useState("");

  // Show loading state while authentication is being checked
  if (authLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-lg text-muted-foreground">Loading...</p>
      </div>
    );
  }

  // Show authentication required message
  if (!user) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-lg text-muted-foreground">Please sign in to view candidate details</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Skeleton className="h-16 w-16 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-28" />
          </div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-4">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-48 w-full" />
          </div>
          <div className="space-y-4">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-lg text-muted-foreground">Error loading candidate details</p>
      </div>
    );
  }

  if (!candidate) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-lg text-muted-foreground">Candidate not found</p>
      </div>
    );
  }

  const handleMessageClick = () => {
    setIsMessageDialogOpen(true);
  };

  const handleAddToJobClick = () => {
    setIsAddToJobDialogOpen(true);
  };

  const handleAddToJob = async () => {
    if (!selectedJobId || !candidate) {
      toast({
        title: "Selection Required",
        description: "Please select a job to add the candidate to.",
        variant: "destructive"
      });
      return;
    }

    const selectedJob = jobs.find(job => job.id === selectedJobId);
    if (!selectedJob) {
      toast({
        title: "Job Not Found",
        description: "The selected job could not be found.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Create a message to track this action
      await createMessage.mutateAsync({
        sender_name: "System",
        sender_email: "<EMAIL>",
        content: `Candidate ${candidate.name} has been added to job "${selectedJob.title}" pipeline.`,
        status: "read"
      });

      toast({
        title: "Candidate Added to Job",
        description: `${candidate.name} has been added to the ${selectedJob.title} pipeline.`
      });

      setIsAddToJobDialogOpen(false);
      setSelectedJobId("");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add candidate to job. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleMessageSuccess = () => {
    setIsMessageDialogOpen(false);
    toast({
      title: "Message Sent",
      description: `Message sent to ${candidate?.name} successfully.`
    });
  };

  return (
    <div className="space-y-6">
      <CandidateHeader 
        candidate={candidate} 
        onMessageClick={handleMessageClick}
        onAddToJobClick={handleAddToJobClick}
      />
      <CandidateProfile candidate={candidate} />

      {/* Message Dialog */}
      <Dialog open={isMessageDialogOpen} onOpenChange={setIsMessageDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Send Message to {candidate?.name}</DialogTitle>
          </DialogHeader>
          <EmailComposer 
            candidate={candidate} 
            onSuccess={handleMessageSuccess}
          />
        </DialogContent>
      </Dialog>

      {/* Add to Job Dialog */}
      <Dialog open={isAddToJobDialogOpen} onOpenChange={setIsAddToJobDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Add {candidate?.name} to Job</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="job-select">Select Job Position</Label>
              <Select value={selectedJobId} onValueChange={setSelectedJobId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a job position" />
                </SelectTrigger>
                <SelectContent>
                  {jobs.length === 0 ? (
                    <SelectItem value="no-jobs" disabled>
                      No active jobs available
                    </SelectItem>
                  ) : (
                    jobs.map((job) => (
                      <SelectItem key={job.id} value={job.id}>
                        {job.title} - {job.department}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2">
              <Button 
                onClick={handleAddToJob}
                disabled={!selectedJobId || createMessage.isPending || jobs.length === 0}
                className="flex-1"
              >
                {createMessage.isPending ? "Adding..." : "Add to Job"}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setIsAddToJobDialogOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CandidateDetails;
