import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { MessageList } from "@/components/message/MessageList";
import { MessageComposer } from "@/components/message/MessageComposer";
import { useMessages, useUpdateMessage, type Message } from "@/hooks/useMessages";
import { useMessageTemplates } from "@/hooks/useMessageTemplates";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

const Messages = () => {
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const { user } = useAuth();
  const { data: templates = [] } = useMessageTemplates();
  const updateMessage = useUpdateMessage();

  // Fetch messages with realtime updates
  const fetchMessages = async () => {
    if (!user) return [];
    
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  };
  
  const { records: messages = [], isLoading } = useRealtimeCollection(
    'messages',
    fetchMessages
  );

  const handleSelectMessage = (message: Message) => {
    setSelectedMessage(message);
    
    // Mark message as read if it's unread
    if (message.status === 'unread') {
      updateMessage.mutate({
        id: message.id,
        status: 'read'
      });
    }
  };

  const handleMessageUpdate = (updatedMessage: Message) => {
    setSelectedMessage(updatedMessage);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Messages</h1>
        <div className="text-center p-8">Loading messages...</div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-0">
      <div className="flex items-center gap-2">
        <h1 className="text-2xl sm:text-3xl font-bold truncate">Messages</h1>
      </div>
      
      {/* Mobile: Show either list or conversation, not both */}
      <div className="lg:hidden">
        {!selectedMessage ? (
          <Card className="min-w-0">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Conversations ({messages.length})</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <MessageList 
                messages={messages.map(msg => ({
                  id: msg.id,
                  sender: {
                    name: msg.sender_name,
                    avatar: msg.sender_avatar || "/placeholder.svg",
                    role: msg.sender_role || "Unknown"
                  },
                  content: msg.content,
                  time: new Date(msg.created_at).toLocaleString(),
                  status: msg.status as "read" | "unread",
                  isStarred: msg.is_starred,
                  lastActivity: new Date(msg.updated_at).toLocaleDateString(),
                  followUp: msg.follow_up,
                  reminder: msg.reminder
                }))}
                onSelectMessage={(mockMessage) => {
                  const realMessage = messages.find(m => m.id === mockMessage.id);
                  if (realMessage) handleSelectMessage(realMessage);
                }}
                selectedMessageId={selectedMessage?.id}
              />
            </CardContent>
          </Card>
        ) : (
          <Card className="min-w-0">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <button 
                  onClick={() => setSelectedMessage(null)}
                  className="p-1 hover:bg-accent rounded"
                >
                  ←
                </button>
                <CardTitle className="text-lg truncate">
                  {selectedMessage.sender_name}
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="flex flex-col h-[calc(100vh-200px)] p-0">
              <MessageComposer 
                selectedMessage={{
                  id: selectedMessage.id,
                  sender: {
                    name: selectedMessage.sender_name,
                    avatar: selectedMessage.sender_avatar || "/placeholder.svg",
                    role: selectedMessage.sender_role || "Unknown"
                  },
                  content: selectedMessage.content,
                  time: new Date(selectedMessage.created_at).toLocaleString(),
                  status: selectedMessage.status as "read" | "unread",
                  isStarred: selectedMessage.is_starred,
                  lastActivity: new Date(selectedMessage.updated_at).toLocaleDateString(),
                  followUp: selectedMessage.follow_up,
                  reminder: selectedMessage.reminder
                }}
                onMessageUpdate={(mockMessage) => {
                  const realMessage = messages.find(m => m.id === mockMessage.id);
                  if (realMessage) handleMessageUpdate(realMessage);
                }}
              />
            </CardContent>
          </Card>
        )}
      </div>

      {/* Desktop: Show both side by side */}
      <div className="hidden lg:grid grid-cols-3 gap-6 h-[calc(100vh-200px)]">
        <Card className="col-span-1 min-w-0">
          <CardHeader>
            <CardTitle>Conversations ({messages.length})</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <MessageList 
              messages={messages.map(msg => ({
                id: msg.id,
                sender: {
                  name: msg.sender_name,
                  avatar: msg.sender_avatar || "/placeholder.svg",
                  role: msg.sender_role || "Unknown"
                },
                content: msg.content,
                time: new Date(msg.created_at).toLocaleString(),
                status: msg.status as "read" | "unread",
                isStarred: msg.is_starred,
                lastActivity: new Date(msg.updated_at).toLocaleDateString(),
                followUp: msg.follow_up,
                reminder: msg.reminder
              }))}
              onSelectMessage={(mockMessage) => {
                const realMessage = messages.find(m => m.id === mockMessage.id);
                if (realMessage) handleSelectMessage(realMessage);
              }}
              selectedMessageId={selectedMessage?.id}
            />
          </CardContent>
        </Card>
        
        <Card className="col-span-2 min-w-0">
          <CardHeader>
            <CardTitle>
              {selectedMessage ? "Conversation" : "Select a conversation"}
            </CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col h-full p-0">
            <MessageComposer 
              selectedMessage={selectedMessage ? {
                id: selectedMessage.id,
                sender: {
                  name: selectedMessage.sender_name,
                  avatar: selectedMessage.sender_avatar || "/placeholder.svg",
                  role: selectedMessage.sender_role || "Unknown"
                },
                content: selectedMessage.content,
                time: new Date(selectedMessage.created_at).toLocaleString(),
                status: selectedMessage.status as "read" | "unread",
                isStarred: selectedMessage.is_starred,
                lastActivity: new Date(selectedMessage.updated_at).toLocaleDateString(),
                followUp: selectedMessage.follow_up,
                reminder: selectedMessage.reminder
              } : undefined}
              onMessageUpdate={(mockMessage) => {
                const realMessage = messages.find(m => m.id === mockMessage.id);
                if (realMessage) handleMessageUpdate(realMessage);
              }}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Messages;