
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search, Plus, SlidersHorizontal, Users } from "lucide-react";
import { initializeDatabase } from "@/integrations/supabase/client";
import { CandidateList } from "@/components/candidate/CandidateList";
import { CandidateFilters } from "@/components/candidate/CandidateFilters";
import { AddCandidateDialog } from "@/components/candidate/AddCandidateDialog";
import { CandidateMetrics } from "@/components/candidate/CandidateMetrics";
import { CandidateAnalytics } from "@/components/candidate/CandidateAnalytics";
import { CandidateTools } from "@/components/candidate/CandidateTools";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@/components/ui/sheet";

const Candidates = () => {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  useEffect(() => {
    // Initialize database and assign orphaned candidates
    initializeDatabase();
  }, []);

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-0">
      <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
        <div className="flex items-center gap-2 min-w-0">
          <Users className="w-6 h-6 sm:w-8 sm:h-8 text-primary flex-shrink-0" />
          <h1 className="text-2xl sm:text-3xl font-bold truncate">Candidates</h1>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="w-full sm:w-auto">
                <SlidersHorizontal className="w-4 h-4 mr-2" />
                Filters
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Filter Candidates</SheetTitle>
              </SheetHeader>
              <CandidateFilters />
            </SheetContent>
          </Sheet>
          <Button onClick={() => setIsAddDialogOpen(true)} className="w-full sm:w-auto">
            <Plus className="w-4 h-4 mr-2" />
            Add Candidate
          </Button>
        </div>
      </div>

      <Tabs defaultValue="list" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 h-auto">
          <TabsTrigger value="list" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">Candidate </span>List
          </TabsTrigger>
          <TabsTrigger value="metrics" className="text-xs sm:text-sm">Metrics</TabsTrigger>
          <TabsTrigger value="analytics" className="text-xs sm:text-sm">Analytics</TabsTrigger>
          <TabsTrigger value="tools" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">AI </span>Tools
          </TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          <Card className="min-w-0">
            <CardHeader className="space-y-3 sm:space-y-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg sm:text-xl">All Candidates</CardTitle>
              </div>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input 
                  placeholder="Search candidates..." 
                  className="pl-8 w-full" 
                />
              </div>
            </CardHeader>
            <CardContent className="min-w-0">
              <CandidateList />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="min-w-0">
          <CandidateMetrics />
        </TabsContent>

        <TabsContent value="analytics" className="min-w-0">
          <CandidateAnalytics />
        </TabsContent>
        
        <TabsContent value="tools" className="min-w-0">
          <CandidateTools />
        </TabsContent>
      </Tabs>

      <AddCandidateDialog
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
      />
    </div>
  );
};

export default Candidates;
