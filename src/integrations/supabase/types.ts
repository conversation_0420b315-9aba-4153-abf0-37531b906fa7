export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      analytics_applications: {
        Row: {
          applications: number
          created_at: string
          id: string
          interviews: number | null
          period: string
          updated_at: string
          user_id: string
        }
        Insert: {
          applications: number
          created_at?: string
          id?: string
          interviews?: number | null
          period: string
          updated_at?: string
          user_id: string
        }
        Update: {
          applications?: number
          created_at?: string
          id?: string
          interviews?: number | null
          period?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      analytics_diversity: {
        Row: {
          category: string
          created_at: string
          id: string
          total: number
          updated_at: string
          user_id: string
          value: number
        }
        Insert: {
          category: string
          created_at?: string
          id?: string
          total: number
          updated_at?: string
          user_id: string
          value: number
        }
        Update: {
          category?: string
          created_at?: string
          id?: string
          total?: number
          updated_at?: string
          user_id?: string
          value?: number
        }
        Relationships: []
      }
      analytics_metrics: {
        Row: {
          change_percentage: number | null
          created_at: string
          id: string
          metric_name: string
          metric_value: number
          period: string
          updated_at: string
          user_id: string
        }
        Insert: {
          change_percentage?: number | null
          created_at?: string
          id?: string
          metric_name: string
          metric_value?: number
          period?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          change_percentage?: number | null
          created_at?: string
          id?: string
          metric_name?: string
          metric_value?: number
          period?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      analytics_salary: {
        Row: {
          confidence: number | null
          created_at: string
          current_salary: number | null
          experience_level: string | null
          id: string
          industry: string | null
          location: string | null
          market_rate: number | null
          market_salary: number
          position: string | null
          recommended_salary: number
          role: string
          updated_at: string
          user_id: string
        }
        Insert: {
          confidence?: number | null
          created_at?: string
          current_salary?: number | null
          experience_level?: string | null
          id?: string
          industry?: string | null
          location?: string | null
          market_rate?: number | null
          market_salary: number
          position?: string | null
          recommended_salary: number
          role: string
          updated_at?: string
          user_id: string
        }
        Update: {
          confidence?: number | null
          created_at?: string
          current_salary?: number | null
          experience_level?: string | null
          id?: string
          industry?: string | null
          location?: string | null
          market_rate?: number | null
          market_salary?: number
          position?: string | null
          recommended_salary?: number
          role?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      analytics_skills: {
        Row: {
          created_at: string
          current_level: number
          gap: number
          id: string
          recommendation: string | null
          required_level: number
          skill: string
          success_impact: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          current_level: number
          gap: number
          id?: string
          recommendation?: string | null
          required_level: number
          skill: string
          success_impact?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          current_level?: number
          gap?: number
          id?: string
          recommendation?: string | null
          required_level?: number
          skill?: string
          success_impact?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      analytics_sources: {
        Row: {
          created_at: string
          effectiveness: number
          hires: number
          id: string
          source: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          effectiveness: number
          hires: number
          id?: string
          source: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          effectiveness?: number
          hires?: number
          id?: string
          source?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      candidate_documents: {
        Row: {
          candidate_id: string
          file_url: string
          id: string
          name: string
          size: number | null
          type: string | null
          uploaded_at: string
          uploaded_by: string
        }
        Insert: {
          candidate_id: string
          file_url: string
          id?: string
          name: string
          size?: number | null
          type?: string | null
          uploaded_at?: string
          uploaded_by: string
        }
        Update: {
          candidate_id?: string
          file_url?: string
          id?: string
          name?: string
          size?: number | null
          type?: string | null
          uploaded_at?: string
          uploaded_by?: string
        }
        Relationships: []
      }
      candidate_interviews: {
        Row: {
          candidate_id: string
          created_at: string
          duration_minutes: number
          feedback: string | null
          id: string
          interview_type: string
          interviewers: string[] | null
          location: string | null
          meeting_link: string | null
          meeting_platform: string | null
          scheduled_date: string
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          candidate_id: string
          created_at?: string
          duration_minutes?: number
          feedback?: string | null
          id?: string
          interview_type: string
          interviewers?: string[] | null
          location?: string | null
          meeting_link?: string | null
          meeting_platform?: string | null
          scheduled_date: string
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          candidate_id?: string
          created_at?: string
          duration_minutes?: number
          feedback?: string | null
          id?: string
          interview_type?: string
          interviewers?: string[] | null
          location?: string | null
          meeting_link?: string | null
          meeting_platform?: string | null
          scheduled_date?: string
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      candidate_notes: {
        Row: {
          author_name: string
          candidate_id: string
          content: string
          created_at: string
          id: string
          is_important: boolean
          updated_at: string
          user_id: string
        }
        Insert: {
          author_name: string
          candidate_id: string
          content: string
          created_at?: string
          id?: string
          is_important?: boolean
          updated_at?: string
          user_id: string
        }
        Update: {
          author_name?: string
          candidate_id?: string
          content?: string
          created_at?: string
          id?: string
          is_important?: boolean
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      candidate_timeline: {
        Row: {
          candidate_id: string
          created_at: string
          description: string | null
          event_date: string
          event_type: string
          id: string
          status: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          candidate_id: string
          created_at?: string
          description?: string | null
          event_date: string
          event_type: string
          id?: string
          status?: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          candidate_id?: string
          created_at?: string
          description?: string | null
          event_date?: string
          event_type?: string
          id?: string
          status?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      candidates: {
        Row: {
          ai_summary: string | null
          avatar: string | null
          created_at: string
          email: string
          experience: string | null
          github_url: string | null
          id: string
          industry: string | null
          linkedin_url: string | null
          location: string | null
          name: string
          phone: string | null
          recruiter_avatar: string | null
          recruiter_id: string | null
          recruiter_name: string | null
          relationship_score: number | null
          remote_preference: string | null
          role: string
          screening: Json | null
          search_vector: unknown | null
          skills: Json | null
          tags: string[] | null
          twitter_url: string | null
          updated_at: string
          user_id: string | null
          visa_status: string | null
        }
        Insert: {
          ai_summary?: string | null
          avatar?: string | null
          created_at?: string
          email: string
          experience?: string | null
          github_url?: string | null
          id?: string
          industry?: string | null
          linkedin_url?: string | null
          location?: string | null
          name: string
          phone?: string | null
          recruiter_avatar?: string | null
          recruiter_id?: string | null
          recruiter_name?: string | null
          relationship_score?: number | null
          remote_preference?: string | null
          role: string
          screening?: Json | null
          search_vector?: unknown | null
          skills?: Json | null
          tags?: string[] | null
          twitter_url?: string | null
          updated_at?: string
          user_id?: string | null
          visa_status?: string | null
        }
        Update: {
          ai_summary?: string | null
          avatar?: string | null
          created_at?: string
          email?: string
          experience?: string | null
          github_url?: string | null
          id?: string
          industry?: string | null
          linkedin_url?: string | null
          location?: string | null
          name?: string
          phone?: string | null
          recruiter_avatar?: string | null
          recruiter_id?: string | null
          recruiter_name?: string | null
          relationship_score?: number | null
          remote_preference?: string | null
          role?: string
          screening?: Json | null
          search_vector?: unknown | null
          skills?: Json | null
          tags?: string[] | null
          twitter_url?: string | null
          updated_at?: string
          user_id?: string | null
          visa_status?: string | null
        }
        Relationships: []
      }
      events: {
        Row: {
          category: string
          created_at: string
          description: string | null
          end_time: string
          event_type: string
          id: string
          location: string | null
          meeting_link: string | null
          priority: string
          start_time: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          category?: string
          created_at?: string
          description?: string | null
          end_time: string
          event_type?: string
          id?: string
          location?: string | null
          meeting_link?: string | null
          priority?: string
          start_time: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          category?: string
          created_at?: string
          description?: string | null
          end_time?: string
          event_type?: string
          id?: string
          location?: string | null
          meeting_link?: string | null
          priority?: string
          start_time?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      feature_flag_access_logs: {
        Row: {
          accessed_at: string | null
          flag_name: string
          has_access: boolean
          id: string
          is_pilot_user: boolean | null
          user_id: string
        }
        Insert: {
          accessed_at?: string | null
          flag_name: string
          has_access: boolean
          id?: string
          is_pilot_user?: boolean | null
          user_id: string
        }
        Update: {
          accessed_at?: string | null
          flag_name?: string
          has_access?: boolean
          id?: string
          is_pilot_user?: boolean | null
          user_id?: string
        }
        Relationships: []
      }
      feature_flags: {
        Row: {
          created_at: string | null
          description: string | null
          enabled: boolean | null
          id: string
          metadata: Json | null
          name: string
          pilot_enabled: boolean | null
          pilot_user_ids: string[] | null
          rollout_percentage: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          enabled?: boolean | null
          id?: string
          metadata?: Json | null
          name: string
          pilot_enabled?: boolean | null
          pilot_user_ids?: string[] | null
          rollout_percentage?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          enabled?: boolean | null
          id?: string
          metadata?: Json | null
          name?: string
          pilot_enabled?: boolean | null
          pilot_user_ids?: string[] | null
          rollout_percentage?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      feature_request_votes: {
        Row: {
          created_at: string | null
          id: string
          request_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          request_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          request_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "feature_request_votes_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "feature_requests"
            referencedColumns: ["id"]
          },
        ]
      }
      feature_requests: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          metadata: Json | null
          name: string
          priority: number | null
          request_type: string
          status: string | null
          updated_at: string | null
          use_case: string | null
          user_id: string
          vote_count: number | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          metadata?: Json | null
          name: string
          priority?: number | null
          request_type: string
          status?: string | null
          updated_at?: string | null
          use_case?: string | null
          user_id: string
          vote_count?: number | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          metadata?: Json | null
          name?: string
          priority?: number | null
          request_type?: string
          status?: string | null
          updated_at?: string | null
          use_case?: string | null
          user_id?: string
          vote_count?: number | null
        }
        Relationships: []
      }
      generated_reports: {
        Row: {
          access_count: number | null
          accessed_at: string | null
          description: string | null
          error_message: string | null
          expires_at: string | null
          file_path: string | null
          file_size: number | null
          format: string
          generated_at: string | null
          generated_by: string | null
          id: string
          metadata: Json | null
          name: string
          parameters_used: Json | null
          scheduled_report_id: string | null
          status: string
          template_id: string | null
        }
        Insert: {
          access_count?: number | null
          accessed_at?: string | null
          description?: string | null
          error_message?: string | null
          expires_at?: string | null
          file_path?: string | null
          file_size?: number | null
          format: string
          generated_at?: string | null
          generated_by?: string | null
          id?: string
          metadata?: Json | null
          name: string
          parameters_used?: Json | null
          scheduled_report_id?: string | null
          status?: string
          template_id?: string | null
        }
        Update: {
          access_count?: number | null
          accessed_at?: string | null
          description?: string | null
          error_message?: string | null
          expires_at?: string | null
          file_path?: string | null
          file_size?: number | null
          format?: string
          generated_at?: string | null
          generated_by?: string | null
          id?: string
          metadata?: Json | null
          name?: string
          parameters_used?: Json | null
          scheduled_report_id?: string | null
          status?: string
          template_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "generated_reports_scheduled_report_id_fkey"
            columns: ["scheduled_report_id"]
            isOneToOne: false
            referencedRelation: "due_scheduled_reports"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "generated_reports_scheduled_report_id_fkey"
            columns: ["scheduled_report_id"]
            isOneToOne: false
            referencedRelation: "scheduled_reports"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "generated_reports_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "report_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      jobs: {
        Row: {
          applicant_count: number | null
          benefits: string[] | null
          created_at: string
          department: string
          description: string
          experience_required: string | null
          id: string
          is_active: boolean | null
          is_urgent: boolean | null
          job_type: string
          location: string
          requirements: string[] | null
          salary_range: string | null
          search_vector: unknown | null
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          applicant_count?: number | null
          benefits?: string[] | null
          created_at?: string
          department: string
          description: string
          experience_required?: string | null
          id?: string
          is_active?: boolean | null
          is_urgent?: boolean | null
          job_type?: string
          location: string
          requirements?: string[] | null
          salary_range?: string | null
          search_vector?: unknown | null
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          applicant_count?: number | null
          benefits?: string[] | null
          created_at?: string
          department?: string
          description?: string
          experience_required?: string | null
          id?: string
          is_active?: boolean | null
          is_urgent?: boolean | null
          job_type?: string
          location?: string
          requirements?: string[] | null
          salary_range?: string | null
          search_vector?: unknown | null
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      message_templates: {
        Row: {
          content: string
          created_at: string
          id: string
          name: string
          subject: string
          template_category: string
          updated_at: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          name: string
          subject: string
          template_category?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          name?: string
          subject?: string
          template_category?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      messages: {
        Row: {
          content: string
          created_at: string
          follow_up: boolean
          id: string
          is_starred: boolean
          reminder: boolean
          sender_avatar: string | null
          sender_email: string
          sender_name: string
          sender_role: string | null
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          follow_up?: boolean
          id?: string
          is_starred?: boolean
          reminder?: boolean
          sender_avatar?: string | null
          sender_email: string
          sender_name: string
          sender_role?: string | null
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          follow_up?: boolean
          id?: string
          is_starred?: boolean
          reminder?: boolean
          sender_avatar?: string | null
          sender_email?: string
          sender_name?: string
          sender_role?: string | null
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          company: string | null
          created_at: string
          first_name: string | null
          id: string
          last_name: string | null
          role: string | null
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          company?: string | null
          created_at?: string
          first_name?: string | null
          id: string
          last_name?: string | null
          role?: string | null
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          company?: string | null
          created_at?: string
          first_name?: string | null
          id?: string
          last_name?: string | null
          role?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      report_templates: {
        Row: {
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string
          is_active: boolean | null
          name: string
          query_template: string | null
          template_config: Json
          updated_at: string | null
          visualization_config: Json | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          query_template?: string | null
          template_config?: Json
          updated_at?: string | null
          visualization_config?: Json | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          query_template?: string | null
          template_config?: Json
          updated_at?: string | null
          visualization_config?: Json | null
        }
        Relationships: []
      }
      scheduled_reports: {
        Row: {
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string
          is_active: boolean | null
          last_run_at: string | null
          name: string
          next_run_at: string | null
          output_format: string | null
          parameters: Json | null
          recipients: Json | null
          schedule_config: Json
          template_id: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          last_run_at?: string | null
          name: string
          next_run_at?: string | null
          output_format?: string | null
          parameters?: Json | null
          recipients?: Json | null
          schedule_config?: Json
          template_id?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          last_run_at?: string | null
          name?: string
          next_run_at?: string | null
          output_format?: string | null
          parameters?: Json | null
          recipients?: Json | null
          schedule_config?: Json
          template_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "scheduled_reports_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "report_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      tasks: {
        Row: {
          assignee: string | null
          category: string
          created_at: string
          description: string | null
          due_date: string | null
          id: string
          priority: string
          status: string
          title: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          assignee?: string | null
          category?: string
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          priority?: string
          status?: string
          title: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          assignee?: string | null
          category?: string
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          priority?: string
          status?: string
          title?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      usage_quotas: {
        Row: {
          created_at: string | null
          id: string
          provider: string
          quota_limit: number
          quota_type: string
          quota_used: number | null
          reset_at: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          provider: string
          quota_limit: number
          quota_type: string
          quota_used?: number | null
          reset_at: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          provider?: string
          quota_limit?: number
          quota_type?: string
          quota_used?: number | null
          reset_at?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      user_connections: {
        Row: {
          access_token: string | null
          created_at: string | null
          id: string
          is_active: boolean | null
          provider: string
          provider_account_email: string | null
          provider_account_id: string | null
          refresh_token: string | null
          scopes: string[] | null
          settings: Json | null
          token_expires_at: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          access_token?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          provider: string
          provider_account_email?: string | null
          provider_account_id?: string | null
          refresh_token?: string | null
          scopes?: string[] | null
          settings?: Json | null
          token_expires_at?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          access_token?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          provider?: string
          provider_account_email?: string | null
          provider_account_id?: string | null
          refresh_token?: string | null
          scopes?: string[] | null
          settings?: Json | null
          token_expires_at?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      user_feedback: {
        Row: {
          created_at: string | null
          feature_name: string
          feedback_type: string
          id: string
          message: string
          metadata: Json | null
          rating: number | null
          status: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          feature_name: string
          feedback_type: string
          id?: string
          message: string
          metadata?: Json | null
          rating?: number | null
          status?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          feature_name?: string
          feedback_type?: string
          id?: string
          message?: string
          metadata?: Json | null
          rating?: number | null
          status?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      user_settings: {
        Row: {
          created_at: string
          id: string
          settings: Json
          settings_type: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          settings: Json
          settings_type: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          settings?: Json
          settings_type?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      workflow_alert_history: {
        Row: {
          alert_channel: string
          alert_id: string
          alert_message: string
          alert_status: string
          alert_type: string
          created_at: string
          error_details: string | null
          execution_id: string
          id: string
          recipient: string
          sent_at: string | null
        }
        Insert: {
          alert_channel: string
          alert_id: string
          alert_message: string
          alert_status?: string
          alert_type: string
          created_at?: string
          error_details?: string | null
          execution_id: string
          id?: string
          recipient: string
          sent_at?: string | null
        }
        Update: {
          alert_channel?: string
          alert_id?: string
          alert_message?: string
          alert_status?: string
          alert_type?: string
          created_at?: string
          error_details?: string | null
          execution_id?: string
          id?: string
          recipient?: string
          sent_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "workflow_alert_history_alert_id_fkey"
            columns: ["alert_id"]
            isOneToOne: false
            referencedRelation: "workflow_alerts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workflow_alert_history_execution_id_fkey"
            columns: ["execution_id"]
            isOneToOne: false
            referencedRelation: "workflow_executions"
            referencedColumns: ["id"]
          },
        ]
      }
      workflow_alerts: {
        Row: {
          alert_on_duration_threshold: boolean
          alert_on_failure: boolean
          alert_on_success: boolean
          created_at: string
          created_by: string
          duration_threshold_ms: number | null
          email_addresses: string[] | null
          enable_email: boolean
          enable_slack: boolean
          id: string
          is_active: boolean
          max_alerts_per_hour: number
          slack_channel: string | null
          slack_webhook_url: string | null
          updated_at: string
          workflow_id: string
        }
        Insert: {
          alert_on_duration_threshold?: boolean
          alert_on_failure?: boolean
          alert_on_success?: boolean
          created_at?: string
          created_by: string
          duration_threshold_ms?: number | null
          email_addresses?: string[] | null
          enable_email?: boolean
          enable_slack?: boolean
          id?: string
          is_active?: boolean
          max_alerts_per_hour?: number
          slack_channel?: string | null
          slack_webhook_url?: string | null
          updated_at?: string
          workflow_id: string
        }
        Update: {
          alert_on_duration_threshold?: boolean
          alert_on_failure?: boolean
          alert_on_success?: boolean
          created_at?: string
          created_by?: string
          duration_threshold_ms?: number | null
          email_addresses?: string[] | null
          enable_email?: boolean
          enable_slack?: boolean
          id?: string
          is_active?: boolean
          max_alerts_per_hour?: number
          slack_channel?: string | null
          slack_webhook_url?: string | null
          updated_at?: string
          workflow_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "workflow_alerts_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_configurations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workflow_alerts_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_execution_stats"
            referencedColumns: ["workflow_id"]
          },
        ]
      }
      workflow_configurations: {
        Row: {
          config: Json
          created_at: string
          created_by: string
          description: string | null
          id: string
          is_active: boolean
          name: string
          updated_at: string
        }
        Insert: {
          config: Json
          created_at?: string
          created_by: string
          description?: string | null
          id?: string
          is_active?: boolean
          name: string
          updated_at?: string
        }
        Update: {
          config?: Json
          created_at?: string
          created_by?: string
          description?: string | null
          id?: string
          is_active?: boolean
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      workflow_executions: {
        Row: {
          completed_at: string | null
          created_at: string
          created_by: string
          execution_data: Json | null
          id: string
          logs: Json | null
          started_at: string | null
          status: string
          updated_at: string
          workflow_id: string
        }
        Insert: {
          completed_at?: string | null
          created_at?: string
          created_by: string
          execution_data?: Json | null
          id?: string
          logs?: Json | null
          started_at?: string | null
          status?: string
          updated_at?: string
          workflow_id: string
        }
        Update: {
          completed_at?: string | null
          created_at?: string
          created_by?: string
          execution_data?: Json | null
          id?: string
          logs?: Json | null
          started_at?: string | null
          status?: string
          updated_at?: string
          workflow_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "workflow_executions_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_configurations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workflow_executions_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_execution_stats"
            referencedColumns: ["workflow_id"]
          },
        ]
      }
      workflow_metrics: {
        Row: {
          context_data: Json | null
          cpu_usage_percent: number | null
          created_at: string
          created_by: string
          end_time: string
          error_details: string | null
          execution_duration_ms: number
          execution_id: string
          failed_nodes: number
          id: string
          node_errors: Json | null
          peak_memory_mb: number | null
          skipped_nodes: number
          start_time: string
          successful_nodes: number
          total_nodes_executed: number
          workflow_id: string
        }
        Insert: {
          context_data?: Json | null
          cpu_usage_percent?: number | null
          created_at?: string
          created_by: string
          end_time: string
          error_details?: string | null
          execution_duration_ms: number
          execution_id: string
          failed_nodes?: number
          id?: string
          node_errors?: Json | null
          peak_memory_mb?: number | null
          skipped_nodes?: number
          start_time: string
          successful_nodes?: number
          total_nodes_executed?: number
          workflow_id: string
        }
        Update: {
          context_data?: Json | null
          cpu_usage_percent?: number | null
          created_at?: string
          created_by?: string
          end_time?: string
          error_details?: string | null
          execution_duration_ms?: number
          execution_id?: string
          failed_nodes?: number
          id?: string
          node_errors?: Json | null
          peak_memory_mb?: number | null
          skipped_nodes?: number
          start_time?: string
          successful_nodes?: number
          total_nodes_executed?: number
          workflow_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "workflow_metrics_execution_id_fkey"
            columns: ["execution_id"]
            isOneToOne: false
            referencedRelation: "workflow_executions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workflow_metrics_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_configurations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workflow_metrics_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_execution_stats"
            referencedColumns: ["workflow_id"]
          },
        ]
      }
      workflow_schedules: {
        Row: {
          context: Json | null
          created_at: string
          created_by: string | null
          cron_schedule: string
          id: string
          is_active: boolean
          last_run: string | null
          next_run: string | null
          updated_at: string
          workflow_id: string | null
        }
        Insert: {
          context?: Json | null
          created_at?: string
          created_by?: string | null
          cron_schedule: string
          id?: string
          is_active?: boolean
          last_run?: string | null
          next_run?: string | null
          updated_at?: string
          workflow_id?: string | null
        }
        Update: {
          context?: Json | null
          created_at?: string
          created_by?: string | null
          cron_schedule?: string
          id?: string
          is_active?: boolean
          last_run?: string | null
          next_run?: string | null
          updated_at?: string
          workflow_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "workflow_schedules_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_configurations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workflow_schedules_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_execution_stats"
            referencedColumns: ["workflow_id"]
          },
        ]
      }
      workflow_telemetry: {
        Row: {
          created_at: string | null
          event_data: Json | null
          event_type: string
          id: string
          ip_address: unknown | null
          session_id: string | null
          user_agent: string | null
          user_id: string
          workflow_id: string | null
        }
        Insert: {
          created_at?: string | null
          event_data?: Json | null
          event_type: string
          id?: string
          ip_address?: unknown | null
          session_id?: string | null
          user_agent?: string | null
          user_id: string
          workflow_id?: string | null
        }
        Update: {
          created_at?: string | null
          event_data?: Json | null
          event_type?: string
          id?: string
          ip_address?: unknown | null
          session_id?: string | null
          user_agent?: string | null
          user_id?: string
          workflow_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "workflow_telemetry_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_configurations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workflow_telemetry_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_execution_stats"
            referencedColumns: ["workflow_id"]
          },
        ]
      }
      workflow_triggers: {
        Row: {
          created_at: string
          created_by: string | null
          id: string
          is_active: boolean
          trigger_config: Json
          trigger_type: string
          updated_at: string
          workflow_id: string | null
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          id?: string
          is_active?: boolean
          trigger_config: Json
          trigger_type: string
          updated_at?: string
          workflow_id?: string | null
        }
        Update: {
          created_at?: string
          created_by?: string | null
          id?: string
          is_active?: boolean
          trigger_config?: Json
          trigger_type?: string
          updated_at?: string
          workflow_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "workflow_triggers_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_configurations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workflow_triggers_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_execution_stats"
            referencedColumns: ["workflow_id"]
          },
        ]
      }
      workflow_webhooks: {
        Row: {
          created_at: string
          created_by: string
          description: string | null
          id: string
          is_active: boolean
          last_triggered: string | null
          trigger_count: number
          updated_at: string
          webhook_secret: string
          webhook_url: string
          workflow_id: string
        }
        Insert: {
          created_at?: string
          created_by: string
          description?: string | null
          id?: string
          is_active?: boolean
          last_triggered?: string | null
          trigger_count?: number
          updated_at?: string
          webhook_secret: string
          webhook_url: string
          workflow_id: string
        }
        Update: {
          created_at?: string
          created_by?: string
          description?: string | null
          id?: string
          is_active?: boolean
          last_triggered?: string | null
          trigger_count?: number
          updated_at?: string
          webhook_secret?: string
          webhook_url?: string
          workflow_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "workflow_webhooks_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_configurations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workflow_webhooks_workflow_id_fkey"
            columns: ["workflow_id"]
            isOneToOne: false
            referencedRelation: "workflow_execution_stats"
            referencedColumns: ["workflow_id"]
          },
        ]
      }
    }
    Views: {
      due_scheduled_reports: {
        Row: {
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string | null
          is_active: boolean | null
          last_run_at: string | null
          name: string | null
          next_run_at: string | null
          output_format: string | null
          parameters: Json | null
          query_template: string | null
          recipients: Json | null
          schedule_config: Json | null
          template_id: string | null
          template_name: string | null
          updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "scheduled_reports_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "report_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      feature_usage_summary: {
        Row: {
          description: string | null
          enabled: boolean | null
          feature_name: string | null
          granted_accesses: number | null
          last_accessed: string | null
          pilot_accesses: number | null
          pilot_enabled: boolean | null
          rollout_percentage: number | null
          total_accesses: number | null
          unique_users: number | null
        }
        Relationships: []
      }
      my_report_history: {
        Row: {
          access_count: number | null
          download_url: string | null
          expires_at: string | null
          file_size: number | null
          format: string | null
          generated_at: string | null
          id: string | null
          name: string | null
          schedule_name: string | null
          status: string | null
          template_name: string | null
        }
        Relationships: []
      }
      report_generation_queue: {
        Row: {
          format: string | null
          generated_at: string | null
          generated_by: string | null
          id: string | null
          name: string | null
          parameters_used: Json | null
          query_template: string | null
          status: string | null
          template_name: string | null
          user_email: string | null
        }
        Relationships: []
      }
      workflow_execution_stats: {
        Row: {
          avg_duration_ms: number | null
          avg_success_rate: number | null
          failed_executions: number | null
          max_duration_ms: number | null
          min_duration_ms: number | null
          successful_executions: number | null
          total_executions: number | null
          total_failed_nodes: number | null
          workflow_id: string | null
          workflow_name: string | null
        }
        Relationships: []
      }
      workflow_usage_summary: {
        Row: {
          active_users: number | null
          date: string | null
          workflow_errors: number | null
          workflows_created: number | null
          workflows_executed: number | null
          workflows_shared: number | null
          workflows_used: number | null
        }
        Relationships: []
      }
    }
    Functions: {
      check_alert_rate_limit: {
        Args: { p_alert_id: string }
        Returns: boolean
      }
      cleanup_expired_reports: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      execute_due_workflow_schedules: {
        Args: Record<PropertyKey, never>
        Returns: {
          completed_at: string | null
          created_at: string
          created_by: string
          execution_data: Json | null
          id: string
          logs: Json | null
          started_at: string | null
          status: string
          updated_at: string
          workflow_id: string
        }[]
      }
      generate_report: {
        Args: { p_template_id: string; p_parameters?: Json; p_format?: string }
        Returns: string
      }
      generate_webhook_url: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_report_access_url: {
        Args: { report_id: string }
        Returns: string
      }
      has_ai_workflow_access: {
        Args: { user_id: string }
        Returns: boolean
      }
      populate_user_analytics_data: {
        Args: { user_id: string }
        Returns: undefined
      }
      schedule_report: {
        Args: {
          p_name: string
          p_template_id: string
          p_cron_expression: string
          p_parameters?: Json
          p_recipients?: Json
          p_output_format?: string
        }
        Returns: string
      }
      track_report_access: {
        Args: { p_report_id: string }
        Returns: undefined
      }
      track_workflow_event: {
        Args: {
          p_user_id: string
          p_workflow_id: string
          p_event_type: string
          p_event_data?: Json
          p_session_id?: string
        }
        Returns: string
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const