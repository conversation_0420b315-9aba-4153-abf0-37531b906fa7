import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://ueanagtsdavrgolecirg.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVlYW5hZ3RzZGF2cmdvbGVjaXJnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5NTUwMTgsImV4cCI6MjA2NTUzMTAxOH0.WdGLO87BNmttRDr0rZY5m74KEV3TxJVDY0P2PDi2Yno'

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set.')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  }
})

// Helper function to check if a table exists
export async function tableExists(tableName: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);
    
    return !error;
  } catch (error) {
    return false;
  }
}

// Assign orphaned candidates to current user
export async function assignOrphanedCandidates() {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) return;

  try {
    // Find candidates without a user_id
    const { data: orphanedCandidates } = await supabase
      .from('candidates')
      .select('id')
      .is('user_id', null);

    if (orphanedCandidates && orphanedCandidates.length > 0) {
      console.log(`Found ${orphanedCandidates.length} orphaned candidates, assigning to current user`);
      
      const { error } = await supabase
        .from('candidates')
        .update({ user_id: user.id })
        .is('user_id', null);

      if (error) {
        console.error('Error assigning orphaned candidates:', error);
      } else {
        console.log('Successfully assigned orphaned candidates to current user');
      }
    }
  } catch (error) {
    console.error('Error in assignOrphanedCandidates:', error);
  }
}

// Initialize database with required tables and sample data
export async function initializeDatabase() {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) return;
  
  try {
    // Check if profile exists, create if not
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .maybeSingle();
      
    if (!profile) {
      await supabase.from('profiles').insert({
        id: user.id,
        first_name: null,
        last_name: null,
        company: "Your Company",
        role: "Recruiter"
      });
    }
    
    // Check if storage bucket exists and create it if needed
    // Note: This requires admin privileges, so it's better to create buckets
    // through the Supabase dashboard or API with admin rights
    
    // Check if we need to create sample data
    const tables = ['candidates', 'jobs', 'events', 'messages', 'message_templates', 'workflow_configurations'];
    
    for (const table of tables) {
      const exists = await tableExists(table);
      if (!exists) {
        console.log(`Table ${table} does not exist. It should be created via migrations.`);
      }
    }
    
    // Create sample data for testing if needed
    // This is handled by the migration file
    
    // Assign any orphaned candidates to current user
    await assignOrphanedCandidates();
    
  } catch (error) {
    console.error('Error initializing database:', error);
  }
}

// Create a storage bucket for candidate documents
export async function createCandidateDocumentsBucket() {
  try {
    // This requires admin privileges, so it's better to create buckets
    // through the Supabase dashboard
    console.log('Storage buckets should be created through the Supabase dashboard');
  } catch (error) {
    console.error('Error creating storage bucket:', error);
  }
}
