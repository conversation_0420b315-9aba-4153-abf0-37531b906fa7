import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0';
import { parse } from 'https://deno.land/x/cron@v1.0.0/mod.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Helper function to calculate next run time based on cron expression
function calculateNextRun(cronExpression: string, lastRun?: string | null): Date {
  try {
    const cronPattern = parse(cronExpression);
    const now = new Date();
    const baseTime = lastRun ? new Date(lastRun) : now;
    
    // Simple implementation - for more complex cron patterns, use a proper cron library
    const parts = cronExpression.split(' ');
    if (parts.length === 5) {
      const [minute, hour, dayOfMonth, month, dayOfWeek] = parts;
      
      const nextRun = new Date(baseTime);
      
      // Daily at specific time (e.g., "0 9 * * *")
      if (dayOfMonth === '*' && month === '*' && dayOfWeek === '*' && hour !== '*') {
        nextRun.setHours(parseInt(hour, 10));
        nextRun.setMinutes(parseInt(minute, 10));
        nextRun.setSeconds(0);
        nextRun.setMilliseconds(0);
        
        // If the time has already passed today, set to tomorrow
        if (nextRun <= now) {
          nextRun.setDate(nextRun.getDate() + 1);
        }
      }
      // Hourly (e.g., "0 * * * *")
      else if (minute !== '*' && hour === '*') {
        nextRun.setMinutes(parseInt(minute, 10));
        nextRun.setSeconds(0);
        nextRun.setMilliseconds(0);
        
        // If the minute has passed this hour, go to next hour
        if (nextRun <= now) {
          nextRun.setHours(nextRun.getHours() + 1);
        }
      }
      // Every X minutes (e.g., "*/15 * * * *")
      else if (minute.startsWith('*/')) {
        const interval = parseInt(minute.substring(2), 10);
        const currentMinute = nextRun.getMinutes();
        const nextMinute = Math.ceil(currentMinute / interval) * interval;
        
        if (nextMinute >= 60) {
          nextRun.setHours(nextRun.getHours() + 1);
          nextRun.setMinutes(nextMinute - 60);
        } else {
          nextRun.setMinutes(nextMinute);
        }
        nextRun.setSeconds(0);
        nextRun.setMilliseconds(0);
      }
      // Default: run in 1 hour
      else {
        nextRun.setHours(nextRun.getHours() + 1);
      }
      
      return nextRun;
    }
  } catch (error) {
    console.error('Error parsing cron expression:', error);
  }
  
  // Fallback: run in 1 hour
  const fallback = new Date();
  fallback.setHours(fallback.getHours() + 1);
  return fallback;
}

// Main function to process scheduled workflows
async function processScheduledWorkflows(supabase: any) {
  const now = new Date();
  
  // Fetch all active schedules that are due
  const { data: schedules, error: schedulesError } = await supabase
    .from('workflow_schedules')
    .select('*, workflow_configurations(*)')
    .eq('is_active', true)
    .or(`next_run.is.null,next_run.lte.${now.toISOString()}`);

  if (schedulesError) {
    console.error('Error fetching schedules:', schedulesError);
    return { processed: 0, errors: [schedulesError] };
  }

  if (!schedules || schedules.length === 0) {
    console.log('No schedules to process');
    return { processed: 0, errors: [] };
  }

  const errors: any[] = [];
  let processed = 0;

  for (const schedule of schedules) {
    try {
      // Create workflow execution
      const { data: execution, error: executionError } = await supabase
        .from('workflow_executions')
        .insert({
          workflow_id: schedule.workflow_id,
          status: 'pending',
          execution_data: {
            trigger: 'schedule',
            schedule_id: schedule.id,
            context: schedule.context || {}
          },
          created_by: schedule.created_by
        })
        .select()
        .single();

      if (executionError) {
        errors.push({ schedule_id: schedule.id, error: executionError });
        continue;
      }

      // Calculate next run time
      const nextRun = calculateNextRun(schedule.cron_schedule, schedule.last_run);

      // Update schedule with last run and next run
      const { error: updateError } = await supabase
        .from('workflow_schedules')
        .update({
          last_run: now.toISOString(),
          next_run: nextRun.toISOString(),
          updated_at: now.toISOString()
        })
        .eq('id', schedule.id);

      if (updateError) {
        errors.push({ schedule_id: schedule.id, error: updateError });
        continue;
      }

      // Queue workflow execution
      const channel = supabase.channel('workflow-execution-queue');
      await channel.send({
        type: 'broadcast',
        event: 'execute-workflow',
        payload: {
          execution_id: execution.id,
          workflow_id: schedule.workflow_id,
          created_by: schedule.created_by,
          context: execution.execution_data.context
        }
      });

      processed++;
      console.log(`Queued workflow execution for schedule ${schedule.id}`);
    } catch (error) {
      errors.push({ schedule_id: schedule.id, error });
    }
  }

  return { processed, errors };
}

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Verify this is being called by Supabase CRON or with proper auth
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Process scheduled workflows
    const result = await processScheduledWorkflows(supabase);

    return new Response(
      JSON.stringify({
        success: true,
        processed: result.processed,
        errors: result.errors.length,
        message: `Processed ${result.processed} scheduled workflows`,
        timestamp: new Date().toISOString()
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  } catch (error) {
    console.error('Scheduler error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
