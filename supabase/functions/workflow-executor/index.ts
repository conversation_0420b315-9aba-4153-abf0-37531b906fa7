// @ts-expect-error - Deno runtime imports
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Deno global type declarations for TypeScript
declare global {
  namespace Deno {
    export function serve(handler: (req: Request) => Response | Promise<Response>): void;
    export const env: {
      get(key: string): string | undefined;
    };
  }
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Simple workflow execution engine
class WorkflowEngine {
  private supabase: any;
  private workflowId: string;
  private userId: string;
  private nodes: any[];
  private edges: any[];
  private context: any;
  private logs: any[] = [];

  constructor(supabase: any, workflowId: string, userId: string, nodes: any[], edges: any[], context: any = {}) {
    this.supabase = supabase;
    this.workflowId = workflowId;
    this.userId = userId;
    this.nodes = nodes;
    this.edges = edges;
    this.context = context;
  }

  async execute(): Promise<{ success: boolean; logs: any[]; error?: any }> {
    try {
      // Find start node
      const startNode = this.nodes.find(node => node.type === 'trigger');
      if (!startNode) {
        throw new Error('No trigger node found in workflow');
      }

      // Execute workflow starting from trigger
      await this.executeNode(startNode.id);

      return { success: true, logs: this.logs };
    } catch (error) {
      console.error('Workflow execution error:', error);
      return { success: false, logs: this.logs, error };
    }
  }

  private async executeNode(nodeId: string): Promise<void> {
    const node = this.nodes.find(n => n.id === nodeId);
    if (!node) {
      throw new Error(`Node ${nodeId} not found`);
    }

    this.logs.push({
      nodeId,
      status: 'started',
      timestamp: new Date().toISOString(),
      message: `Executing ${node.type} node: ${node.data.label || nodeId}`
    });

    try {
      // Execute node based on type
      let result: any;
      switch (node.type) {
        case 'trigger':
          result = await this.executeTriggerNode(node);
          break;
        case 'action':
          result = await this.executeActionNode(node);
          break;
        case 'condition':
          result = await this.executeConditionNode(node);
          break;
        case 'transformation':
          result = await this.executeTransformationNode(node);
          break;
        default:
          result = { success: true };
      }

      this.logs.push({
        nodeId,
        status: 'completed',
        timestamp: new Date().toISOString(),
        message: `Completed ${node.type} node`,
        data: result
      });

      // Find next nodes to execute
      const outgoingEdges = this.edges.filter(edge => edge.source === nodeId);
      
      // Handle condition nodes differently
      if (node.type === 'condition' && result.branch) {
        const branchEdge = outgoingEdges.find(edge => 
          edge.sourceHandle === result.branch || edge.label === result.branch
        );
        if (branchEdge) {
          await this.executeNode(branchEdge.target);
        }
      } else {
        // Execute all outgoing nodes
        for (const edge of outgoingEdges) {
          await this.executeNode(edge.target);
        }
      }
    } catch (error) {
      this.logs.push({
        nodeId,
        status: 'error',
        timestamp: new Date().toISOString(),
        message: `Error in ${node.type} node: ${error.message}`,
        error
      });
      throw error;
    }
  }

  private async executeTriggerNode(node: any): Promise<any> {
    // Trigger nodes don't perform actions, they just pass context
    return { success: true, context: this.context };
  }

  private async executeActionNode(node: any): Promise<any> {
    const actionType = node.data.actionType;
    const config = node.data.config || {};

    switch (actionType) {
      case 'send_email':
        return await this.sendEmail(config);
      case 'update_status':
        return await this.updateStatus(config);
      case 'send_notification':
        return await this.sendNotification(config);
      default:
        return { success: true, message: `Action ${actionType} executed` };
    }
  }

  private async executeConditionNode(node: any): Promise<any> {
    const { field, operator, value } = node.data.config || {};
    const fieldValue = this.getValueFromContext(field);
    
    let conditionMet = false;
    switch (operator) {
      case 'equals':
        conditionMet = fieldValue == value;
        break;
      case 'not_equals':
        conditionMet = fieldValue != value;
        break;
      case 'contains':
        conditionMet = String(fieldValue).includes(value);
        break;
      case 'greater_than':
        conditionMet = fieldValue > value;
        break;
      case 'less_than':
        conditionMet = fieldValue < value;
        break;
      default:
        conditionMet = true;
    }

    return { 
      success: true, 
      conditionMet,
      branch: conditionMet ? 'true' : 'false'
    };
  }

  private async executeTransformationNode(node: any): Promise<any> {
    const { transformationType, config } = node.data;
    
    switch (transformationType) {
      case 'map_fields': {
        // Simple field mapping
        const mapped = {};
        for (const [from, to] of Object.entries(config.mapping || {})) {
          (mapped as any)[to as string] = this.getValueFromContext(from as string);
        }
        this.context = { ...this.context, ...mapped };
        break;
      }
      
      case 'filter':
        // Filter array data
        if (Array.isArray(this.context.data)) {
          this.context.data = this.context.data.filter(item => {
            // Simple filter logic
            return true;
          });
        }
        break;
    }

    return { success: true, context: this.context };
  }

  private getValueFromContext(path: string): any {
    const parts = path.split('.');
    let value = this.context;
    for (const part of parts) {
      value = value?.[part];
    }
    return value;
  }

  private async sendEmail(config: any): Promise<any> {
    // Placeholder for email sending
    console.log('Sending email:', config);
    return { success: true, message: 'Email sent' };
  }

  private async updateStatus(config: any): Promise<any> {
    // Placeholder for status update
    console.log('Updating status:', config);
    return { success: true, message: 'Status updated' };
  }

  private async sendNotification(config: any): Promise<any> {
    // Placeholder for notification
    console.log('Sending notification:', config);
    return { success: true, message: 'Notification sent' };
  }
}

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { execution_id, workflow_id, user_id, context } = await req.json();

    if (!execution_id || !workflow_id || !user_id) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Update execution status to running
    await supabase
      .from('workflow_executions')
      .update({
        status: 'running',
        started_at: new Date().toISOString()
      })
      .eq('id', execution_id);

    // Fetch workflow configuration
    const { data: workflow, error: workflowError } = await supabase
      .from('workflow_configurations')
      .select('*')
      .eq('id', workflow_id)
      .single();

    if (workflowError || !workflow) {
      throw new Error('Workflow not found');
    }

    // Execute workflow
    const engine = new WorkflowEngine(
      supabase,
      workflow_id,
      user_id,
      workflow.config.nodes || [],
      workflow.config.edges || [],
      context || {}
    );

    const result = await engine.execute();

    // Update execution with results
    await supabase
      .from('workflow_executions')
      .update({
        status: result.success ? 'completed' : 'failed',
        completed_at: new Date().toISOString(),
        logs: result.logs,
        execution_data: {
          ...workflow.execution_data,
          result: result.success ? 'success' : 'failed',
          error: result.error
        }
      })
      .eq('id', execution_id);

    return new Response(
      JSON.stringify({
        success: result.success,
        execution_id,
        logs: result.logs,
        message: result.success ? 'Workflow executed successfully' : 'Workflow execution failed'
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  } catch (error) {
    console.error('Workflow executor error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
