import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0';
import { crypto } from 'https://deno.land/std@0.224.0/crypto/mod.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-webhook-signature',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Helper function to verify webhook signature
async function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string
): Promise<boolean> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  
  const expectedSignature = await crypto.subtle.sign(
    'HMAC',
    key,
    encoder.encode(payload)
  );
  
  const expectedHex = Array.from(new Uint8Array(expectedSignature))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
  
  return signature === expectedHex;
}

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get webhook ID from URL path
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/');
    const webhookId = pathParts[pathParts.length - 1];
    
    if (!webhookId || webhookId === 'webhooks') {
      return new Response(
        JSON.stringify({ error: 'Webhook ID is required' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Get request body
    const body = await req.text();
    const signature = req.headers.get('x-webhook-signature');
    
    if (!signature) {
      return new Response(
        JSON.stringify({ error: 'Missing webhook signature' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Fetch webhook configuration
    const { data: webhook, error: webhookError } = await supabase
      .from('workflow_webhooks')
      .select('*, workflow_configurations(*)')
      .eq('webhook_url', webhookId)
      .eq('is_active', true)
      .single();

    if (webhookError || !webhook) {
      console.error('Webhook not found:', webhookError);
      return new Response(
        JSON.stringify({ error: 'Webhook not found or inactive' }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Verify signature
    const isValidSignature = await verifyWebhookSignature(
      body,
      signature,
      webhook.webhook_secret
    );

    if (!isValidSignature) {
      return new Response(
        JSON.stringify({ error: 'Invalid webhook signature' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Parse request body
    let webhookData: any;
    try {
      webhookData = JSON.parse(body);
    } catch (e) {
      webhookData = { raw: body };
    }

    // Create workflow execution
    const { data: execution, error: executionError } = await supabase
      .from('workflow_executions')
      .insert({
        workflow_id: webhook.workflow_id,
        status: 'pending',
        execution_data: {
          trigger: 'webhook',
          webhook_id: webhook.id,
          webhook_data: webhookData,
          context: {
            webhook_url: webhookId,
            ...webhookData
          }
        },
        created_by: webhook.created_by
      })
      .select()
      .single();

    if (executionError) {
      console.error('Failed to create workflow execution:', executionError);
      return new Response(
        JSON.stringify({ error: 'Failed to create workflow execution' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Update webhook statistics
    await supabase
      .from('workflow_webhooks')
      .update({
        last_triggered: new Date().toISOString(),
        trigger_count: webhook.trigger_count + 1
      })
      .eq('id', webhook.id);

    // Queue workflow execution using Supabase Realtime
    const channel = supabase.channel('workflow-execution-queue');
    await channel.send({
      type: 'broadcast',
      event: 'execute-workflow',
      payload: {
        execution_id: execution.id,
        workflow_id: webhook.workflow_id,
        created_by: webhook.created_by,
        context: execution.execution_data.context
      }
    });

    return new Response(
      JSON.stringify({
        success: true,
        execution_id: execution.id,
        message: 'Workflow execution queued successfully'
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  } catch (error) {
    console.error('Webhook handler error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
