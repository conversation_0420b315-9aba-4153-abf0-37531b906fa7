-- Seed script for sample workflows and data
-- This script creates sample data for testing and development

-- First, ensure we have a test user (if not already present)
DO $$
DECLARE
  test_user_id UUID;
BEGIN
  -- Get the first available user or create a test user
  SELECT id INTO test_user_id FROM auth.users LIMIT 1;
  
  -- Only proceed if we have a user
  IF test_user_id IS NOT NULL THEN
    
    -- Create sample workflow configurations
    INSERT INTO public.workflow_configurations (name, description, config, is_active, created_by)
    VALUES 
    (
      'Interview Scheduling Workflow',
      'Automated workflow for scheduling interviews with candidates',
      jsonb_build_object(
        'trigger', jsonb_build_object(
          'type', 'webhook',
          'event', 'candidate_applied'
        ),
        'nodes', jsonb_build_array(
          jsonb_build_object(
            'id', 'node_1',
            'type', 'filter',
            'label', 'Check Resume Score',
            'config', jsonb_build_object(
              'condition', 'resume_score > 80'
            )
          ),
          jsonb_build_object(
            'id', 'node_2',
            'type', 'email',
            'label', 'Send Interview Invitation',
            'config', jsonb_build_object(
              'template_id', 'interview_invitation',
              'to', '{{candidate.email}}',
              'subject', 'Interview Invitation - {{job.title}}'
            )
          ),
          jsonb_build_object(
            'id', 'node_3',
            'type', 'calendar',
            'label', 'Create Calendar Event',
            'config', jsonb_build_object(
              'duration', 60,
              'attendees', jsonb_build_array('{{candidate.email}}', '{{interviewer.email}}')
            )
          )
        ),
        'edges', jsonb_build_array(
          jsonb_build_object('source', 'trigger', 'target', 'node_1'),
          jsonb_build_object('source', 'node_1', 'target', 'node_2', 'condition', 'true'),
          jsonb_build_object('source', 'node_2', 'target', 'node_3')
        )
      ),
      true,
      test_user_id
    ),
    (
      'Candidate Follow-up Workflow',
      'Automated follow-up emails for candidates after interviews',
      jsonb_build_object(
        'trigger', jsonb_build_object(
          'type', 'scheduled',
          'schedule', '0 9 * * *',
          'event', 'interview_completed'
        ),
        'nodes', jsonb_build_array(
          jsonb_build_object(
            'id', 'node_1',
            'type', 'database_query',
            'label', 'Get Recent Interviews',
            'config', jsonb_build_object(
              'query', 'SELECT * FROM events WHERE event_type = ''interview'' AND end_time < now() - interval ''24 hours'' AND end_time > now() - interval ''48 hours'''
            )
          ),
          jsonb_build_object(
            'id', 'node_2',
            'type', 'condition',
            'label', 'Check Follow-up Status',
            'config', jsonb_build_object(
              'condition', 'followup_sent != true'
            )
          ),
          jsonb_build_object(
            'id', 'node_3',
            'type', 'email',
            'label', 'Send Follow-up Email',
            'config', jsonb_build_object(
              'template', 'interview_followup',
              'delay_minutes', 1440
            )
          ),
          jsonb_build_object(
            'id', 'node_4',
            'type', 'update_database',
            'label', 'Mark Follow-up Sent',
            'config', jsonb_build_object(
              'table', 'candidates',
              'set', jsonb_build_object('followup_sent', true)
            )
          )
        ),
        'edges', jsonb_build_array(
          jsonb_build_object('source', 'trigger', 'target', 'node_1'),
          jsonb_build_object('source', 'node_1', 'target', 'node_2'),
          jsonb_build_object('source', 'node_2', 'target', 'node_3', 'condition', 'true'),
          jsonb_build_object('source', 'node_3', 'target', 'node_4')
        )
      ),
      true,
      test_user_id
    ),
    (
      'Job Posting Distribution Workflow',
      'Automatically distribute job postings to multiple platforms',
      jsonb_build_object(
        'trigger', jsonb_build_object(
          'type', 'database',
          'table', 'jobs',
          'event', 'insert'
        ),
        'nodes', jsonb_build_array(
          jsonb_build_object(
            'id', 'node_1',
            'type', 'enrichment',
            'label', 'Generate Job Description',
            'config', jsonb_build_object(
              'ai_model', 'gpt-4',
              'prompt', 'Generate compelling job description for {{job.title}}'
            )
          ),
          jsonb_build_object(
            'id', 'node_2',
            'type', 'parallel',
            'label', 'Post to Job Boards',
            'config', jsonb_build_object(
              'tasks', jsonb_build_array(
                jsonb_build_object('platform', 'linkedin', 'api_endpoint', '/jobs/post'),
                jsonb_build_object('platform', 'indeed', 'api_endpoint', '/listings/create'),
                jsonb_build_object('platform', 'glassdoor', 'api_endpoint', '/jobs/new')
              )
            )
          ),
          jsonb_build_object(
            'id', 'node_3',
            'type', 'notification',
            'label', 'Notify Team',
            'config', jsonb_build_object(
              'channels', jsonb_build_array('slack', 'email'),
              'message', 'New job posting live: {{job.title}}'
            )
          )
        ),
        'edges', jsonb_build_array(
          jsonb_build_object('source', 'trigger', 'target', 'node_1'),
          jsonb_build_object('source', 'node_1', 'target', 'node_2'),
          jsonb_build_object('source', 'node_2', 'target', 'node_3')
        )
      ),
      true,
      test_user_id
    ),
    (
      'Onboarding Workflow',
      'New employee onboarding automation',
      jsonb_build_object(
        'trigger', jsonb_build_object(
          'type', 'manual',
          'event', 'employee_hired'
        ),
        'nodes', jsonb_build_array(
          jsonb_build_object(
            'id', 'node_1',
            'type', 'create_tasks',
            'label', 'Create Onboarding Tasks',
            'config', jsonb_build_object(
              'tasks', jsonb_build_array(
                'Setup email account',
                'Order equipment',
                'Schedule orientation',
                'Assign buddy',
                'Create access badges'
              )
            )
          ),
          jsonb_build_object(
            'id', 'node_2',
            'type', 'email',
            'label', 'Send Welcome Email',
            'config', jsonb_build_object(
              'template', 'welcome_new_employee',
              'attachments', jsonb_build_array('employee_handbook.pdf', 'benefits_guide.pdf')
            )
          ),
          jsonb_build_object(
            'id', 'node_3',
            'type', 'calendar',
            'label', 'Schedule First Day Meeting',
            'config', jsonb_build_object(
              'event_type', 'onboarding',
              'duration', 120,
              'attendees', jsonb_build_array('{{employee.email}}', '{{manager.email}}', '{{hr.email}}')
            )
          ),
          jsonb_build_object(
            'id', 'node_4',
            'type', 'integration',
            'label', 'Add to Payroll System',
            'config', jsonb_build_object(
              'system', 'adp',
              'action', 'create_employee'
            )
          )
        ),
        'edges', jsonb_build_array(
          jsonb_build_object('source', 'trigger', 'target', 'node_1'),
          jsonb_build_object('source', 'node_1', 'target', 'node_2'),
          jsonb_build_object('source', 'node_2', 'target', 'node_3'),
          jsonb_build_object('source', 'node_3', 'target', 'node_4')
        )
      ),
      true,
      test_user_id
    )
    ON CONFLICT (id) DO NOTHING;

    -- Create sample message templates
    INSERT INTO public.message_templates (name, subject, content, template_category, user_id)
    VALUES 
    (
      'Interview Invitation',
      'Interview Invitation - {{position}}',
      E'Dear {{candidate_name}},\n\nWe are pleased to invite you for an interview for the {{position}} position at our company.\n\nPlease let us know your availability for next week.\n\nBest regards,\n{{recruiter_name}}',
      'interview',
      test_user_id
    ),
    (
      'Interview Follow-up',
      'Thank you for your interview',
      E'Dear {{candidate_name}},\n\nThank you for taking the time to interview with us for the {{position}} role.\n\nWe will be in touch within the next few days with our decision.\n\nBest regards,\n{{recruiter_name}}',
      'follow_up',
      test_user_id
    ),
    (
      'Job Offer',
      'Job Offer - {{position}}',
      E'Dear {{candidate_name}},\n\nWe are delighted to offer you the position of {{position}} at our company.\n\nPlease find attached the offer letter with all the details.\n\nWe look forward to having you on our team!\n\nBest regards,\n{{hr_manager_name}}',
      'offer',
      test_user_id
    ),
    (
      'Application Received',
      'Application Received - {{position}}',
      E'Dear {{candidate_name}},\n\nThank you for your interest in the {{position}} position.\n\nWe have received your application and will review it carefully. You can expect to hear from us within 5-7 business days.\n\nBest regards,\n{{company_name}} Recruitment Team',
      'general',
      test_user_id
    )
    ON CONFLICT (id) DO NOTHING;

    -- Create sample events
    INSERT INTO public.events (
      title, description, start_time, end_time, location, meeting_link,
      event_type, priority, category, user_id
    )
    VALUES 
    (
      'Interview - John Doe - Senior Developer',
      'Technical interview with John Doe for the Senior Developer position',
      now() + interval '2 days' + time '14:00:00',
      now() + interval '2 days' + time '15:00:00',
      'Conference Room A',
      'https://meet.google.com/abc-defg-hij',
      'interview',
      'high',
      'recruitment',
      test_user_id
    ),
    (
      'Team Sync - Recruitment Pipeline Review',
      'Weekly review of current candidates and open positions',
      now() + interval '1 day' + time '10:00:00',
      now() + interval '1 day' + time '11:00:00',
      'Virtual',
      'https://zoom.us/j/*********',
      'meeting',
      'medium',
      'internal',
      test_user_id
    ),
    (
      'Client Meeting - New Position Requirements',
      'Discuss requirements for new Data Scientist position',
      now() + interval '3 days' + time '15:30:00',
      now() + interval '3 days' + time '16:30:00',
      'Client Office',
      NULL,
      'meeting',
      'high',
      'client',
      test_user_id
    )
    ON CONFLICT (id) DO NOTHING;

  END IF;
END $$;

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_workflow_config_active ON public.workflow_configurations(is_active);
CREATE INDEX IF NOT EXISTS idx_workflow_config_created_at ON public.workflow_configurations(created_at DESC);

-- Add helpful comments
COMMENT ON TABLE public.workflow_configurations IS 'Stores workflow definitions and configurations for automation';
COMMENT ON COLUMN public.workflow_configurations.config IS 'JSON configuration containing trigger, nodes, edges, and other workflow settings';
COMMENT ON TABLE public.message_templates IS 'Email and message templates for recruitment communications';
COMMENT ON TABLE public.events IS 'Calendar events including interviews, meetings, and other scheduled activities';
