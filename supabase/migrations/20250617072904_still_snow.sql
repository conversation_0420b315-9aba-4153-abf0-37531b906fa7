\n\n-- Create messages table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.messages (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  sender_name TEXT NOT NULL,\n  sender_email TEXT NOT NULL,\n  sender_role TEXT,\n  sender_avatar TEXT,\n  content TEXT NOT NULL,\n  status TEXT NOT NULL DEFAULT 'unread', -- 'read', 'unread', 'archived'\n  is_starred BOOLEAN NOT NULL DEFAULT false,\n  follow_up BOOLEAN NOT NULL DEFAULT false,\n  reminder BOOLEAN NOT NULL DEFAULT false,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create message_templates table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.message_templates (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  name TEXT NOT NULL,\n  subject TEXT NOT NULL,\n  content TEXT NOT NULL,\n  template_category TEXT NOT NULL DEFAULT 'general', -- 'interview', 'follow_up', 'rejection', 'offer'\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Enable RLS on all tables\nALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.message_templates ENABLE ROW LEVEL SECURITY;
\n\n-- Drop existing policies if they exist\nDROP POLICY IF EXISTS "Users can manage their own messages" ON public.messages;
\nDROP POLICY IF EXISTS "Users can manage their own message templates" ON public.message_templates;
\n\n-- Create RLS policies for messages\nCREATE POLICY "Users can manage their own messages"\n  ON public.messages\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\n-- Create RLS policies for message_templates\nCREATE POLICY "Users can manage their own message templates"\n  ON public.message_templates\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\n-- Create indexes for better performance\nCREATE INDEX IF NOT EXISTS idx_messages_user_id ON public.messages(user_id);
\nCREATE INDEX IF NOT EXISTS idx_messages_status ON public.messages(status);
\nCREATE INDEX IF NOT EXISTS idx_message_templates_user_id ON public.message_templates(user_id);
\nCREATE INDEX IF NOT EXISTS idx_message_templates_category ON public.message_templates(template_category);
;
