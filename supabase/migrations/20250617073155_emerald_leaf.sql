\n\n-- Create analytics_metrics table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.analytics_metrics (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  metric_name TEXT NOT NULL,\n  metric_value NUMERIC NOT NULL,\n  change_percentage NUMERIC,\n  period TEXT NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create analytics_applications table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.analytics_applications (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  period TEXT NOT NULL,\n  applications INTEGER NOT NULL,\n  interviews INTEGER,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create analytics_skills table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.analytics_skills (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  skill TEXT NOT NULL,\n  current_level INTEGER NOT NULL,\n  required_level INTEGER NOT NULL,\n  gap INTEGER NOT NULL,\n  recommendation TEXT,\n  success_impact INTEGER,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create analytics_sources table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.analytics_sources (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  source TEXT NOT NULL,\n  effectiveness INTEGER NOT NULL,\n  hires INTEGER NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create analytics_diversity table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.analytics_diversity (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  category TEXT NOT NULL,\n  value INTEGER NOT NULL,\n  total INTEGER NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create analytics_salary table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.analytics_salary (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  role TEXT NOT NULL,\n  market_salary NUMERIC NOT NULL,\n  recommended_salary NUMERIC NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Enable RLS on all tables\nALTER TABLE public.analytics_metrics ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.analytics_applications ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.analytics_skills ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.analytics_sources ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.analytics_diversity ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.analytics_salary ENABLE ROW LEVEL SECURITY;
\n\n-- Drop existing policies if they exist and create new ones\nDROP POLICY IF EXISTS "Users can manage their own analytics_metrics" ON public.analytics_metrics;
\nDROP POLICY IF EXISTS "Users can manage their own analytics_applications" ON public.analytics_applications;
\nDROP POLICY IF EXISTS "Users can manage their own analytics_skills" ON public.analytics_skills;
\n\n-- Create RLS policies\nCREATE POLICY "Users can manage their own analytics_metrics"\n  ON public.analytics_metrics\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can manage their own analytics_applications"\n  ON public.analytics_applications\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can manage their own analytics_skills"\n  ON public.analytics_skills\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\n-- Drop existing policies if they exist first\nDROP POLICY IF EXISTS "Users can manage their own analytics_sources" ON public.analytics_sources;
\nDROP POLICY IF EXISTS "Users can manage their own analytics_diversity" ON public.analytics_diversity;
\nDROP POLICY IF EXISTS "Users can manage their own analytics_salary" ON public.analytics_salary;
\n\nCREATE POLICY "Users can manage their own analytics_sources"\n  ON public.analytics_sources\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can manage their own analytics_diversity"\n  ON public.analytics_diversity\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can manage their own analytics_salary"\n  ON public.analytics_salary\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\n-- Create indexes for better performance\nCREATE INDEX IF NOT EXISTS idx_analytics_metrics_user_id ON public.analytics_metrics(user_id);
\nCREATE INDEX IF NOT EXISTS idx_analytics_applications_user_id ON public.analytics_applications(user_id);
\nCREATE INDEX IF NOT EXISTS idx_analytics_skills_user_id ON public.analytics_skills(user_id);
\nCREATE INDEX IF NOT EXISTS idx_analytics_sources_user_id ON public.analytics_sources(user_id);
\nCREATE INDEX IF NOT EXISTS idx_analytics_diversity_user_id ON public.analytics_diversity(user_id);
\nCREATE INDEX IF NOT EXISTS idx_analytics_salary_user_id ON public.analytics_salary(user_id);
;
