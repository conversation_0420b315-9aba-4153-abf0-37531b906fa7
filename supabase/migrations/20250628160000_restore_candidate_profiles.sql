\n\n-- Function to restore candidate profiles for the current user\nDO $$ \nDECLARE\n    current_user_id UUID;
\nBEGIN\n    -- Get the current user ID\n    current_user_id := auth.uid();
\n    \n    -- Only proceed if we have a valid user\n    IF current_user_id IS NOT NULL THEN\n        RAISE NOTICE 'Restoring candidate profiles for user: %', current_user_id;
\n        \n        -- Insert the original candidate profiles with proper user association\n        -- Only insert if no candidates exist for this user\n        IF NOT EXISTS (SELECT 1 FROM public.candidates WHERE user_id = current_user_id LIMIT 1) THEN\n            INSERT INTO public.candidates (\n                name, role, email, phone, location, avatar, recruiter_id, recruiter_name, recruiter_avatar,\n                tags, github_url, linkedin_url, twitter_url, relationship_score, experience, industry,\n                remote_preference, visa_status, skills, ai_summary, user_id\n            ) VALUES \n            (\n                '<PERSON>', 'Senior Frontend Developer', '<EMAIL>', '+****************',\n                'San Francisco, CA', '/placeholder.svg', 'rec1', '<PERSON>', '/placeholder.svg',\n                ARRAY['React', 'TypeScript', 'Next.js', 'TailwindCSS'], 'https://github.com/sarahj',\n                'https://linkedin.com/in/sarahj', 'https://twitter.com/sarahj', 85, '8 years', 'Technology',\n                'Hybrid', 'US Citizen', '[\n                    {"name": "React", "level": "expert", "years": 6},\n                    {"name": "TypeScript", "level": "expert", "years": 5},\n                    {"name": "Next.js", "level": "intermediate", "years": 3},\n                    {"name": "TailwindCSS", "level": "expert", "years": 4}\n                ]'::jsonb, 'Experienced frontend developer with strong React and TypeScript skills. Shows great potential for leadership roles.',\n                current_user_id\n            ),\n            (\n                'Michael Chen', 'Full Stack Engineer', '<EMAIL>', '+1 (555) 234-5678',\n                'New York, NY', '/placeholder.svg', 'rec2', 'Emma Wilson', '/placeholder.svg',\n                ARRAY['Node.js', 'React', 'MongoDB', 'AWS'], 'https://github.com/michaelc',\n                'https://linkedin.com/in/michaelc', NULL, 75, '5 years', 'Finance',\n                'Remote', 'H1B', '[\n                    {"name": "Node.js", "level": "expert", "years": 5},\n                    {"name": "React", "level": "intermediate", "years": 3},\n                    {"name": "MongoDB", "level": "intermediate", "years": 4},\n                    {"name": "AWS", "level": "intermediate", "years": 2}\n                ]'::jsonb, 'Versatile full-stack engineer with strong backend experience in Node.js and cloud technologies.',\n                current_user_id\n            ),\n            (\n                'Emily Rodriguez', 'UI/UX Designer', '<EMAIL>', '+1 (555) 345-6789',\n                'Austin, TX', '/placeholder.svg', 'rec3', 'David Kim', '/placeholder.svg',\n                ARRAY['Figma', 'User Research', 'Prototyping', 'Design Systems'], NULL,\n                'https://linkedin.com/in/emilyr', 'https://twitter.com/emilyr', 90, '6 years', 'Design Agency',\n                'On-site', 'US Citizen', '[\n                    {"name": "Figma", "level": "expert", "years": 4},\n                    {"name": "User Research", "level": "expert", "years": 6},\n                    {"name": "Prototyping", "level": "expert", "years": 5},\n                    {"name": "Design Systems", "level": "intermediate", "years": 3}\n                ]'::jsonb, 'Creative UI/UX designer with extensive user research experience and design systems expertise.',\n                current_user_id\n            ),\n            (\n                'David Wilson', 'DevOps Engineer', '<EMAIL>', '+1 (555) 456-7890',\n                'Seattle, WA', '/placeholder.svg', 'rec4', 'Sophie Lee', '/placeholder.svg',\n                ARRAY['Kubernetes', 'Docker', 'AWS', 'CI/CD'], 'https://github.com/davidw',\n                'https://linkedin.com/in/davidw', NULL, 78, '7 years', 'Cloud Computing',\n                'Remote', 'US Citizen', '[\n                    {"name": "Kubernetes", "level": "expert", "years": 5},\n                    {"name": "Docker", "level": "expert", "years": 6},\n                    {"name": "AWS", "level": "expert", "years": 7},\n                    {"name": "CI/CD", "level": "intermediate", "years": 4}\n                ]'::jsonb, 'Expert DevOps engineer specializing in containerization and cloud infrastructure automation.',\n                current_user_id\n            ),\n            (\n                'Lisa Zhang', 'Data Scientist', '<EMAIL>', '+1 (555) 567-8901',\n                'Boston, MA', '/placeholder.svg', 'rec5', 'James Brown', '/placeholder.svg',\n                ARRAY['Python', 'Machine Learning', 'TensorFlow', 'SQL'], 'https://github.com/lisaz',\n                'https://linkedin.com/in/lisaz', NULL, 92, '4 years', 'AI/ML',\n                'Hybrid', 'Green Card', '[\n                    {"name": "Python", "level": "expert", "years": 4},\n                    {"name": "Machine Learning", "level": "expert", "years": 3},\n                    {"name": "TensorFlow", "level": "intermediate", "years": 2},\n                    {"name": "SQL", "level": "expert", "years": 4}\n                ]'::jsonb, 'Talented data scientist with expertise in machine learning and predictive analytics.',\n                current_user_id\n            ),\n            (\n                'Marcus Johnson', 'Backend Developer', '<EMAIL>', '+1 (555) 678-9012',\n                'Chicago, IL', '/placeholder.svg', 'rec6', 'Rachel Green', '/placeholder.svg',\n                ARRAY['Java', 'Spring Boot', 'PostgreSQL', 'Microservices'], 'https://github.com/marcusj',\n                'https://linkedin.com/in/marcusj', NULL, 83, '6 years', 'E-commerce',\n                'Remote', 'US Citizen', '[\n                    {"name": "Java", "level": "expert", "years": 6},\n                    {"name": "Spring Boot", "level": "expert", "years": 4},\n                    {"name": "PostgreSQL", "level": "intermediate", "years": 5},\n                    {"name": "Microservices", "level": "expert", "years": 3}\n                ]'::jsonb, 'Experienced backend developer with strong Java and microservices architecture knowledge.',\n                current_user_id\n            ),\n            (\n                'Sophia Patel', 'Mobile Developer', '<EMAIL>', '+****************',\n                'Los Angeles, CA', '/placeholder.svg', 'rec7', 'Tom Wilson', '/placeholder.svg',\n                ARRAY['React Native', 'iOS', 'Android', 'Mobile Architecture'], 'https://github.com/sophiap',\n                'https://linkedin.com/in/sophiap', NULL, 88, '5 years', 'Mobile Apps',\n                'Hybrid', 'H1B', '[\n                    {"name": "React Native", "level": "expert", "years": 4},\n                    {"name": "iOS", "level": "intermediate", "years": 5},\n                    {"name": "Android", "level": "intermediate", "years": 5},\n                    {"name": "Mobile Architecture", "level": "expert", "years": 3}\n                ]'::jsonb, 'Skilled mobile developer with cross-platform expertise and strong architecture design skills.',\n                current_user_id\n            ),\n            (\n                'Alex Rivera', 'Security Engineer', '<EMAIL>', '+****************',\n                'Miami, FL', '/placeholder.svg', 'rec8', 'Linda Chen', '/placeholder.svg',\n                ARRAY['Cybersecurity', 'Penetration Testing', 'Security Architecture'], 'https://github.com/alexr',\n                'https://linkedin.com/in/alexr', NULL, 86, '7 years', 'Cybersecurity',\n                'Remote', 'US Citizen', '[\n                    {"name": "Cybersecurity", "level": "expert", "years": 7},\n                    {"name": "Penetration Testing", "level": "expert", "years": 5},\n                    {"name": "Security Architecture", "level": "intermediate", "years": 4},\n                    {"name": "Cloud Security", "level": "expert", "years": 3}\n                ]'::jsonb, 'Expert security engineer with comprehensive cybersecurity and penetration testing experience.',\n                current_user_id\n            ),\n            (\n                'Emma Thompson', 'Product Manager', '<EMAIL>', '+1 (555) 901-2345',\n                'Denver, CO', '/placeholder.svg', 'rec9', 'Mark Davis', '/placeholder.svg',\n                ARRAY['Product Strategy', 'Agile', 'User Research', 'Analytics'], NULL,\n                'https://linkedin.com/in/emmat', NULL, 91, '8 years', 'Product Management',\n                'Hybrid', 'US Citizen', '[\n                    {"name": "Product Strategy", "level": "expert", "years": 8},\n                    {"name": "Agile", "level": "expert", "years": 6},\n                    {"name": "User Research", "level": "intermediate", "years": 5},\n                    {"name": "Analytics", "level": "expert", "years": 4}\n                ]'::jsonb, 'Strategic product manager with strong analytical skills and extensive experience in agile methodologies.',\n                current_user_id\n            );
\n            \n            RAISE NOTICE 'Successfully restored % candidate profiles', 9;
\n        ELSE\n            RAISE NOTICE 'Candidates already exist for this user, skipping restore';
\n        END IF;
\n    ELSE\n        RAISE NOTICE 'No authenticated user found, unable to restore profiles';
\n    END IF;
\nEND $$;
\n;
