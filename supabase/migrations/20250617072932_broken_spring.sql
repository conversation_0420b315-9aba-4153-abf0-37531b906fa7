\n\n-- Insert sample events\nDO $$ \nBEGIN\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'events') THEN\n    -- Only insert if no data exists for the current user\n    IF NOT EXISTS (\n      SELECT 1 FROM public.events \n      WHERE user_id = auth.uid() \n      LIMIT 1\n    ) AND EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid()) THEN\n      -- Create events for the next 7 days\n      INSERT INTO public.events (\n        title, description, start_time, end_time, location, event_type, \n        priority, category, user_id\n      )\n      VALUES \n        (\n          'Technical Interview - Frontend Developer', \n          'Interview with candidate <PERSON> for the Frontend Developer position',\n          (CURRENT_DATE + INTERVAL '1 day' + INTERVAL '10 hours')::timestamp with time zone,\n          (CURRENT_DATE + INTERVAL '1 day' + INTERVAL '11 hours')::timestamp with time zone,\n          'Conference Room A', \n          'interview', \n          'high', \n          'recruitment', \n          auth.uid()\n        ),\n        (\n          'Team Standup', \n          'Daily team standup meeting',\n          (CURRENT_DATE + INTERVAL '2 days' + INTERVAL '9 hours')::timestamp with time zone,\n          (CURRENT_DATE + INTERVAL '2 days' + INTERVAL '9 hours' + INTERVAL '30 minutes')::timestamp with time zone,\n          NULL, \n          'meeting', \n          'medium', \n          'internal', \n          auth.uid()\n        ),\n        (\n          'Product Demo', \n          'Demo of new recruitment features to the client',\n          (CURRENT_DATE + INTERVAL '3 days' + INTERVAL '14 hours')::timestamp with time zone,\n          (CURRENT_DATE + INTERVAL '3 days' + INTERVAL '15 hours')::timestamp with time zone,\n          'Main Conference Room', \n          'presentation', \n          'high', \n          'client', \n          auth.uid()\n        ),\n        (\n          'Candidate Follow-up Call', \n          'Follow up with Sarah Miller about the Product Manager position',\n          (CURRENT_DATE + INTERVAL '4 days' + INTERVAL '11 hours')::timestamp with time zone,\n          (CURRENT_DATE + INTERVAL '4 days' + INTERVAL '11 hours' + INTERVAL '30 minutes')::timestamp with time zone,\n          NULL, \n          'call', \n          'medium', \n          'recruitment', \n          auth.uid()\n        ),\n        (\n          'Weekly Planning', \n          'Team planning session for upcoming recruitment activities',\n          (CURRENT_DATE + INTERVAL '5 days' + INTERVAL '13 hours')::timestamp with time zone,\n          (CURRENT_DATE + INTERVAL '5 days' + INTERVAL '14 hours')::timestamp with time zone,\n          'Meeting Room B', \n          'meeting', \n          'medium', \n          'internal', \n          auth.uid()\n        );
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Enable RLS on analytics tables\nALTER TABLE public.analytics_metrics ENABLE ROW LEVEL SECURITY;
\n\n-- Drop existing analytics policies if they exist\nDROP POLICY IF EXISTS "Users can manage their own analytics_metrics" ON public.analytics_metrics;
\n\n-- Create RLS policies for analytics_metrics\nCREATE POLICY "Users can manage their own analytics_metrics"\n  ON public.analytics_metrics\n    FOR ALL\n    TO authenticated\n    USING (auth.uid() = user_id)\n    WITH CHECK (auth.uid() = user_id);
;
