\n-- Create user profiles table\nCREATE TABLE public.profiles (\n  id UUID NOT NULL REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,\n  first_name TEXT,\n  last_name TEXT,\n  company TEXT,\n  role TEXT,\n  avatar_url TEXT,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Add RLS policies for profiles\nALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
\n\nCREATE POLICY "Users can view their own profile" \n  ON public.profiles \n  FOR SELECT \n  USING (auth.uid() = id);
\n\nCREATE POLICY "Users can update their own profile" \n  ON public.profiles \n  FOR UPDATE \n  USING (auth.uid() = id);
\n\nCREATE POLICY "Users can insert their own profile" \n  ON public.profiles \n  FOR INSERT \n  WITH CHECK (auth.uid() = id);
\n\n-- Create trigger to automatically create profile when user signs up\nCREATE OR REPLACE FUNCTION public.handle_new_user()\nRETURNS trigger\nLANGUAGE plpgsql\nSECURITY DEFINER SET search_path = ''\nAS $$\nBEGIN\n  INSERT INTO public.profiles (id, first_name, last_name)\n  VALUES (\n    new.id, \n    new.raw_user_meta_data ->> 'first_name',\n    new.raw_user_meta_data ->> 'last_name'\n  );
\n  RETURN new;
\nEND;
\n$$;
\n\nCREATE TRIGGER on_auth_user_created\n  AFTER INSERT ON auth.users\n  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
\n;
