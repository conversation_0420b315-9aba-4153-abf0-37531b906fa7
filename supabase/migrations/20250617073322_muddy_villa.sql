\n\n-- Insert sample messages\nDO $$ \nBEGIN\n  -- Only insert if no data exists for the current user\n  IF NOT EXISTS (\n    SELECT 1 FROM public.messages \n    WHERE user_id = auth.uid() \n    LIMIT 1\n  ) AND EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid()) THEN\n    INSERT INTO public.messages (\n      sender_name, sender_email, sender_role, sender_avatar, content, \n      status, is_starred, follow_up, reminder, user_id\n    )\n    VALUES \n      (\n        '<PERSON>', \n        '<EMAIL>', \n        'Hiring Manager', \n        'https://i.pravatar.cc/150?u=1',\n        'Hello, I wanted to discuss the Frontend Developer position. We have several candidates that look promising.',\n        'unread', \n        false, \n        false, \n        false, \n        auth.uid()\n      ),\n      (\n        '<PERSON>', \n        '<EMAIL>', \n        'Senior Developer', \n        'https://i.pravatar.cc/150?u=2',\n        'I''ve reviewed the technical assessment for <PERSON>. He did very well on the coding challenge.',\n        'read', \n        true, \n        true, \n        false, \n        auth.uid()\n      ),\n      (\n        '<PERSON>', \n        '<EMAIL>', \n        'HR Manager', \n        'https://i.pravatar.cc/150?u=3',\n        'We need to schedule interviews for the Product Manager position by next week. Can you help coordinate?',\n        'unread', \n        false, \n        false, \n        true, \n        auth.uid()\n      ),\n      (\n        'David Wilson', \n        '<EMAIL>', \n        'CTO', \n        'https://i.pravatar.cc/150?u=4',\n        'I''d like to discuss our hiring strategy for Q3. Can we schedule a meeting next week to go over the plans?',\n        'unread', \n        false, \n        false, \n        false, \n        auth.uid()\n      ),\n      (\n        'Lisa Zhang', \n        '<EMAIL>', \n        'Product Manager', \n        'https://i.pravatar.cc/150?u=5',\n        'The UX Designer role has been open for a while. Do we have any promising candidates in the pipeline?',\n        'read', \n        false, \n        true, \n        false, \n        auth.uid()\n      );
\n  END IF;
\nEND $$;
\n\n-- Insert sample message templates\nDO $$ \nBEGIN\n  -- Only insert if no data exists for the current user\n  IF NOT EXISTS (\n    SELECT 1 FROM public.message_templates \n    WHERE user_id = auth.uid() \n    LIMIT 1\n  ) AND EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid()) THEN\n    INSERT INTO public.message_templates (\n      name, subject, content, template_category, user_id\n    )\n    VALUES \n      (\n        'Interview Invitation', \n        'Interview Invitation for [Position]',\n        'Dear [Name],\\n\\nThank you for your application for the [Position] role at [Company]. We were impressed with your background and would like to invite you for an interview.\\n\\nPlease let me know your availability for next week.\\n\\nBest regards,\\n[Your Name]',\n        'interview', \n        auth.uid()\n      ),\n      (\n        'Follow-up After Interview', \n        'Thank You for Your Interview',\n        'Dear [Name],\\n\\nThank you for taking the time to interview for the [Position] position. It was a pleasure speaking with you and learning more about your skills and experience.\\n\\nWe are currently reviewing all candidates and will be in touch soon with next steps.\\n\\nBest regards,\\n[Your Name]',\n        'follow_up', \n        auth.uid()\n      ),\n      (\n        'Job Offer', \n        'Job Offer - [Position] at [Company]',\n        'Dear [Name],\\n\\nI am pleased to offer you the position of [Position] at [Company]. Based on your experience and skills, we believe you would be a valuable addition to our team.\\n\\nYour starting salary will be [Salary] per year, and you will be eligible for our benefits package including [Benefits].\\n\\nPlease review the attached offer letter for complete details.\\n\\nBest regards,\\n[Your Name]',\n        'offer', \n        auth.uid()\n      ),\n      (\n        'Application Rejection', \n        'Update on Your Application for [Position]',\n        'Dear [Name],\\n\\nThank you for your interest in the [Position] role at [Company] and for taking the time to go through our application process.\\n\\nAfter careful consideration, we have decided to move forward with other candidates whose qualifications better match our current needs.\\n\\nWe appreciate your interest in [Company] and wish you the best in your job search.\\n\\nBest regards,\\n[Your Name]',\n        'rejection', \n        auth.uid()\n      ),\n      (\n        'Technical Assessment', \n        'Technical Assessment for [Position] at [Company]',\n        'Dear [Name],\\n\\nThank you for your continued interest in the [Position] role at [Company].\\n\\nAs the next step in our process, we would like to invite you to complete a technical assessment. This will help us better understand your skills and approach to problem-solving.\\n\\nPlease find the assessment details below:\\n\\n[Assessment Details]\\n\\nYou will have [Duration] to complete this assessment. Please submit your work by [Deadline].\\n\\nIf you have any questions, feel free to reach out.\\n\\nBest regards,\\n[Your Name]',\n        'interview', \n        auth.uid()\n      ),\n      (\n        'Reference Request', \n        'Reference Request for [Name]',\n        'Dear [Reference Name],\\n\\n[Candidate Name] has applied for the [Position] role at [Company] and has provided your name as a reference.\\n\\nWe would greatly appreciate if you could share your insights about [Candidate Name]''s work experience, skills, and professional qualities.\\n\\nPlease let me know when would be a convenient time for a brief call, or you can respond to the attached reference questionnaire.\\n\\nThank you for your assistance.\\n\\nBest regards,\\n[Your Name]',\n        'general', \n        auth.uid()\n      );
\n  END IF;
\nEND $$;
\n\n-- Note: Messages table RLS policies are already handled in workflows_table.sql migration\n-- Note: workflow_triggers table is created in still_smoke.sql migration\n\n-- Ensure RLS is enabled on messages table (safe to run multiple times)\nALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
;
