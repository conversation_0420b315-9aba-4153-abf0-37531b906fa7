\n\n-- Create workflow_configurations table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.workflow_configurations (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  name TEXT NOT NULL,\n  description TEXT,\n  config JSONB NOT NULL,\n  is_active BOOLEAN NOT NULL DEFAULT true,\n  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create workflow_executions table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.workflow_executions (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  workflow_id UUID REFERENCES public.workflow_configurations(id) ON DELETE CASCADE NOT NULL,\n  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'running', 'completed', 'failed'\n  started_at TIMESTAMP WITH TIME ZONE,\n  completed_at TIMESTAMP WITH TIME ZONE,\n  execution_log JSONB,\n  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Enable RLS on workflow tables\nALTER TABLE public.workflow_configurations ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.workflow_executions ENABLE ROW LEVEL SECURITY;
\n\n-- Drop existing workflow policies if they exist\nDROP POLICY IF EXISTS "Users can view their own workflows" ON public.workflow_configurations;
\nDROP POLICY IF EXISTS "Users can create their own workflows" ON public.workflow_configurations;
\nDROP POLICY IF EXISTS "Users can update their own workflows" ON public.workflow_configurations;
\nDROP POLICY IF EXISTS "Users can delete their own workflows" ON public.workflow_configurations;
\nDROP POLICY IF EXISTS "Users can view their own workflow executions" ON public.workflow_executions;
\nDROP POLICY IF EXISTS "Users can create their own workflow executions" ON public.workflow_executions;
\nDROP POLICY IF EXISTS "Users can update their own workflow executions" ON public.workflow_executions;
\n\n-- Create RLS policies for workflow_configurations\nCREATE POLICY "Users can view their own workflows"\n  ON public.workflow_configurations\n  FOR SELECT\n  USING (auth.uid() = created_by);
\n\nCREATE POLICY "Users can create their own workflows"\n  ON public.workflow_configurations\n  FOR INSERT\n  WITH CHECK (auth.uid() = created_by);
\n\nCREATE POLICY "Users can update their own workflows"\n  ON public.workflow_configurations\n  FOR UPDATE\n  USING (auth.uid() = created_by);
\n\nCREATE POLICY "Users can delete their own workflows"\n  ON public.workflow_configurations\n  FOR DELETE\n  USING (auth.uid() = created_by);
\n\n-- Drop existing policies first to avoid conflicts\nDROP POLICY IF EXISTS "Users can view their own workflow executions" ON public.workflow_executions;
\nDROP POLICY IF EXISTS "Users can create their own workflow executions" ON public.workflow_executions;
 \nDROP POLICY IF EXISTS "Users can update their own workflow executions" ON public.workflow_executions;
\n\n-- Create RLS policies for workflow_executions\nCREATE POLICY "Users can view their own workflow executions"\n  ON public.workflow_executions\n  FOR SELECT\n  USING (auth.uid() = created_by);
\n\nCREATE POLICY "Users can create their own workflow executions"\n  ON public.workflow_executions\n  FOR INSERT\n  WITH CHECK (auth.uid() = created_by);
\n\nCREATE POLICY "Users can update their own workflow executions"\n  ON public.workflow_executions\n  FOR UPDATE\n  USING (auth.uid() = created_by);
\n\n-- Create indexes for better performance\nCREATE INDEX IF NOT EXISTS idx_workflow_configurations_created_by ON public.workflow_configurations(created_by);
\nCREATE INDEX IF NOT EXISTS idx_workflow_executions_workflow_id ON public.workflow_executions(workflow_id);
\nCREATE INDEX IF NOT EXISTS idx_workflow_executions_created_by ON public.workflow_executions(created_by);
\nCREATE INDEX IF NOT EXISTS idx_workflow_executions_status ON public.workflow_executions(status);
;
