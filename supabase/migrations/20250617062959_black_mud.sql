\n\n-- Create sample messages if the table exists\nDO $$ \nBEGIN\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'messages') THEN\n    -- Only insert if no messages exist\n    IF NOT EXISTS (SELECT 1 FROM public.messages LIMIT 1) THEN\n      INSERT INTO public.messages (\n        sender_name, sender_email, sender_role, sender_avatar, content, \n        status, is_starred, follow_up, reminder, user_id\n      )\n      SELECT \n        '<PERSON>', '<EMAIL>', 'Hiring Manager', 'https://i.pravatar.cc/150?u=1',\n        'Hello, I wanted to discuss the Frontend Developer position. We have several candidates that look promising.',\n        'unread', false, false, false, auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.messages (\n        sender_name, sender_email, sender_role, sender_avatar, content, \n        status, is_starred, follow_up, reminder, user_id\n      )\n      SELECT \n        'Sarah <PERSON>', '<EMAIL>', 'Senior Developer', 'https://i.pravatar.cc/150?u=2',\n        'I''ve reviewed the technical assessment for Michael Chen. He did very well on the coding challenge.',\n        'read', true, true, false, auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.messages (\n        sender_name, sender_email, sender_role, sender_avatar, content, \n        status, is_starred, follow_up, reminder, user_id\n      )\n      SELECT \n        'Emily Rodriguez', '<EMAIL>', 'HR Manager', 'https://i.pravatar.cc/150?u=3',\n        'We need to schedule interviews for the Product Manager position by next week. Can you help coordinate?',\n        'unread', false, false, true, auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Create sample message templates if the table exists\nDO $$ \nBEGIN\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'message_templates') THEN\n    -- Only insert if no templates exist\n    IF NOT EXISTS (SELECT 1 FROM public.message_templates LIMIT 1) THEN\n      INSERT INTO public.message_templates (\n        name, subject, content, template_category, user_id\n      )\n      SELECT \n        'Interview Invitation', 'Interview Invitation for [Position]',\n        'Dear [Name],\\n\\nThank you for your application for the [Position] role at [Company]. We were impressed with your background and would like to invite you for an interview.\\n\\nPlease let me know your availability for next week.\\n\\nBest regards,\\n[Your Name]',\n        'interview', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.message_templates (\n        name, subject, content, template_category, user_id\n      )\n      SELECT \n        'Follow-up After Interview', 'Thank You for Your Interview',\n        'Dear [Name],\\n\\nThank you for taking the time to interview for the [Position] position. It was a pleasure speaking with you and learning more about your skills and experience.\\n\\nWe are currently reviewing all candidates and will be in touch soon with next steps.\\n\\nBest regards,\\n[Your Name]',\n        'follow_up', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.message_templates (\n        name, subject, content, template_category, user_id\n      )\n      SELECT \n        'Job Offer', 'Job Offer - [Position] at [Company]',\n        'Dear [Name],\\n\\nI am pleased to offer you the position of [Position] at [Company]. Based on your experience and skills, we believe you would be a valuable addition to our team.\\n\\nYour starting salary will be [Salary] per year, and you will be eligible for our benefits package including [Benefits].\\n\\nPlease review the attached offer letter for complete details.\\n\\nBest regards,\\n[Your Name]',\n        'offer', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Create sample events if the table exists\nDO $$ \nBEGIN\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'events') THEN\n    -- Only insert if no events exist\n    IF NOT EXISTS (SELECT 1 FROM public.events LIMIT 1) THEN\n      -- Create events for the next 7 days\n      INSERT INTO public.events (\n        title, description, start_time, end_time, location, event_type, \n        priority, category, user_id\n      )\n      SELECT \n        'Technical Interview - Frontend Developer', 'Interview with candidate John Doe for the Frontend Developer position',\n        (CURRENT_DATE + INTERVAL '1 day' + INTERVAL '10 hours')::timestamp with time zone,\n        (CURRENT_DATE + INTERVAL '1 day' + INTERVAL '11 hours')::timestamp with time zone,\n        'Conference Room A', 'interview', 'high', 'recruitment', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.events (\n        title, description, start_time, end_time, meeting_link, event_type, \n        priority, category, user_id\n      )\n      SELECT \n        'Team Standup', 'Daily team standup meeting',\n        (CURRENT_DATE + INTERVAL '2 days' + INTERVAL '9 hours')::timestamp with time zone,\n        (CURRENT_DATE + INTERVAL '2 days' + INTERVAL '9 hours' + INTERVAL '30 minutes')::timestamp with time zone,\n        'https://meet.example.com/standup', 'meeting', 'medium', 'internal', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.events (\n        title, description, start_time, end_time, location, event_type, \n        priority, category, user_id\n      )\n      SELECT \n        'Product Demo', 'Demo of new recruitment features to the client',\n        (CURRENT_DATE + INTERVAL '3 days' + INTERVAL '14 hours')::timestamp with time zone,\n        (CURRENT_DATE + INTERVAL '3 days' + INTERVAL '15 hours')::timestamp with time zone,\n        'Main Conference Room', 'presentation', 'high', 'client', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Create sample workflow configurations if the table exists\nDO $$ \nBEGIN\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'workflow_configurations') THEN\n    -- Only insert if no workflow configurations exist\n    IF NOT EXISTS (SELECT 1 FROM public.workflow_configurations LIMIT 1) THEN\n      INSERT INTO public.workflow_configurations (\n        name, description, config, is_active, created_by\n      )\n      SELECT \n        'New Candidate Screening', 'Automated workflow for screening new candidates',\n        '{\n          "nodes": [\n            {\n              "id": "new-application-1",\n              "type": "workflowNode",\n              "position": {"x": 100, "y": 100},\n              "data": {\n                "id": "new-application",\n                "originalId": "new-application",\n                "category": "triggers",\n                "type": "trigger",\n                "label": "New Application",\n                "description": "Triggers when a new candidate applies",\n                "config": {\n                  "jobTypes": "all",\n                  "notifyTeam": true\n                }\n              }\n            },\n            {\n              "id": "ai-screen-1",\n              "type": "workflowNode",\n              "position": {"x": 100, "y": 250},\n              "data": {\n                "id": "ai-screen",\n                "originalId": "ai-screen",\n                "category": "actions",\n                "type": "action",\n                "label": "AI Screening",\n                "description": "Screen resume with AI",\n                "config": {\n                  "criteria": "comprehensive",\n                  "minScore": "75"\n                }\n              }\n            },\n            {\n              "id": "skills-match-1",\n              "type": "workflowNode",\n              "position": {"x": 100, "y": 400},\n              "data": {\n                "id": "skills-match",\n                "originalId": "skills-match",\n                "category": "conditions",\n                "type": "condition",\n                "label": "Skills Match",\n                "description": "Check required skills",\n                "config": {\n                  "requiredSkills": "React, TypeScript, Node.js",\n                  "minMatchPercentage": "70",\n                  "considerSimilarSkills": true\n                }\n              }\n            },\n            {\n              "id": "send-email-1",\n              "type": "workflowNode",\n              "position": {"x": 100, "y": 550},\n              "data": {\n                "id": "send-email",\n                "originalId": "send-email",\n                "category": "actions",\n                "type": "action",\n                "label": "Send Email",\n                "description": "Send an automated email",\n                "config": {\n                  "template": "interview",\n                  "customMessage": "We would like to invite you for an interview."\n                }\n              }\n            }\n          ],\n          "edges": [\n            {\n              "id": "edge-1",\n              "source": "new-application-1",\n              "target": "ai-screen-1",\n              "sourceHandle": "source-bottom",\n              "targetHandle": "target-top",\n              "type": "default",\n              "animated": true\n            },\n            {\n              "id": "edge-2",\n              "source": "ai-screen-1",\n              "target": "skills-match-1",\n              "sourceHandle": "source-bottom",\n              "targetHandle": "target-top",\n              "type": "default",\n              "animated": true\n            },\n            {\n              "id": "edge-3",\n              "source": "skills-match-1",\n              "target": "send-email-1",\n              "sourceHandle": "source-bottom",\n              "targetHandle": "target-top",\n              "type": "default",\n              "animated": true\n            }\n          ]\n        }'::jsonb,\n        true, auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.workflow_configurations (\n        name, description, config, is_active, created_by\n      )\n      SELECT \n        'Interview Scheduling', 'Automated workflow for scheduling interviews',\n        '{\n          "nodes": [\n            {\n              "id": "application-status-1",\n              "type": "workflowNode",\n              "position": {"x": 100, "y": 100},\n              "data": {\n                "id": "application-status",\n                "originalId": "application-status",\n                "category": "triggers",\n                "type": "trigger",\n                "label": "Status Change",\n                "description": "Triggers when application status changes",\n                "config": {\n                  "statusType": "approved",\n                  "customStatus": ""\n                }\n              }\n            },\n            {\n              "id": "schedule-interview-1",\n              "type": "workflowNode",\n              "position": {"x": 100, "y": 250},\n              "data": {\n                "id": "schedule-interview",\n                "originalId": "schedule-interview",\n                "category": "actions",\n                "type": "action",\n                "label": "Schedule Interview",\n                "description": "Automatically schedule interviews",\n                "config": {\n                  "interviewType": "technical",\n                  "duration": "60",\n                  "sendCalendarInvite": true\n                }\n              }\n            },\n            {\n              "id": "notify-team-1",\n              "type": "workflowNode",\n              "position": {"x": 100, "y": 400},\n              "data": {\n                "id": "notify-team",\n                "originalId": "notify-team",\n                "category": "actions",\n                "type": "action",\n                "label": "Notify Team",\n                "description": "Send notification to team",\n                "config": {\n                  "channel": "email",\n                  "teamMembers": "hiring-managers",\n                  "customMessage": "New interview scheduled"\n                }\n              }\n            }\n          ],\n          "edges": [\n            {\n              "id": "edge-1",\n              "source": "application-status-1",\n              "target": "schedule-interview-1",\n              "sourceHandle": "source-bottom",\n              "targetHandle": "target-top",\n              "type": "default",\n              "animated": true\n            },\n            {\n              "id": "edge-2",\n              "source": "schedule-interview-1",\n              "target": "notify-team-1",\n              "sourceHandle": "source-bottom",\n              "targetHandle": "target-top",\n              "type": "default",\n              "animated": true\n            }\n          ]\n        }'::jsonb,\n        true, auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Create sample candidates if the table exists but is empty\nDO $$ \nBEGIN\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'candidates') THEN\n    -- Only insert if no candidates exist\n    IF NOT EXISTS (SELECT 1 FROM public.candidates LIMIT 1) THEN\n      INSERT INTO public.candidates (\n        name, role, email, phone, location, tags, experience, industry, \n        remote_preference, visa_status, skills, relationship_score, ai_summary, user_id\n      )\n      SELECT \n        'Alex Johnson', 'Senior Frontend Developer', '<EMAIL>', '+****************',\n        'San Francisco, CA', ARRAY['React', 'TypeScript', 'Next.js'], '7 years', 'Technology',\n        'Remote', 'US Citizen', \n        '[{"name": "React", "level": "expert", "years": 6}, {"name": "TypeScript", "level": "expert", "years": 5}, {"name": "Next.js", "level": "advanced", "years": 3}]'::jsonb,\n        85, 'Experienced frontend developer with strong React and TypeScript skills.', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.candidates (\n        name, role, email, phone, location, tags, experience, industry, \n        remote_preference, visa_status, skills, relationship_score, ai_summary, user_id\n      )\n      SELECT \n        'Maria Garcia', 'Product Manager', '<EMAIL>', '+****************',\n        'New York, NY', ARRAY['Product Strategy', 'Agile', 'User Research'], '5 years', 'SaaS',\n        'Hybrid', 'US Citizen', \n        '[{"name": "Product Strategy", "level": "expert", "years": 5}, {"name": "Agile", "level": "expert", "years": 4}, {"name": "User Research", "level": "intermediate", "years": 3}]'::jsonb,\n        92, 'Strategic product manager with strong analytical background.', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Create sample jobs if the table exists but is empty\nDO $$ \nBEGIN\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'jobs') THEN\n    -- Only insert if no jobs exist\n    IF NOT EXISTS (SELECT 1 FROM public.jobs LIMIT 1) THEN\n      INSERT INTO public.jobs (\n        title, department, location, job_type, salary_range, experience_required,\n        description, requirements, benefits, is_urgent, is_active, applicant_count, user_id\n      )\n      SELECT \n        'Senior Frontend Developer', 'Engineering', 'San Francisco, CA', 'Full-time',\n        '$120,000 - $160,000', '5+ years',\n        'We are looking for a Senior Frontend Developer to join our growing engineering team. You will be responsible for building user interfaces for our web applications using modern JavaScript frameworks.',\n        ARRAY['5+ years of experience with React', 'Strong TypeScript skills', 'Experience with state management', 'Understanding of web performance optimization'],\n        ARRAY['Health insurance', 'Unlimited PTO', 'Remote work options', '401(k) matching'],\n        true, true, 12, auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.jobs (\n        title, department, location, job_type, salary_range, experience_required,\n        description, requirements, benefits, is_urgent, is_active, applicant_count, user_id\n      )\n      SELECT \n        'Product Manager', 'Product', 'Remote', 'Full-time',\n        '$110,000 - $140,000', '3+ years',\n        'We are seeking a Product Manager to help define and execute our product strategy. You will work closely with engineering, design, and business stakeholders to deliver features that delight our users.',\n        ARRAY['3+ years of product management experience', 'Experience with B2B SaaS products', 'Data-driven approach to decision making', 'Strong communication skills'],\n        ARRAY['Health insurance', 'Flexible work hours', 'Professional development budget', 'Stock options'],\n        false, true, 8, auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Create storage bucket for candidate documents if it doesn't exist\nDO $$ \nBEGIN\n  -- This is a placeholder since we can't directly create storage buckets in SQL\n  -- The bucket should be created through the Supabase dashboard or API\n  NULL;
\nEND $$;
;
