\n-- Drop all problematic triggers and functions first\nDROP TRIGGER IF EXISTS jobs_search_vector_update ON public.jobs;
\nDROP TRIGGER IF EXISTS trigger_update_active_jobs ON public.jobs;
\nDROP TRIGGER IF EXISTS update_jobs_updated_at ON public.jobs;
\nDROP TRIGGER IF EXISTS jobs_applicant_count_trigger ON public.jobs;
\n\nDROP FUNCTION IF EXISTS update_active_jobs() CASCADE;
\nDROP FUNCTION IF EXISTS update_jobs_search_vector() CASCADE;
\n\n-- Drop problematic analytics table constraints\nDROP TABLE IF EXISTS analytics_metrics CASCADE;
\n\n-- Recreate analytics_metrics table with proper constraints\nCREATE TABLE IF NOT EXISTS analytics_metrics (\n  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  metric_name text NOT NULL,\n  metric_value numeric NOT NULL DEFAULT 0,\n  change_percentage numeric DEFAULT 0,\n  period text NOT NULL DEFAULT 'week',\n  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,\n  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,\n  UNIQUE(user_id, metric_name, period)\n);
\n\n-- Enable RLS\nALTER TABLE analytics_metrics ENABLE ROW LEVEL SECURITY;
\n\n-- Create analytics policies\nCREATE POLICY "Users can view their own analytics" ON analytics_metrics\n  FOR SELECT USING (auth.uid() = user_id);
\n\nCREATE POLICY "Users can insert their own analytics" ON analytics_metrics\n  FOR INSERT WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can update their own analytics" ON analytics_metrics\n  FOR UPDATE USING (auth.uid() = user_id);
\n\n-- Recreate the search vector function for jobs\nCREATE OR REPLACE FUNCTION update_jobs_search_vector()\nRETURNS trigger AS $$\nBEGIN\n  NEW.search_vector := \n    setweight(to_tsvector('english', COALESCE(NEW.title, '')), 'A') ||\n    setweight(to_tsvector('english', COALESCE(NEW.department, '')), 'B') ||\n    setweight(to_tsvector('english', COALESCE(NEW.location, '')), 'B') ||\n    setweight(to_tsvector('english', COALESCE(NEW.description, '')), 'C') ||\n    setweight(to_tsvector('english', COALESCE(NEW.job_type, '')), 'C') ||\n    setweight(to_tsvector('english', COALESCE(NEW.experience_required, '')), 'C');
\n  RETURN NEW;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Recreate the active jobs function without ON CONFLICT issues\nCREATE OR REPLACE FUNCTION update_active_jobs()\nRETURNS TRIGGER AS $$\nDECLARE\n  active_count INTEGER;
\n  prev_week_count INTEGER;
\n  change_percentage NUMERIC;
\nBEGIN\n  -- Get current count of active jobs\n  SELECT COUNT(*) INTO active_count\n  FROM jobs\n  WHERE user_id = NEW.user_id\n    AND is_active = true;
\n  \n  -- Get count from previous week\n  SELECT COUNT(*) INTO prev_week_count\n  FROM jobs\n  WHERE user_id = NEW.user_id\n    AND is_active = true\n    AND created_at < (now() - interval '1 week');
\n  \n  -- Calculate change percentage\n  IF prev_week_count > 0 THEN\n    change_percentage := ((active_count - prev_week_count)::NUMERIC / prev_week_count) * 100;
\n  ELSE\n    change_percentage := 100;
 -- If no previous data, assume 100% increase\n  END IF;
\n  \n  -- Check if record exists and update or insert\n  IF EXISTS (SELECT 1 FROM analytics_metrics WHERE user_id = NEW.user_id AND metric_name = 'active_jobs' AND period = 'week') THEN\n    UPDATE analytics_metrics \n    SET \n      metric_value = active_count,\n      change_percentage = change_percentage,\n      updated_at = now()\n    WHERE user_id = NEW.user_id \n      AND metric_name = 'active_jobs' \n      AND period = 'week';
\n  ELSE\n    INSERT INTO analytics_metrics (\n      user_id, metric_name, metric_value, change_percentage, period\n    ) VALUES (\n      NEW.user_id, 'active_jobs', active_count, change_percentage, 'week'\n    );
\n  END IF;
\n  \n  RETURN NEW;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Recreate triggers with proper naming\nCREATE TRIGGER jobs_search_vector_update\n  BEFORE INSERT OR UPDATE ON public.jobs\n  FOR EACH ROW EXECUTE FUNCTION update_jobs_search_vector();
\n\nCREATE TRIGGER trigger_update_active_jobs\n  AFTER INSERT OR UPDATE ON public.jobs\n  FOR EACH ROW EXECUTE FUNCTION update_active_jobs();
\n\nCREATE TRIGGER update_jobs_updated_at \n  BEFORE UPDATE ON public.jobs\n  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
\n\n-- Ensure jobs table has all required indexes\nCREATE INDEX IF NOT EXISTS idx_jobs_search_vector ON public.jobs USING gin(search_vector);
\nCREATE INDEX IF NOT EXISTS idx_analytics_metrics_user_metric ON analytics_metrics(user_id, metric_name, period);
\n;
