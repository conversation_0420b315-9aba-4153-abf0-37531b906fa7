/*
  # Create All Remaining Missing Tables for Hardcoded Data

  1. New Tables
    - integrations: Store integration configurations
    - compliance_data: Store compliance metrics and tracking
    - budget_data: Store budget categories and spending
    - retention_predictions: Store AI-predicted retention risk data
    - hiring_trends: Store predicted hiring trends data

  2. Security
    - Enable RLS on all tables
    - Add policies for user-specific access
*/

-- Create integrations table
CREATE TABLE IF NOT EXISTS public.integrations (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  name text NOT NULL,
  description text,
  category text NOT NULL CHECK (category IN ('communication', 'calendar', 'database', 'analytics', 'other')),
  status text NOT NULL CHECK (status IN ('connected', 'available', 'coming_soon')) DEFAULT 'available',
  settings jsonb DEFAULT '{}',
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create compliance_data table
CREATE TABLE IF NOT EXISTS public.compliance_data (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  overall_score integer DEFAULT 0 CHECK (overall_score >= 0 AND overall_score <= 100),
  last_updated timestamp with time zone DEFAULT now(),
  documentation_compliance numeric DEFAULT 0,
  equal_opportunity numeric DEFAULT 0,
  interview_process numeric DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create budget_data table
CREATE TABLE IF NOT EXISTS public.budget_data (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  total_budget numeric DEFAULT 0,
  spent_amount numeric DEFAULT 0,
  category text NOT NULL,
  amount numeric DEFAULT 0,
  percentage numeric DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create retention_predictions table
CREATE TABLE IF NOT EXISTS public.retention_predictions (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  department text NOT NULL,
  score integer DEFAULT 0 CHECK (score >= 0 AND score <= 100),
  risk_level text NOT NULL CHECK (risk_level IN ('Low', 'Medium', 'High')),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create hiring_trends table
CREATE TABLE IF NOT EXISTS public.hiring_trends (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  period text NOT NULL, -- e.g., 'Jun', 'Jul', etc.
  predicted_hires integer DEFAULT 0,
  actual_hires integer DEFAULT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Enable RLS on all tables
ALTER TABLE public.integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.compliance_data ENABLE ROW LEVEL SECURITY;  
ALTER TABLE public.budget_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.retention_predictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.hiring_trends ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for integrations
CREATE POLICY "Users can view their own integrations" ON public.integrations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own integrations" ON public.integrations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own integrations" ON public.integrations
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own integrations" ON public.integrations
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for compliance_data
CREATE POLICY "Users can view their own compliance data" ON public.compliance_data
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own compliance data" ON public.compliance_data
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own compliance data" ON public.compliance_data
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own compliance data" ON public.compliance_data
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for budget_data
CREATE POLICY "Users can view their own budget data" ON public.budget_data
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own budget data" ON public.budget_data
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own budget data" ON public.budget_data
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own budget data" ON public.budget_data
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for retention_predictions
CREATE POLICY "Users can view their own retention predictions" ON public.retention_predictions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own retention predictions" ON public.retention_predictions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own retention predictions" ON public.retention_predictions
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own retention predictions" ON public.retention_predictions
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for hiring_trends
CREATE POLICY "Users can view their own hiring trends" ON public.hiring_trends
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own hiring trends" ON public.hiring_trends
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own hiring trends" ON public.hiring_trends
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own hiring trends" ON public.hiring_trends
  FOR DELETE USING (auth.uid() = user_id);

-- Add indexes for better performance
CREATE INDEX idx_integrations_user_id ON public.integrations(user_id);
CREATE INDEX idx_integrations_status ON public.integrations(status);
CREATE INDEX idx_compliance_data_user_id ON public.compliance_data(user_id);
CREATE INDEX idx_budget_data_user_id ON public.budget_data(user_id);
CREATE INDEX idx_budget_data_category ON public.budget_data(category);
CREATE INDEX idx_retention_predictions_user_id ON public.retention_predictions(user_id);
CREATE INDEX idx_retention_predictions_risk_level ON public.retention_predictions(risk_level);
CREATE INDEX idx_hiring_trends_user_id ON public.hiring_trends(user_id);
CREATE INDEX idx_hiring_trends_period ON public.hiring_trends(period);;
