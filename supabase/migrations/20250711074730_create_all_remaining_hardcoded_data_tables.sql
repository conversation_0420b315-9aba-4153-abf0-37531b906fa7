/*
  # Create All Remaining Tables for Hardcoded Data Components

  1. HIGH PRIORITY Tables
    - pipeline_stages: Job pipeline stage data
    - pipeline_candidates: Candidates in pipeline stages
    - candidate_monthly_stats: Monthly candidate analytics
    - candidate_skills_stats: Skills distribution analytics

  2. MEDIUM PRIORITY Tables
    - report_templates_data: Report generation templates
    - generated_reports_data: Generated report records
    - workflow_templates_data: Pre-built workflow templates

  3. Security
    - Enable RLS on all tables
    - Add policies for user-specific access
*/

-- ===== HIGH PRIORITY TABLES =====

-- Create pipeline_stages table
CREATE TABLE IF NOT EXISTS public.pipeline_stages (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  name text NOT NULL,
  count integer DEFAULT 0,
  color text DEFAULT 'bg-blue-500',
  stage_order integer NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create pipeline_candidates table
CREATE TABLE IF NOT EXISTS public.pipeline_candidates (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  candidate_name text NOT NULL,
  role text NOT NULL,
  stage text NOT NULL,
  rating numeric DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
  last_activity text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create candidate_monthly_stats table
CREATE TABLE IF NOT EXISTS public.candidate_monthly_stats (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  month text NOT NULL,
  candidates_count integer DEFAULT 0,
  placements_count integer DEFAULT 0,
  year integer DEFAULT EXTRACT(YEAR FROM CURRENT_DATE),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  UNIQUE(user_id, month, year)
);

-- Create candidate_skills_stats table
CREATE TABLE IF NOT EXISTS public.candidate_skills_stats (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  skill_name text NOT NULL,
  candidate_count integer DEFAULT 0,
  color text DEFAULT '#8884d8',
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  UNIQUE(user_id, skill_name)
);

-- ===== MEDIUM PRIORITY TABLES =====

-- Create report_templates_data table
CREATE TABLE IF NOT EXISTS public.report_templates_data (
  id text PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  name text NOT NULL,
  description text,
  type text NOT NULL CHECK (type IN ('candidate', 'analytics', 'job', 'performance')),
  format text NOT NULL CHECK (format IN ('pdf', 'excel', 'csv')),
  metrics jsonb DEFAULT '[]',
  last_generated timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create generated_reports_data table
CREATE TABLE IF NOT EXISTS public.generated_reports_data (
  id text PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  name text NOT NULL,
  type text NOT NULL,
  format text NOT NULL,
  generated_at timestamp with time zone DEFAULT now(),
  file_size text,
  download_url text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Create workflow_templates_data table
CREATE TABLE IF NOT EXISTS public.workflow_templates_data (
  id text PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  name text NOT NULL,
  description text,
  icon text DEFAULT 'Settings',
  config jsonb NOT NULL DEFAULT '{}',
  category text DEFAULT 'general',
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- ===== ENABLE RLS ON ALL TABLES =====

ALTER TABLE public.pipeline_stages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pipeline_candidates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.candidate_monthly_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.candidate_skills_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.report_templates_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.generated_reports_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workflow_templates_data ENABLE ROW LEVEL SECURITY;

-- ===== CREATE RLS POLICIES =====

-- Pipeline stages policies
CREATE POLICY "Users can view their own pipeline stages" ON public.pipeline_stages
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own pipeline stages" ON public.pipeline_stages
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own pipeline stages" ON public.pipeline_stages
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own pipeline stages" ON public.pipeline_stages
  FOR DELETE USING (auth.uid() = user_id);

-- Pipeline candidates policies
CREATE POLICY "Users can view their own pipeline candidates" ON public.pipeline_candidates
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own pipeline candidates" ON public.pipeline_candidates
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own pipeline candidates" ON public.pipeline_candidates
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own pipeline candidates" ON public.pipeline_candidates
  FOR DELETE USING (auth.uid() = user_id);

-- Candidate monthly stats policies
CREATE POLICY "Users can view their own candidate monthly stats" ON public.candidate_monthly_stats
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own candidate monthly stats" ON public.candidate_monthly_stats
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own candidate monthly stats" ON public.candidate_monthly_stats
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own candidate monthly stats" ON public.candidate_monthly_stats
  FOR DELETE USING (auth.uid() = user_id);

-- Candidate skills stats policies
CREATE POLICY "Users can view their own candidate skills stats" ON public.candidate_skills_stats
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own candidate skills stats" ON public.candidate_skills_stats
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own candidate skills stats" ON public.candidate_skills_stats
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own candidate skills stats" ON public.candidate_skills_stats
  FOR DELETE USING (auth.uid() = user_id);

-- Report templates data policies
CREATE POLICY "Users can view their own report templates" ON public.report_templates_data
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own report templates" ON public.report_templates_data
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own report templates" ON public.report_templates_data
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own report templates" ON public.report_templates_data
  FOR DELETE USING (auth.uid() = user_id);

-- Generated reports data policies
CREATE POLICY "Users can view their own generated reports" ON public.generated_reports_data
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own generated reports" ON public.generated_reports_data
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own generated reports" ON public.generated_reports_data
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own generated reports" ON public.generated_reports_data
  FOR DELETE USING (auth.uid() = user_id);

-- Workflow templates data policies
CREATE POLICY "Users can view their own workflow templates" ON public.workflow_templates_data
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own workflow templates" ON public.workflow_templates_data
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own workflow templates" ON public.workflow_templates_data
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own workflow templates" ON public.workflow_templates_data
  FOR DELETE USING (auth.uid() = user_id);

-- ===== CREATE INDEXES FOR PERFORMANCE =====

CREATE INDEX idx_pipeline_stages_user_id ON public.pipeline_stages(user_id);
CREATE INDEX idx_pipeline_stages_order ON public.pipeline_stages(stage_order);
CREATE INDEX idx_pipeline_candidates_user_id ON public.pipeline_candidates(user_id);
CREATE INDEX idx_pipeline_candidates_stage ON public.pipeline_candidates(stage);
CREATE INDEX idx_candidate_monthly_stats_user_id ON public.candidate_monthly_stats(user_id);
CREATE INDEX idx_candidate_monthly_stats_month ON public.candidate_monthly_stats(month, year);
CREATE INDEX idx_candidate_skills_stats_user_id ON public.candidate_skills_stats(user_id);
CREATE INDEX idx_candidate_skills_stats_skill ON public.candidate_skills_stats(skill_name);
CREATE INDEX idx_report_templates_data_user_id ON public.report_templates_data(user_id);
CREATE INDEX idx_report_templates_data_type ON public.report_templates_data(type);
CREATE INDEX idx_generated_reports_data_user_id ON public.generated_reports_data(user_id);
CREATE INDEX idx_workflow_templates_data_user_id ON public.workflow_templates_data(user_id);;
