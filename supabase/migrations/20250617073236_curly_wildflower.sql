\n\n-- Insert sample workflow configurations\nDO $$ \nBEGIN\n  -- Only insert if no data exists for the current user\n  IF NOT EXISTS (\n    SELECT 1 FROM public.workflow_configurations \n    WHERE created_by = auth.uid() \n    LIMIT 1\n  ) AND EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid()) THEN\n    INSERT INTO public.workflow_configurations (\n      name, description, config, is_active, created_by\n    )\n    VALUES (\n      'New Candidate Screening', \n      'Automated workflow for screening new candidates',\n      '{\n        "nodes": [\n          {\n            "id": "new-application-1",\n            "type": "workflowNode",\n            "position": {"x": 100, "y": 100},\n            "data": {\n              "id": "new-application",\n              "originalId": "new-application",\n              "category": "triggers",\n              "type": "trigger",\n              "label": "New Application",\n              "description": "Triggers when a new candidate applies",\n              "config": {\n                "jobTypes": "all",\n                "notifyTeam": true\n              }\n            }\n          },\n          {\n            "id": "ai-screen-1",\n            "type": "workflowNode",\n            "position": {"x": 100, "y": 250},\n            "data": {\n              "id": "ai-screen",\n              "originalId": "ai-screen",\n              "category": "actions",\n              "type": "action",\n              "label": "AI Screening",\n              "description": "Screen resume with AI",\n              "config": {\n                "criteria": "comprehensive",\n                "minScore": "75"\n              }\n            }\n          },\n          {\n            "id": "skills-match-1",\n            "type": "workflowNode",\n            "position": {"x": 100, "y": 400},\n            "data": {\n              "id": "skills-match",\n              "originalId": "skills-match",\n              "category": "conditions",\n              "type": "condition",\n              "label": "Skills Match",\n              "description": "Check required skills",\n              "config": {\n                "requiredSkills": "React, TypeScript, Node.js",\n                "minMatchPercentage": "70",\n                "considerSimilarSkills": true\n              }\n            }\n          },\n          {\n            "id": "send-email-1",\n            "type": "workflowNode",\n            "position": {"x": 100, "y": 550},\n            "data": {\n              "id": "send-email",\n              "originalId": "send-email",\n              "category": "actions",\n              "type": "action",\n              "label": "Send Email",\n              "description": "Send an automated email",\n              "config": {\n                "template": "interview",\n                "customMessage": "We would like to invite you for an interview."\n              }\n            }\n          }\n        ],\n        "edges": [\n          {\n            "id": "edge-1",\n            "source": "new-application-1",\n            "target": "ai-screen-1",\n            "sourceHandle": "source-bottom",\n            "targetHandle": "target-top",\n            "type": "default",\n            "animated": true\n          },\n          {\n            "id": "edge-2",\n            "source": "ai-screen-1",\n            "target": "skills-match-1",\n            "sourceHandle": "source-bottom",\n            "targetHandle": "target-top",\n            "type": "default",\n            "animated": true\n          },\n          {\n            "id": "edge-3",\n            "source": "skills-match-1",\n            "target": "send-email-1",\n            "sourceHandle": "source-bottom",\n            "targetHandle": "target-top",\n            "type": "default",\n            "animated": true\n          }\n        ]\n      }'::jsonb,\n      true,\n      auth.uid()\n    ),\n    (\n      'Interview Scheduling', \n      'Automated workflow for scheduling interviews',\n      '{\n        "nodes": [\n          {\n            "id": "application-status-1",\n            "type": "workflowNode",\n            "position": {"x": 100, "y": 100},\n            "data": {\n              "id": "application-status",\n              "originalId": "application-status",\n              "category": "triggers",\n              "type": "trigger",\n              "label": "Status Change",\n              "description": "Triggers when application status changes",\n              "config": {\n                "statusType": "approved",\n                "customStatus": ""\n              }\n            }\n          },\n          {\n            "id": "schedule-interview-1",\n            "type": "workflowNode",\n            "position": {"x": 100, "y": 250},\n            "data": {\n              "id": "schedule-interview",\n              "originalId": "schedule-interview",\n              "category": "actions",\n              "type": "action",\n              "label": "Schedule Interview",\n              "description": "Automatically schedule interviews",\n              "config": {\n                "interviewType": "technical",\n                "duration": "60",\n                "sendCalendarInvite": true\n              }\n            }\n          },\n          {\n            "id": "notify-team-1",\n            "type": "workflowNode",\n            "position": {"x": 100, "y": 400},\n            "data": {\n              "id": "notify-team",\n              "originalId": "notify-team",\n              "category": "actions",\n              "type": "action",\n              "label": "Notify Team",\n              "description": "Send notification to team",\n              "config": {\n                "channel": "email",\n                "teamMembers": "hiring-managers",\n                "customMessage": "New interview scheduled"\n              }\n            }\n          }\n        ],\n        "edges": [\n          {\n            "id": "edge-1",\n            "source": "application-status-1",\n            "target": "schedule-interview-1",\n            "sourceHandle": "source-bottom",\n            "targetHandle": "target-top",\n            "type": "default",\n            "animated": true\n          },\n          {\n            "id": "edge-2",\n            "source": "schedule-interview-1",\n            "target": "notify-team-1",\n            "sourceHandle": "source-bottom",\n            "targetHandle": "target-top",\n            "type": "default",\n            "animated": true\n          }\n        ]\n      }'::jsonb,\n      true,\n      auth.uid()\n    ),\n    (\n      'Candidate Rejection Process', \n      'Automated workflow for handling candidate rejections',\n      '{\n        "nodes": [\n          {\n            "id": "application-status-1",\n            "type": "workflowNode",\n            "position": {"x": 100, "y": 100},\n            "data": {\n              "id": "application-status",\n              "originalId": "application-status",\n              "category": "triggers",\n              "type": "trigger",\n              "label": "Status Change",\n              "description": "Triggers when application status changes",\n              "config": {\n                "statusType": "rejected",\n                "customStatus": ""\n              }\n            }\n          },\n          {\n            "id": "send-email-1",\n            "type": "workflowNode",\n            "position": {"x": 100, "y": 250},\n            "data": {\n              "id": "send-email",\n              "originalId": "send-email",\n              "category": "actions",\n              "type": "action",\n              "label": "Send Email",\n              "description": "Send an automated email",\n              "config": {\n                "template": "rejection",\n                "customMessage": "Thank you for your interest in our company."\n              }\n            }\n          },\n          {\n            "id": "add-to-pool-1",\n            "type": "workflowNode",\n            "position": {"x": 100, "y": 400},\n            "data": {\n              "id": "add-to-pool",\n              "originalId": "add-to-pool",\n              "category": "outputs",\n              "type": "output",\n              "label": "Add to Talent Pool",\n              "description": "Add to specific talent pool",\n              "config": {\n                "poolName": "engineering",\n                "tags": "rejected,future-consideration",\n                "followUpDate": ""\n              }\n            }\n          }\n        ],\n        "edges": [\n          {\n            "id": "edge-1",\n            "source": "application-status-1",\n            "target": "send-email-1",\n            "sourceHandle": "source-bottom",\n            "targetHandle": "target-top",\n            "type": "default",\n            "animated": true\n          },\n          {\n            "id": "edge-2",\n            "source": "send-email-1",\n            "target": "add-to-pool-1",\n            "sourceHandle": "source-bottom",\n            "targetHandle": "target-top",\n            "type": "default",\n            "animated": true\n          }\n        ]\n      }'::jsonb,\n      true,\n      auth.uid()\n    );
\n  END IF;
\nEND $$;
\n\n-- Insert sample workflow executions\nDO $$ \nBEGIN\n  -- Only insert if workflow_executions table exists and no data exists for the current user\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'workflow_executions') THEN\n    IF NOT EXISTS (\n      SELECT 1 FROM public.workflow_executions \n      WHERE created_by = auth.uid() \n      LIMIT 1\n    ) AND EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid()) THEN\n      -- Get the first workflow ID for this user\n      DECLARE workflow_id UUID;
\n      BEGIN\n        SELECT id INTO workflow_id FROM public.workflow_configurations \n        WHERE created_by = auth.uid() \n        LIMIT 1;
\n\n        IF workflow_id IS NOT NULL THEN\n          INSERT INTO public.workflow_executions (\n            workflow_id, status, started_at, completed_at, execution_log, created_by\n          )\n          VALUES (\n            workflow_id,\n            'completed',\n            now() - interval '2 days',\n            now() - interval '2 days' + interval '5 minutes',\n            '{\n              "nodes": [\n                {\n                  "id": "new-application-1",\n                  "status": "success",\n                  "message": "Successfully executed New Application",\n                  "timestamp": "2025-06-15T10:00:00Z"\n                },\n                {\n                  "id": "ai-screen-1",\n                  "status": "success",\n                  "message": "Successfully executed AI Screening",\n                  "timestamp": "2025-06-15T10:01:30Z"\n                },\n                {\n                  "id": "skills-match-1",\n                  "status": "success",\n                  "message": "Successfully executed Skills Match",\n                  "timestamp": "2025-06-15T10:03:00Z"\n                },\n                {\n                  "id": "send-email-1",\n                  "status": "success",\n                  "message": "Successfully executed Send Email",\n                  "timestamp": "2025-06-15T10:04:30Z"\n                }\n              ],\n              "executionPath": ["new-application-1", "ai-screen-1", "skills-match-1", "send-email-1"]\n            }'::jsonb,\n            auth.uid()\n          ),\n          (\n            workflow_id,\n            'failed',\n            now() - interval '1 day',\n            now() - interval '1 day' + interval '2 minutes',\n            '{\n              "nodes": [\n                {\n                  "id": "new-application-1",\n                  "status": "success",\n                  "message": "Successfully executed New Application",\n                  "timestamp": "2025-06-16T14:30:00Z"\n                },\n                {\n                  "id": "ai-screen-1",\n                  "status": "failed",\n                  "message": "Failed to execute AI Screening: API error",\n                  "timestamp": "2025-06-16T14:31:30Z"\n                }\n              ],\n              "executionPath": ["new-application-1", "ai-screen-1"],\n              "error": "API connection timeout"\n            }'::jsonb,\n            auth.uid()\n          );
\n        END IF;
\n      END;
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Drop existing profiles policies if they exist\nDROP POLICY IF EXISTS "Users can manage their own profiles" ON public.profiles;
\nDROP POLICY IF EXISTS "Users can view their own profiles" ON public.profiles;
\nDROP POLICY IF EXISTS "Users can update their own profiles" ON public.profiles;
\n\n-- Create RLS policies for profiles table\nCREATE POLICY "Users can manage their own profiles"\n  ON public.profiles\n    FOR ALL\n    TO authenticated\n    USING (auth.uid() = id)\n    WITH CHECK (auth.uid() = id);
\n\nCREATE POLICY "Users can view their own profiles"\n  ON public.profiles\n    FOR SELECT\n    TO authenticated\n    USING (auth.uid() = id);
\n\nCREATE POLICY "Users can update their own profiles"\n  ON public.profiles\n    FOR UPDATE\n    TO authenticated\n    USING (auth.uid() = id)\n    WITH CHECK (auth.uid() = id);
;
