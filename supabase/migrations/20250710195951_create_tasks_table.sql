CREATE TABLE tasks (\n    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n    title TEXT NOT NULL,\n    description TEXT,\n    status TEXT NOT NULL DEFAULT 'pending',\n    priority TEXT NOT NULL DEFAULT 'medium',\n    assignee TEXT,\n    due_date TIMESTAMPTZ,\n    category TEXT NOT NULL DEFAULT 'general',\n    user_id UUID REFERENCES auth.users(id),\n    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),\n    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()\n);
\n;
