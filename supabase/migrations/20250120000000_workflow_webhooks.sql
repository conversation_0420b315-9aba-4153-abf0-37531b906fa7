\n\n-- Create workflow_webhooks table\nCREATE TABLE IF NOT EXISTS public.workflow_webhooks (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  workflow_id UUID REFERENCES public.workflow_configurations(id) ON DELETE CASCADE NOT NULL,\n  webhook_secret TEXT NOT NULL, -- Secret for signature validation\n  webhook_url TEXT NOT NULL UNIQUE, -- Unique URL path for this webhook\n  description TEXT,\n  is_active BOOLEAN NOT NULL DEFAULT true,\n  last_triggered TIMESTAMP WITH TIME ZONE,\n  trigger_count INTEGER NOT NULL DEFAULT 0,\n  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Enable RLS on the table\nALTER TABLE public.workflow_webhooks ENABLE ROW LEVEL SECURITY;
\n\n-- Create RLS policies for workflow_webhooks\nCREATE POLICY "Users can view their own workflow webhooks"\n  ON public.workflow_webhooks\n  FOR SELECT\n  USING (auth.uid() = created_by);
\n\nCREATE POLICY "Users can create their own workflow webhooks"\n  ON public.workflow_webhooks\n  FOR INSERT\n  WITH CHECK (auth.uid() = created_by);
\n\nCREATE POLICY "Users can update their own workflow webhooks"\n  ON public.workflow_webhooks\n  FOR UPDATE\n  USING (auth.uid() = created_by);
\n\nCREATE POLICY "Users can delete their own workflow webhooks"\n  ON public.workflow_webhooks\n  FOR DELETE\n  USING (auth.uid() = created_by);
\n\n-- Create indexes for better performance\nCREATE INDEX IF NOT EXISTS idx_workflow_webhooks_workflow_id ON public.workflow_webhooks(workflow_id);
\nCREATE INDEX IF NOT EXISTS idx_workflow_webhooks_webhook_url ON public.workflow_webhooks(webhook_url);
\nCREATE INDEX IF NOT EXISTS idx_workflow_webhooks_created_by ON public.workflow_webhooks(created_by);
\nCREATE INDEX IF NOT EXISTS idx_workflow_webhooks_is_active ON public.workflow_webhooks(is_active);
\n\n-- Create a function to generate webhook URL\nCREATE OR REPLACE FUNCTION generate_webhook_url()\nRETURNS TEXT AS $$\nBEGIN\n  RETURN 'webhook_' || encode(gen_random_bytes(16), 'hex');
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Create trigger to update updated_at timestamp\nCREATE OR REPLACE FUNCTION update_updated_at_column()\nRETURNS TRIGGER AS $$\nBEGIN\n  NEW.updated_at = CURRENT_TIMESTAMP;
\n  RETURN NEW;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\nCREATE TRIGGER update_workflow_webhooks_updated_at\n  BEFORE UPDATE ON public.workflow_webhooks\n  FOR EACH ROW\n  EXECUTE FUNCTION update_updated_at_column();
\n;
