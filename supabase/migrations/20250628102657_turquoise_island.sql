\n\n-- Create user_settings table\nCREATE TABLE IF NOT EXISTS public.user_settings (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\n  settings_type TEXT NOT NULL, -- 'ai', 'notifications', etc.\n  settings JSONB NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  UNIQUE(user_id, settings_type)\n);
\n\n-- Enable RLS on user_settings table\nALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;
\n\n-- Create RLS policies for user_settings\nCREATE POLICY "Users can manage their own settings"\n  ON public.user_settings\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\n-- Create index for better performance\nCREATE INDEX idx_user_settings_user_id ON public.user_settings(user_id);
\nCREATE INDEX idx_user_settings_type ON public.user_settings(settings_type);
;
