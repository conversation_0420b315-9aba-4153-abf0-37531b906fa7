-- Enable realtime optimizations for all tables used in real-time subscriptions
-- This migration ensures proper replica identity and realtime publication settings

-- Set replica identity to FULL for all tables to ensure realtime works optimally
-- FULL replica identity includes all columns in the replication stream

-- Core data tables
ALTER TABLE public.candidates REPLICA IDENTITY FULL;
ALTER TABLE public.jobs REPL<PERSON>A IDENTITY FULL;
ALTER TABLE public.events REPLICA IDENTITY FULL;
ALTER TABLE public.messages REPLICA IDENTITY FULL;
ALTER TABLE public.message_templates REPLICA IDENTITY FULL;
-- Analytics tables
ALTER TABLE public.analytics_metrics REP<PERSON><PERSON><PERSON> IDENTITY FULL;
ALTER TABLE public.analytics_applications REPL<PERSON><PERSON> IDENTITY FULL;
ALTER TABLE public.analytics_skills REPLICA IDENTITY FULL;
ALTER TABLE public.analytics_sources REPLICA IDENTITY FULL;
ALTER TABLE public.analytics_diversity REPLICA IDENTITY FULL;
ALTER TABLE public.analytics_salary REPLICA IDENTITY FULL;
-- Task and system tables
ALTER TABLE public.tasks REPLICA IDENTITY FULL;
ALTER TABLE public.system_health REPLICA IDENTITY FULL;
ALTER TABLE public.notifications REPLICA IDENTITY FULL;
ALTER TABLE public.user_feedback REPLICA IDENTITY FULL;
-- Workflow tables
ALTER TABLE public.workflow_configurations REPLICA IDENTITY FULL;
ALTER TABLE public.workflow_executions REPLICA IDENTITY FULL;
ALTER TABLE public.workflow_schedules REPLICA IDENTITY FULL;
ALTER TABLE public.workflow_webhooks REPLICA IDENTITY FULL;
ALTER TABLE public.workflow_metrics REPLICA IDENTITY FULL;
-- Candidate related tables
ALTER TABLE public.candidate_timeline REPLICA IDENTITY FULL;
ALTER TABLE public.candidate_notes REPLICA IDENTITY FULL;
ALTER TABLE public.candidate_interviews REPLICA IDENTITY FULL;
-- Budget and analytics tables
ALTER TABLE public.budget_data REPLICA IDENTITY FULL;
ALTER TABLE public.budget_summary REPLICA IDENTITY FULL;
ALTER TABLE public.hiring_trends REPLICA IDENTITY FULL;
ALTER TABLE public.retention_predictions REPLICA IDENTITY FULL;
ALTER TABLE public.candidate_monthly_stats REPLICA IDENTITY FULL;
ALTER TABLE public.candidate_skills_stats REPLICA IDENTITY FULL;
-- User and profile tables
ALTER TABLE public.profiles REPLICA IDENTITY FULL;
ALTER TABLE public.user_settings REPLICA IDENTITY FULL;
-- Create indexes on commonly filtered columns for realtime performance
-- These improve the performance of realtime subscriptions with filters

-- User ID indexes (most common filter)
CREATE INDEX IF NOT EXISTS idx_candidates_user_id ON public.candidates(user_id);
CREATE INDEX IF NOT EXISTS idx_jobs_user_id ON public.jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_events_user_id ON public.events(user_id);
CREATE INDEX IF NOT EXISTS idx_messages_user_id ON public.messages(user_id);
CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON public.tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_user_feedback_user_id ON public.user_feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_metrics_user_id ON public.analytics_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_applications_user_id ON public.analytics_applications(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_skills_user_id ON public.analytics_skills(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_sources_user_id ON public.analytics_sources(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_diversity_user_id ON public.analytics_diversity(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_salary_user_id ON public.analytics_salary(user_id);
-- Status and read filters for notifications
CREATE INDEX IF NOT EXISTS idx_notifications_read ON public.notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_user_read ON public.notifications(user_id, read);
-- Task status filters
CREATE INDEX IF NOT EXISTS idx_tasks_status ON public.tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_user_status ON public.tasks(user_id, status);
-- Due date filters for tasks
CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON public.tasks(due_date);
CREATE INDEX IF NOT EXISTS idx_tasks_user_due_date ON public.tasks(user_id, due_date);
-- Workflow execution status
CREATE INDEX IF NOT EXISTS idx_workflow_executions_status ON public.workflow_executions(status);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_user_status ON public.workflow_executions(user_id, status);
-- System health status
CREATE INDEX IF NOT EXISTS idx_system_health_status ON public.system_health(status);
-- Budget and analytics time-based indexes
CREATE INDEX IF NOT EXISTS idx_hiring_trends_period ON public.hiring_trends(period);
CREATE INDEX IF NOT EXISTS idx_candidate_monthly_stats_month ON public.candidate_monthly_stats(month);
-- Comment explaining the optimizations
COMMENT ON EXTENSION plpgsql IS 'Realtime optimizations applied: REPLICA IDENTITY FULL set on all real-time tables, performance indexes added for common filter patterns';
-- Note: In Supabase, the realtime publication is automatically managed
-- All tables with REPLICA IDENTITY FULL will be included in realtime automatically
-- No manual publication management is needed;
