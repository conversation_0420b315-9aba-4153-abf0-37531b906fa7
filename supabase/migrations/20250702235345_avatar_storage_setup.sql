-- Create avatars storage bucket (if not exists)\nINSERT INTO storage.buckets (id, name, public) \nVALUES ('avatars', 'avatars', true)\nON CONFLICT (id) DO NOTHING;
\n\n-- Create storage policies for avatars (drop and recreate to ensure correct permissions)\nDROP POLICY IF EXISTS "Avatar images are publicly accessible" ON storage.objects;
\nCREATE POLICY "Avatar images are publicly accessible" \nON storage.objects \nFOR SELECT \nUSING (bucket_id = 'avatars');
\n\nDROP POLICY IF EXISTS "Users can upload their own avatar" ON storage.objects;
\nCREATE POLICY "Users can upload their own avatar" \nON storage.objects \nFOR INSERT \nWITH CHECK (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);
\n\nDROP POLICY IF EXISTS "Users can update their own avatar" ON storage.objects;
\nCREATE POLICY "Users can update their own avatar" \nON storage.objects \nFOR UPDATE \nUSING (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);
\n\nDROP POLICY IF EXISTS "Users can delete their own avatar" ON storage.objects;
\nCREATE POLICY "Users can delete their own avatar" \nON storage.objects \nFOR DELETE \nUSING (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);
\n;
