\n-- Create a table for job postings\nCREATE TABLE public.jobs (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID REFERENCES auth.users NOT NULL,\n  title TEXT NOT NULL,\n  department TEXT NOT NULL,\n  location TEXT NOT NULL,\n  job_type TEXT NOT NULL DEFAULT 'Full-time',\n  salary_range TEXT,\n  experience_required TEXT,\n  description TEXT NOT NULL,\n  requirements TEXT[],\n  benefits TEXT[],\n  is_urgent BOOLEAN DEFAULT false,\n  is_active BOOLEAN DEFAULT true,\n  applicant_count INTEGER DEFAULT 0,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Add Row Level Security (RLS) to ensure users can only see their own job postings\nALTER TABLE public.jobs ENABLE ROW LEVEL SECURITY;
\n\n-- Create policy that allows users to SELECT their own jobs\nCREATE POLICY "Users can view their own jobs" \n  ON public.jobs \n  FOR SELECT \n  USING (auth.uid() = user_id);
\n\n-- Create policy that allows users to INSERT their own jobs\nCREATE POLICY "Users can create their own jobs" \n  ON public.jobs \n  FOR INSERT \n  WITH CHECK (auth.uid() = user_id);
\n\n-- Create policy that allows users to UPDATE their own jobs\nCREATE POLICY "Users can update their own jobs" \n  ON public.jobs \n  FOR UPDATE \n  USING (auth.uid() = user_id);
\n\n-- Create policy that allows users to DELETE their own jobs\nCREATE POLICY "Users can delete their own jobs" \n  ON public.jobs \n  FOR DELETE \n  USING (auth.uid() = user_id);
\n\n-- Create an index for better performance\nCREATE INDEX idx_jobs_user_id ON public.jobs(user_id);
\nCREATE INDEX idx_jobs_active ON public.jobs(is_active);
\n;
