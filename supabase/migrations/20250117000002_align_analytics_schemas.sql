\n\n-- Add missing fields to analytics_salary table\nALTER TABLE public.analytics_salary \nADD COLUMN IF NOT EXISTS position TEXT,\nADD COLUMN IF NOT EXISTS current_salary NUMERIC,\nADD COLUMN IF NOT EXISTS market_rate NUMERIC,\nADD COLUMN IF NOT EXISTS confidence INTEGER DEFAULT 0,\nADD COLUMN IF NOT EXISTS location TEXT,\nADD COLUMN IF NOT EXISTS experience_level TEXT,\nADD COLUMN IF NOT EXISTS industry TEXT;
\n\n-- Add success_impact to analytics_skills if not exists\nALTER TABLE public.analytics_skills \nADD COLUMN IF NOT EXISTS success_impact INTEGER DEFAULT 0;
\n\n-- Update analytics_salary with sample data if the table is empty\nDO $$\nBEGIN\n    IF NOT EXISTS (SELECT 1 FROM public.analytics_salary LIMIT 1) THEN\n        -- This will be populated by the sample data generation\n        NULL;
\n    END IF;
\nEND $$;
\n;
