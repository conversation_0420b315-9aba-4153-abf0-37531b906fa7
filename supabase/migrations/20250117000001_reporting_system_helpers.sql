-- Helper function to get report access URL\nCREATE OR REPLACE FUNCTION public.get_report_access_url(report_id UUID)\nRETURNS TEXT AS $$\nDECLARE\n    report_record RECORD;
\n    base_url TEXT;
\nBEGIN\n    -- Get report details\n    SELECT file_path, generated_by INTO report_record\n    FROM public.generated_reports\n    WHERE id = report_id;
\n    \n    IF report_record.file_path IS NULL THEN\n        RETURN NULL;
\n    END IF;
\n    \n    -- Get the storage URL\n    SELECT \n        CASE \n            WHEN current_setting('app.supabase_url', true) IS NOT NULL \n            THEN current_setting('app.supabase_url', true)\n            ELSE 'https://your-project.supabase.co'\n        END INTO base_url;
\n    \n    -- Return the full URL\n    RETURN base_url || '/storage/v1/object/authenticated/reports/' || report_record.file_path;
\nEND;
\n$$ LANGUAGE plpgsql SECURITY DEFINER;
\n\n-- Helper function to schedule a report\nCREATE OR REPLACE FUNCTION public.schedule_report(\n    p_name TEXT,\n    p_template_id UUID,\n    p_cron_expression TEXT,\n    p_parameters JSONB DEFAULT '{}',\n    p_recipients JSONB DEFAULT '[]',\n    p_output_format TEXT DEFAULT 'pdf'\n)\nRETURNS UUID AS $$\nDECLARE\n    v_report_id UUID;
\n    v_next_run TIMESTAMPTZ;
\nBEGIN\n    -- Basic cron validation (you might want to add more sophisticated validation)\n    IF p_cron_expression !~ '^(\\S+\\s+){4}\\S+$' THEN\n        RAISE EXCEPTION 'Invalid cron expression format';
\n    END IF;
\n    \n    -- Calculate next run time (simplified - you'd want a proper cron parser)\n    -- For now, just set it to tomorrow at the same time\n    v_next_run := NOW() + INTERVAL '1 day';
\n    \n    INSERT INTO public.scheduled_reports (\n        name,\n        template_id,\n        schedule_config,\n        parameters,\n        recipients,\n        output_format,\n        next_run_at,\n        created_by\n    ) VALUES (\n        p_name,\n        p_template_id,\n        jsonb_build_object(\n            'cron', p_cron_expression,\n            'timezone', COALESCE(current_setting('timezone', true), 'UTC')\n        ),\n        p_parameters,\n        p_recipients,\n        p_output_format,\n        v_next_run,\n        auth.uid()\n    ) RETURNING id INTO v_report_id;
\n    \n    RETURN v_report_id;
\nEND;
\n$$ LANGUAGE plpgsql SECURITY DEFINER;
\n\n-- Helper function to generate a report\nCREATE OR REPLACE FUNCTION public.generate_report(\n    p_template_id UUID,\n    p_parameters JSONB DEFAULT '{}',\n    p_format TEXT DEFAULT 'pdf'\n)\nRETURNS UUID AS $$\nDECLARE\n    v_report_id UUID;
\n    v_template RECORD;
\nBEGIN\n    -- Get template details\n    SELECT * INTO v_template\n    FROM public.report_templates\n    WHERE id = p_template_id AND (is_active = true OR created_by = auth.uid());
\n    \n    IF v_template.id IS NULL THEN\n        RAISE EXCEPTION 'Template not found or not accessible';
\n    END IF;
\n    \n    -- Create report record\n    INSERT INTO public.generated_reports (\n        template_id,\n        name,\n        description,\n        parameters_used,\n        format,\n        status,\n        generated_by\n    ) VALUES (\n        p_template_id,\n        v_template.name || ' - ' || TO_CHAR(NOW(), 'YYYY-MM-DD HH24:MI'),\n        v_template.description,\n        p_parameters,\n        p_format,\n        'pending',\n        auth.uid()\n    ) RETURNING id INTO v_report_id;
\n    \n    -- Trigger an event that can be picked up by an Edge Function or external service\n    PERFORM pg_notify(\n        'report_generation_requested',\n        json_build_object(\n            'report_id', v_report_id,\n            'template_id', p_template_id,\n            'parameters', p_parameters,\n            'format', p_format,\n            'user_id', auth.uid()\n        )::text\n    );
\n    \n    RETURN v_report_id;
\nEND;
\n$$ LANGUAGE plpgsql SECURITY DEFINER;
\n\n-- View for report generation queue\nCREATE OR REPLACE VIEW public.report_generation_queue AS\nSELECT \n    gr.id,\n    gr.name,\n    gr.format,\n    gr.status,\n    gr.generated_by,\n    gr.generated_at,\n    rt.name as template_name,\n    rt.query_template,\n    gr.parameters_used,\n    u.email as user_email\nFROM public.generated_reports gr\nJOIN public.report_templates rt ON gr.template_id = rt.id\nLEFT JOIN auth.users u ON gr.generated_by = u.id\nWHERE gr.status IN ('pending', 'generating')\nORDER BY gr.generated_at ASC;
\n\n-- View for scheduled reports due for execution\nCREATE OR REPLACE VIEW public.due_scheduled_reports AS\nSELECT \n    sr.*,\n    rt.name as template_name,\n    rt.query_template\nFROM public.scheduled_reports sr\nJOIN public.report_templates rt ON sr.template_id = rt.id\nWHERE sr.is_active = true\n  AND sr.next_run_at <= NOW()\nORDER BY sr.next_run_at ASC;
\n\n-- View for user report history\nCREATE OR REPLACE VIEW public.my_report_history AS\nSELECT \n    gr.id,\n    gr.name,\n    gr.format,\n    gr.status,\n    gr.generated_at,\n    gr.file_size,\n    gr.access_count,\n    gr.expires_at,\n    rt.name as template_name,\n    sr.name as schedule_name,\n    CASE \n        WHEN gr.file_path IS NOT NULL AND gr.status = 'completed' \n        THEN public.get_report_access_url(gr.id)\n        ELSE NULL\n    END as download_url\nFROM public.generated_reports gr\nLEFT JOIN public.report_templates rt ON gr.template_id = rt.id\nLEFT JOIN public.scheduled_reports sr ON gr.scheduled_report_id = sr.id\nWHERE gr.generated_by = auth.uid()\n   OR sr.created_by = auth.uid()\nORDER BY gr.generated_at DESC;
\n\n-- Grant appropriate permissions\nGRANT SELECT ON public.report_generation_queue TO authenticated;
\nGRANT SELECT ON public.due_scheduled_reports TO authenticated;
\nGRANT SELECT ON public.my_report_history TO authenticated;
\n\n-- Function to clean up expired reports\nCREATE OR REPLACE FUNCTION public.cleanup_expired_reports()\nRETURNS INTEGER AS $$\nDECLARE\n    v_deleted_count INTEGER;
\nBEGIN\n    -- Mark expired reports\n    UPDATE public.generated_reports\n    SET status = 'expired'\n    WHERE expires_at < NOW()\n      AND status = 'completed';
\n    \n    -- Delete old expired reports and their files\n    WITH deleted AS (\n        DELETE FROM public.generated_reports\n        WHERE status = 'expired'\n          AND expires_at < NOW() - INTERVAL '7 days'\n        RETURNING id, file_path\n    )\n    SELECT COUNT(*) INTO v_deleted_count FROM deleted;
\n    \n    -- Note: Actual file deletion from storage would need to be handled\n    -- by an Edge Function or external service\n    \n    RETURN v_deleted_count;
\nEND;
\n$$ LANGUAGE plpgsql SECURITY DEFINER;
\n\n-- Function to update report access tracking\nCREATE OR REPLACE FUNCTION public.track_report_access(p_report_id UUID)\nRETURNS VOID AS $$\nBEGIN\n    UPDATE public.generated_reports\n    SET \n        accessed_at = NOW(),\n        access_count = COALESCE(access_count, 0) + 1\n    WHERE id = p_report_id\n      AND (generated_by = auth.uid() OR EXISTS (\n          SELECT 1 FROM public.scheduled_reports sr\n          WHERE sr.id = generated_reports.scheduled_report_id\n            AND sr.created_by = auth.uid()\n      ));
\nEND;
\n$$ LANGUAGE plpgsql SECURITY DEFINER;
\n\n-- Add RLS policies for the views\nCREATE POLICY "Users can view report generation queue for their reports"\nON public.generated_reports FOR SELECT\nTO authenticated\nUSING (\n    generated_by = auth.uid() AND status IN ('pending', 'generating')\n);
\n\n-- Add helpful comments\nCOMMENT ON FUNCTION public.get_report_access_url IS 'Generate a secure access URL for a completed report';
\nCOMMENT ON FUNCTION public.schedule_report IS 'Schedule a recurring report based on a template';
\nCOMMENT ON FUNCTION public.generate_report IS 'Generate a one-time report based on a template';
\nCOMMENT ON FUNCTION public.cleanup_expired_reports IS 'Clean up expired reports and mark for deletion';
\nCOMMENT ON FUNCTION public.track_report_access IS 'Track when a report is accessed/downloaded';
\n\nCOMMENT ON VIEW public.report_generation_queue IS 'Queue of reports waiting to be generated';
\nCOMMENT ON VIEW public.due_scheduled_reports IS 'Scheduled reports that are due for execution';
\nCOMMENT ON VIEW public.my_report_history IS 'User''s report generation and access history';
\n;
