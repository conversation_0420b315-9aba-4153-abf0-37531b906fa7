-- Add full-text search column to candidates table only (jobs already has it)\nALTER TABLE public.candidates \nADD COLUMN IF NOT EXISTS search_vector tsvector;
\n\n-- Create GIN index for candidates search vector\nCREATE INDEX IF NOT EXISTS candidates_search_vector_idx ON public.candidates USING gin(search_vector);
\n\n-- Update existing candidates records with search vectors\nUPDATE public.candidates SET search_vector = \n  setweight(to_tsvector('english', COALESCE(name, '')), 'A') ||\n  setweight(to_tsvector('english', COALESCE(role, '')), 'A') ||\n  setweight(to_tsvector('english', COALESCE(email, '')), 'B') ||\n  setweight(to_tsvector('english', COALESCE(location, '')), 'B') ||\n  setweight(to_tsvector('english', COALESCE(experience, '')), 'C') ||\n  setweight(to_tsvector('english', COALESCE(industry, '')), 'C') ||\n  setweight(to_tsvector('english', COALESCE(ai_summary, '')), 'D') ||\n  setweight(to_tsvector('english', COALESCE(array_to_string(tags, ' '), '')), 'B')\nWHERE search_vector IS NULL;
;
