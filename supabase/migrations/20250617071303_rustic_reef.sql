\n\n-- Create analytics_metrics table\nCREATE TABLE IF NOT EXISTS public.analytics_metrics (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  metric_name TEXT NOT NULL,\n  metric_value NUMERIC NOT NULL,\n  change_percentage NUMERIC,\n  period TEXT NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create analytics_applications table\nCREATE TABLE IF NOT EXISTS public.analytics_applications (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  period TEXT NOT NULL,\n  applications INTEGER NOT NULL,\n  interviews INTEGER,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create analytics_skills table\nCREATE TABLE IF NOT EXISTS public.analytics_skills (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  skill TEXT NOT NULL,\n  current_level INTEGER NOT NULL,\n  required_level INTEGER NOT NULL,\n  gap INTEGER NOT NULL,\n  recommendation TEXT,\n  success_impact INTEGER,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create analytics_sources table\nCREATE TABLE IF NOT EXISTS public.analytics_sources (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  source TEXT NOT NULL,\n  effectiveness INTEGER NOT NULL,\n  hires INTEGER NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create analytics_diversity table\nCREATE TABLE IF NOT EXISTS public.analytics_diversity (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  category TEXT NOT NULL,\n  value INTEGER NOT NULL,\n  total INTEGER NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create analytics_salary table\nCREATE TABLE IF NOT EXISTS public.analytics_salary (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  role TEXT NOT NULL,\n  market_salary NUMERIC NOT NULL,\n  recommended_salary NUMERIC NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Enable RLS on all tables\nALTER TABLE public.analytics_metrics ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.analytics_applications ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.analytics_skills ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.analytics_sources ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.analytics_diversity ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.analytics_salary ENABLE ROW LEVEL SECURITY;
\n\n-- Drop existing policies if they exist and create new ones\nDROP POLICY IF EXISTS "Users can manage their own analytics_metrics" ON public.analytics_metrics;
\nDROP POLICY IF EXISTS "Users can manage their own analytics_applications" ON public.analytics_applications;
\nDROP POLICY IF EXISTS "Users can manage their own analytics_skills" ON public.analytics_skills;
\nDROP POLICY IF EXISTS "Users can manage their own analytics_sources" ON public.analytics_sources;
\nDROP POLICY IF EXISTS "Users can manage their own analytics_diversity" ON public.analytics_diversity;
\nDROP POLICY IF EXISTS "Users can manage their own analytics_salary" ON public.analytics_salary;
\n\n-- Create RLS policies\nCREATE POLICY "Users can manage their own analytics_metrics"\n  ON public.analytics_metrics\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can manage their own analytics_applications"\n  ON public.analytics_applications\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can manage their own analytics_skills"\n  ON public.analytics_skills\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can manage their own analytics_sources"\n  ON public.analytics_sources\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can manage their own analytics_diversity"\n  ON public.analytics_diversity\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can manage their own analytics_salary"\n  ON public.analytics_salary\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\n-- Create indexes for better performance\nCREATE INDEX IF NOT EXISTS idx_analytics_metrics_user_id ON public.analytics_metrics(user_id);
\nCREATE INDEX IF NOT EXISTS idx_analytics_applications_user_id ON public.analytics_applications(user_id);
\nCREATE INDEX IF NOT EXISTS idx_analytics_skills_user_id ON public.analytics_skills(user_id);
\nCREATE INDEX IF NOT EXISTS idx_analytics_sources_user_id ON public.analytics_sources(user_id);
\nCREATE INDEX IF NOT EXISTS idx_analytics_diversity_user_id ON public.analytics_diversity(user_id);
\nCREATE INDEX IF NOT EXISTS idx_analytics_salary_user_id ON public.analytics_salary(user_id);
\n\n-- Insert sample data for analytics_metrics\nINSERT INTO public.analytics_metrics (user_id, metric_name, metric_value, change_percentage, period)\nSELECT \n  auth.uid(), 'total_applications', 2543, 12, 'month'\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_metrics (user_id, metric_name, metric_value, change_percentage, period)\nSELECT \n  auth.uid(), 'active_jobs', 48, 4, 'week'\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_metrics (user_id, metric_name, metric_value, change_percentage, period)\nSELECT \n  auth.uid(), 'time_to_hire', 23, -3, 'month'\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\n-- Insert sample data for analytics_applications\nINSERT INTO public.analytics_applications (user_id, period, applications, interviews)\nSELECT auth.uid(), 'Jan', 400, 240\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_applications (user_id, period, applications, interviews)\nSELECT auth.uid(), 'Feb', 300, 180\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_applications (user_id, period, applications, interviews)\nSELECT auth.uid(), 'Mar', 600, 320\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_applications (user_id, period, applications, interviews)\nSELECT auth.uid(), 'Apr', 800, 400\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_applications (user_id, period, applications, interviews)\nSELECT auth.uid(), 'May', 500, 280\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_applications (user_id, period, applications, interviews)\nSELECT auth.uid(), 'Jun', 700, 350\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\n-- Insert sample data for analytics_skills\nINSERT INTO public.analytics_skills (user_id, skill, current_level, required_level, gap, recommendation, success_impact)\nSELECT \n  auth.uid(), 'Technical', 75, 85, 10, 'Focus on advanced React patterns and TypeScript', 85\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_skills (user_id, skill, current_level, required_level, gap, recommendation, success_impact)\nSELECT \n  auth.uid(), 'Leadership', 65, 80, 15, 'Consider leadership training or mentorship programs', 70\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_skills (user_id, skill, current_level, required_level, gap, recommendation, success_impact)\nSELECT \n  auth.uid(), 'Communication', 85, 90, 5, 'Minor improvements needed in technical documentation', 90\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_skills (user_id, skill, current_level, required_level, gap, recommendation, success_impact)\nSELECT \n  auth.uid(), 'Problem Solving', 70, 75, 5, 'Practice system design and architecture', 80\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\n-- Insert sample data for analytics_sources\nINSERT INTO public.analytics_sources (user_id, source, effectiveness, hires)\nSELECT auth.uid(), 'LinkedIn', 85, 28\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_sources (user_id, source, effectiveness, hires)\nSELECT auth.uid(), 'Internal Referrals', 92, 35\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_sources (user_id, source, effectiveness, hires)\nSELECT auth.uid(), 'Job Boards', 65, 18\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_sources (user_id, source, effectiveness, hires)\nSELECT auth.uid(), 'Company Website', 72, 22\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\n-- Insert sample data for analytics_diversity\nINSERT INTO public.analytics_diversity (user_id, category, value, total)\nSELECT auth.uid(), 'Gender', 42, 100\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_diversity (user_id, category, value, total)\nSELECT auth.uid(), 'Ethnicity', 38, 100\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_diversity (user_id, category, value, total)\nSELECT auth.uid(), 'Age Groups', 45, 100\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_diversity (user_id, category, value, total)\nSELECT auth.uid(), 'Veterans', 15, 100\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\n-- Insert sample data for analytics_salary\nINSERT INTO public.analytics_salary (user_id, role, market_salary, recommended_salary)\nSELECT auth.uid(), 'Software Engineer', 120000, 132000\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_salary (user_id, role, market_salary, recommended_salary)\nSELECT auth.uid(), 'Product Manager', 130000, 143000\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_salary (user_id, role, market_salary, recommended_salary)\nSELECT auth.uid(), 'Data Scientist', 115000, 126500\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n\nINSERT INTO public.analytics_salary (user_id, role, market_salary, recommended_salary)\nSELECT auth.uid(), 'UX Designer', 95000, 104500\nWHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
;
