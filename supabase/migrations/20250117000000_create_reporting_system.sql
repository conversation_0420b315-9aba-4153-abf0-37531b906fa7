-- Create report_templates table\nCREATE TABLE IF NOT EXISTS public.report_templates (\n    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n    name TEXT NOT NULL,\n    description TEXT,\n    template_config <PERSON><PERSON><PERSON><PERSON> NOT NULL DEFAULT '{}',\n    query_template TEXT,\n    visualization_config JSON<PERSON> DEFAULT '{}',\n    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,\n    created_at TIMESTAMPTZ DEFAULT NOW(),\n    updated_at TIMESTAMPTZ DEFAULT NOW(),\n    is_active BOOLEAN DEFAULT true,\n    CONSTRAINT report_templates_name_check CHECK (char_length(name) > 0)\n);
\n\n-- Create scheduled_reports table\nCREATE TABLE IF NOT EXISTS public.scheduled_reports (\n    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n    name TEXT NOT NULL,\n    description TEXT,\n    template_id UUID REFERENCES public.report_templates(id) ON DELETE CASCADE,\n    schedule_config J<PERSON><PERSON>B NOT NULL DEFAULT '{}', -- cron expression, timezone, etc.\n    parameters JSONB DEFAULT '{}', -- runtime parameters for the report\n    recipients JSONB DEFAULT '[]', -- array of email addresses or user ids\n    output_format TEXT DEFAULT 'pdf' CHECK (output_format IN ('pdf', 'csv', 'excel', 'json')),\n    is_active BOOLEAN DEFAULT true,\n    last_run_at TIMESTAMPTZ,\n    next_run_at TIMESTAMPTZ,\n    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,\n    created_at TIMESTAMPTZ DEFAULT NOW(),\n    updated_at TIMESTAMPTZ DEFAULT NOW(),\n    CONSTRAINT scheduled_reports_name_check CHECK (char_length(name) > 0)\n);
\n\n-- Create generated_reports table\nCREATE TABLE IF NOT EXISTS public.generated_reports (\n    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n    scheduled_report_id UUID REFERENCES public.scheduled_reports(id) ON DELETE CASCADE,\n    template_id UUID REFERENCES public.report_templates(id) ON DELETE SET NULL,\n    name TEXT NOT NULL,\n    description TEXT,\n    parameters_used JSONB DEFAULT '{}', -- actual parameters used for generation\n    file_path TEXT, -- path in storage bucket\n    file_size BIGINT,\n    format TEXT NOT NULL CHECK (format IN ('pdf', 'csv', 'excel', 'json')),\n    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'generating', 'completed', 'failed', 'expired')),\n    error_message TEXT,\n    metadata JSONB DEFAULT '{}', -- additional metadata like page count, row count, etc.\n    generated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,\n    generated_at TIMESTAMPTZ DEFAULT NOW(),\n    expires_at TIMESTAMPTZ,\n    accessed_at TIMESTAMPTZ,\n    access_count INTEGER DEFAULT 0,\n    CONSTRAINT generated_reports_name_check CHECK (char_length(name) > 0)\n);
\n\n-- Create indexes for better performance\nCREATE INDEX idx_report_templates_created_by ON public.report_templates(created_by);
\nCREATE INDEX idx_report_templates_is_active ON public.report_templates(is_active);
\n\nCREATE INDEX idx_scheduled_reports_created_by ON public.scheduled_reports(created_by);
\nCREATE INDEX idx_scheduled_reports_is_active ON public.scheduled_reports(is_active);
\nCREATE INDEX idx_scheduled_reports_next_run_at ON public.scheduled_reports(next_run_at) WHERE is_active = true;
\nCREATE INDEX idx_scheduled_reports_template_id ON public.scheduled_reports(template_id);
\n\nCREATE INDEX idx_generated_reports_generated_by ON public.generated_reports(generated_by);
\nCREATE INDEX idx_generated_reports_scheduled_report_id ON public.generated_reports(scheduled_report_id);
\nCREATE INDEX idx_generated_reports_template_id ON public.generated_reports(template_id);
\nCREATE INDEX idx_generated_reports_status ON public.generated_reports(status);
\nCREATE INDEX idx_generated_reports_generated_at ON public.generated_reports(generated_at);
\nCREATE INDEX idx_generated_reports_expires_at ON public.generated_reports(expires_at) WHERE expires_at IS NOT NULL;
\n\n-- Enable Row Level Security\nALTER TABLE public.report_templates ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.scheduled_reports ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.generated_reports ENABLE ROW LEVEL SECURITY;
\n\n-- RLS Policies for report_templates\n-- Users can view all active templates\nCREATE POLICY "Users can view active report templates"\nON public.report_templates FOR SELECT\nTO authenticated\nUSING (is_active = true OR created_by = auth.uid());
\n\n-- Users can create their own templates\nCREATE POLICY "Users can create report templates"\nON public.report_templates FOR INSERT\nTO authenticated\nWITH CHECK (auth.uid() = created_by);
\n\n-- Users can update their own templates\nCREATE POLICY "Users can update own report templates"\nON public.report_templates FOR UPDATE\nTO authenticated\nUSING (auth.uid() = created_by)\nWITH CHECK (auth.uid() = created_by);
\n\n-- Users can delete their own templates\nCREATE POLICY "Users can delete own report templates"\nON public.report_templates FOR DELETE\nTO authenticated\nUSING (auth.uid() = created_by);
\n\n-- RLS Policies for scheduled_reports\n-- Users can view their own scheduled reports\nCREATE POLICY "Users can view own scheduled reports"\nON public.scheduled_reports FOR SELECT\nTO authenticated\nUSING (auth.uid() = created_by);
\n\n-- Users can create scheduled reports\nCREATE POLICY "Users can create scheduled reports"\nON public.scheduled_reports FOR INSERT\nTO authenticated\nWITH CHECK (auth.uid() = created_by);
\n\n-- Users can update their own scheduled reports\nCREATE POLICY "Users can update own scheduled reports"\nON public.scheduled_reports FOR UPDATE\nTO authenticated\nUSING (auth.uid() = created_by)\nWITH CHECK (auth.uid() = created_by);
\n\n-- Users can delete their own scheduled reports\nCREATE POLICY "Users can delete own scheduled reports"\nON public.scheduled_reports FOR DELETE\nTO authenticated\nUSING (auth.uid() = created_by);
\n\n-- RLS Policies for generated_reports\n-- Users can view reports they generated or reports from their scheduled reports\nCREATE POLICY "Users can view own generated reports"\nON public.generated_reports FOR SELECT\nTO authenticated\nUSING (\n    auth.uid() = generated_by \n    OR auth.uid() IN (\n        SELECT created_by FROM public.scheduled_reports \n        WHERE id = generated_reports.scheduled_report_id\n    )\n);
\n\n-- Users can create generated reports\nCREATE POLICY "Users can create generated reports"\nON public.generated_reports FOR INSERT\nTO authenticated\nWITH CHECK (auth.uid() = generated_by);
\n\n-- Users can update their own generated reports\nCREATE POLICY "Users can update own generated reports"\nON public.generated_reports FOR UPDATE\nTO authenticated\nUSING (auth.uid() = generated_by)\nWITH CHECK (auth.uid() = generated_by);
\n\n-- Users can delete their own generated reports\nCREATE POLICY "Users can delete own generated reports"\nON public.generated_reports FOR DELETE\nTO authenticated\nUSING (auth.uid() = generated_by);
\n\n-- Create updated_at trigger function if it doesn't exist\nCREATE OR REPLACE FUNCTION public.handle_updated_at()\nRETURNS TRIGGER AS $$\nBEGIN\n    NEW.updated_at = NOW();
\n    RETURN NEW;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Create triggers for updated_at\nCREATE TRIGGER handle_report_templates_updated_at\n    BEFORE UPDATE ON public.report_templates\n    FOR EACH ROW\n    EXECUTE FUNCTION public.handle_updated_at();
\n\nCREATE TRIGGER handle_scheduled_reports_updated_at\n    BEFORE UPDATE ON public.scheduled_reports\n    FOR EACH ROW\n    EXECUTE FUNCTION public.handle_updated_at();
\n\n-- Create storage bucket for reports\nINSERT INTO storage.buckets (id, name, public, allowed_mime_types, file_size_limit, avif_autodetection)\nVALUES (\n    'reports',\n    'reports',\n    false,\n    ARRAY['application/pdf', 'text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/json'],\n    52428800, -- 50MB limit\n    false\n)\nON CONFLICT (id) DO NOTHING;
\n\n-- Create storage policies for reports bucket\n-- Users can upload files to reports bucket\nCREATE POLICY "Users can upload report files"\nON storage.objects FOR INSERT\nTO authenticated\nWITH CHECK (\n    bucket_id = 'reports' \n    AND auth.uid()::text = (storage.foldername(name))[1]\n);
\n\n-- Users can view their own report files\nCREATE POLICY "Users can view own report files"\nON storage.objects FOR SELECT\nTO authenticated\nUSING (\n    bucket_id = 'reports' \n    AND auth.uid()::text = (storage.foldername(name))[1]\n);
\n\n-- Users can update their own report files\nCREATE POLICY "Users can update own report files"\nON storage.objects FOR UPDATE\nTO authenticated\nUSING (\n    bucket_id = 'reports' \n    AND auth.uid()::text = (storage.foldername(name))[1]\n)\nWITH CHECK (\n    bucket_id = 'reports' \n    AND auth.uid()::text = (storage.foldername(name))[1]\n);
\n\n-- Users can delete their own report files\nCREATE POLICY "Users can delete own report files"\nON storage.objects FOR DELETE\nTO authenticated\nUSING (\n    bucket_id = 'reports' \n    AND auth.uid()::text = (storage.foldername(name))[1]\n);
\n\n-- Add helpful comments\nCOMMENT ON TABLE public.report_templates IS 'Stores reusable report templates with their configurations';
\nCOMMENT ON TABLE public.scheduled_reports IS 'Stores scheduled reports configuration and execution schedule';
\nCOMMENT ON TABLE public.generated_reports IS 'Stores metadata about generated reports and their file locations';
\n\nCOMMENT ON COLUMN public.report_templates.template_config IS 'JSON configuration for the report template including data sources, filters, etc.';
\nCOMMENT ON COLUMN public.report_templates.query_template IS 'SQL query template with parameter placeholders';
\nCOMMENT ON COLUMN public.report_templates.visualization_config IS 'JSON configuration for charts, graphs, and other visualizations';
\n\nCOMMENT ON COLUMN public.scheduled_reports.schedule_config IS 'Cron expression, timezone, and other scheduling configuration';
\nCOMMENT ON COLUMN public.scheduled_reports.parameters IS 'Runtime parameters to pass to the report template';
\nCOMMENT ON COLUMN public.scheduled_reports.recipients IS 'Array of email addresses or user IDs to send the report to';
\n\nCOMMENT ON COLUMN public.generated_reports.parameters_used IS 'Actual parameters used when generating this report';
\nCOMMENT ON COLUMN public.generated_reports.file_path IS 'Path to the report file in the storage bucket';
\nCOMMENT ON COLUMN public.generated_reports.metadata IS 'Additional metadata like page count, row count, generation time, etc.';
\n;
