\n-- Create workflow_executions table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.workflow_executions (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  workflow_id UUID REFERENCES workflow_configurations(id),\n  status TEXT NOT NULL, -- 'completed', 'failed', 'in_progress'\n  started_at TIMESTAMP WITH TIME ZONE NOT NULL,\n  completed_at TIMESTAMP WITH TIME ZONE,\n  execution_log JSONB,\n  created_by UUID REFERENCES auth.users(id)\n);
\n\n-- Enable RLS on workflow_executions table\nALTER TABLE public.workflow_executions ENABLE ROW LEVEL SECURITY;
\n\n-- Note: RLS policies for workflow_executions are handled in mellow_truth.sql migration\n-- Only ensuring table exists and has proper structure\n\n-- Create indexes for better performance (avoiding duplicates)\nCREATE INDEX IF NOT EXISTS idx_workflow_executions_workflow_id ON public.workflow_executions(workflow_id);
\nCREATE INDEX IF NOT EXISTS idx_workflow_executions_created_by ON public.workflow_executions(created_by);
\n\n-- Note: workflow_schedules table and policies are handled in mellow_truth.sql migration\n-- Note: All RLS policies for workflow tables are consolidated in still_smoke.sql migration\n-- This migration only ensures table structure consistency\n;
