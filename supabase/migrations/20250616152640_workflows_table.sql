\n-- Create events table for calendar functionality\nCREATE TABLE public.events (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  title TEXT NOT NULL,\n  description TEXT,\n  start_time TIMESTAMP WITH TIME ZONE NOT NULL,\n  end_time TIMESTAMP WITH TIME ZONE NOT NULL,\n  location TEXT,\n  meeting_link TEXT,\n  event_type TEXT NOT NULL DEFAULT 'meeting', -- 'meeting', 'interview', 'personal', etc.\n  priority TEXT NOT NULL DEFAULT 'medium', -- 'low', 'medium', 'high'\n  category TEXT NOT NULL DEFAULT 'general',\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create messages table for communication functionality\nCREATE TABLE public.messages (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  sender_name TEXT NOT NULL,\n  sender_email TEXT NOT NULL,\n  sender_role TEXT,\n  sender_avatar TEXT,\n  content TEXT NOT NULL,\n  status TEXT NOT NULL DEFAULT 'unread', -- 'read', 'unread'\n  is_starred BOOLEAN NOT NULL DEFAULT false,\n  follow_up BOOLEAN NOT NULL DEFAULT false,\n  reminder BOOLEAN NOT NULL DEFAULT false,\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create message templates table for quick responses\nCREATE TABLE public.message_templates (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  name TEXT NOT NULL,\n  subject TEXT NOT NULL,\n  content TEXT NOT NULL,\n  template_category TEXT NOT NULL DEFAULT 'general', -- 'interview', 'rejection', 'follow-up', etc.\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Enable RLS on all new tables\nALTER TABLE public.events ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.message_templates ENABLE ROW LEVEL SECURITY;
\n\n-- Create RLS policies for events\nCREATE POLICY "Users can manage their own events" \n  ON public.events \n  FOR ALL \n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\n-- Create RLS policies for messages\nCREATE POLICY "Users can manage their own messages" \n  ON public.messages \n  FOR ALL \n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\n-- Create RLS policies for message templates\nCREATE POLICY "Users can manage their own message templates" \n  ON public.message_templates \n  FOR ALL \n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\n-- Create indexes for better performance\nCREATE INDEX idx_events_user_id ON public.events(user_id);
\nCREATE INDEX idx_events_start_time ON public.events(start_time);
\nCREATE INDEX idx_messages_user_id ON public.messages(user_id);
\nCREATE INDEX idx_messages_status ON public.messages(status);
\nCREATE INDEX idx_message_templates_user_id ON public.message_templates(user_id);
\n;
