\n\n-- Create events table if it doesn't exist\nDO $$ \nBEGIN\n  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'events') THEN\n    CREATE TABLE public.events (\n      id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n      title TEXT NOT NULL,\n      description TEXT,\n      start_time TIMESTAMP WITH TIME ZONE NOT NULL,\n      end_time TIMESTAMP WITH TIME ZONE NOT NULL,\n      location TEXT,\n      meeting_link TEXT,\n      event_type TEXT NOT NULL DEFAULT 'meeting', -- 'meeting', 'interview', 'call', 'presentation', 'other'\n      priority TEXT NOT NULL DEFAULT 'medium', -- 'low', 'medium', 'high'\n      category TEXT NOT NULL DEFAULT 'general', -- 'general', 'recruitment', 'client', 'internal'\n      user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n    );
\n    \n    -- Enable RLS on events table\n    ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;
\n    \n    -- Create RLS policies for events\n    CREATE POLICY "Users can manage their own events" \n      ON public.events \n      FOR ALL \n      USING (auth.uid() = user_id)\n      WITH CHECK (auth.uid() = user_id);
\n      \n    -- Create indexes for better performance on events\n    CREATE INDEX idx_events_user_id ON public.events(user_id);
\n    CREATE INDEX idx_events_start_time ON public.events(start_time);
\n  END IF;
\nEND $$;
\n\n-- Create messages table if it doesn't exist\nDO $$ \nBEGIN\n  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'messages') THEN\n    CREATE TABLE public.messages (\n      id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n      sender_name TEXT NOT NULL,\n      sender_email TEXT NOT NULL,\n      sender_role TEXT,\n      sender_avatar TEXT,\n      content TEXT NOT NULL,\n      status TEXT NOT NULL DEFAULT 'unread', -- 'read', 'unread', 'archived'\n      is_starred BOOLEAN NOT NULL DEFAULT false,\n      follow_up BOOLEAN NOT NULL DEFAULT false,\n      reminder BOOLEAN NOT NULL DEFAULT false,\n      user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n    );
\n    \n    -- Enable RLS on messages table\n    ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
\n    \n    -- Create RLS policies for messages\n    CREATE POLICY "Users can manage their own messages" \n      ON public.messages \n      FOR ALL \n      USING (auth.uid() = user_id)\n      WITH CHECK (auth.uid() = user_id);
\n      \n    -- Create indexes for better performance on messages\n    CREATE INDEX idx_messages_user_id ON public.messages(user_id);
\n    CREATE INDEX idx_messages_status ON public.messages(status);
\n  END IF;
\nEND $$;
\n\n-- Create message_templates table if it doesn't exist\nDO $$ \nBEGIN\n  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'message_templates') THEN\n    CREATE TABLE public.message_templates (\n      id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n      name TEXT NOT NULL,\n      subject TEXT NOT NULL,\n      content TEXT NOT NULL,\n      template_category TEXT NOT NULL DEFAULT 'general', -- 'interview', 'follow_up', 'rejection', 'offer'\n      user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n    );
\n    \n    -- Enable RLS on message_templates table\n    ALTER TABLE public.message_templates ENABLE ROW LEVEL SECURITY;
\n    \n    -- Create RLS policies for message templates\n    CREATE POLICY "Users can manage their own message templates" \n      ON public.message_templates \n      FOR ALL \n      USING (auth.uid() = user_id)\n      WITH CHECK (auth.uid() = user_id);
\n      \n    -- Create indexes for better performance on message templates\n    CREATE INDEX idx_message_templates_user_id ON public.message_templates(user_id);
\n  END IF;
\nEND $$;
\n\n-- Create workflow_configurations table if it doesn't exist\nDO $$ \nBEGIN\n  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'workflow_configurations') THEN\n    CREATE TABLE public.workflow_configurations (\n      id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n      name TEXT NOT NULL,\n      description TEXT,\n      config JSONB NOT NULL,\n      is_active BOOLEAN NOT NULL DEFAULT true,\n      created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n    );
\n    \n    -- Enable RLS on workflow_configurations table\n    ALTER TABLE public.workflow_configurations ENABLE ROW LEVEL SECURITY;
\n    \n    -- Create RLS policies for workflow_configurations\n    CREATE POLICY "Users can view their own workflows" \n      ON public.workflow_configurations \n      FOR SELECT \n      USING (auth.uid() = created_by);
\n\n    CREATE POLICY "Users can create workflows" \n      ON public.workflow_configurations \n      FOR INSERT \n      WITH CHECK (auth.uid() = created_by);
\n\n    CREATE POLICY "Users can update their own workflows" \n      ON public.workflow_configurations \n      FOR UPDATE \n      USING (auth.uid() = created_by);
\n\n    CREATE POLICY "Users can delete their own workflows" \n      ON public.workflow_configurations \n      FOR DELETE \n      USING (auth.uid() = created_by);
\n      \n    -- Create index for better performance\n    CREATE INDEX idx_workflow_configurations_created_by ON public.workflow_configurations(created_by);
\n  END IF;
\nEND $$;
\n\n-- Create sample data for testing\nDO $$ \nBEGIN\n  -- Create sample messages if the table exists\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'messages') THEN\n    -- Only insert if no messages exist\n    IF NOT EXISTS (SELECT 1 FROM public.messages LIMIT 1) THEN\n      INSERT INTO public.messages (\n        sender_name, sender_email, sender_role, sender_avatar, content, \n        status, is_starred, follow_up, reminder, user_id\n      )\n      SELECT \n        'John Smith', '<EMAIL>', 'Hiring Manager', 'https://i.pravatar.cc/150?u=1',\n        'Hello, I wanted to discuss the Frontend Developer position. We have several candidates that look promising.',\n        'unread', false, false, false, auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.messages (\n        sender_name, sender_email, sender_role, sender_avatar, content, \n        status, is_starred, follow_up, reminder, user_id\n      )\n      SELECT \n        'Sarah Johnson', '<EMAIL>', 'Senior Developer', 'https://i.pravatar.cc/150?u=2',\n        'I''ve reviewed the technical assessment for Michael Chen. He did very well on the coding challenge.',\n        'read', true, true, false, auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.messages (\n        sender_name, sender_email, sender_role, sender_avatar, content, \n        status, is_starred, follow_up, reminder, user_id\n      )\n      SELECT \n        'Emily Rodriguez', '<EMAIL>', 'HR Manager', 'https://i.pravatar.cc/150?u=3',\n        'We need to schedule interviews for the Product Manager position by next week. Can you help coordinate?',\n        'unread', false, false, true, auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n    END IF;
\n  END IF;
\n\n  -- Create sample message templates if the table exists\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'message_templates') THEN\n    -- Only insert if no templates exist\n    IF NOT EXISTS (SELECT 1 FROM public.message_templates LIMIT 1) THEN\n      INSERT INTO public.message_templates (\n        name, subject, content, template_category, user_id\n      )\n      SELECT \n        'Interview Invitation', 'Interview Invitation for [Position]',\n        'Dear [Name],\\n\\nThank you for your application for the [Position] role at [Company]. We were impressed with your background and would like to invite you for an interview.\\n\\nPlease let me know your availability for next week.\\n\\nBest regards,\\n[Your Name]',\n        'interview', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.message_templates (\n        name, subject, content, template_category, user_id\n      )\n      SELECT \n        'Follow-up After Interview', 'Thank You for Your Interview',\n        'Dear [Name],\\n\\nThank you for taking the time to interview for the [Position] position. It was a pleasure speaking with you and learning more about your skills and experience.\\n\\nWe are currently reviewing all candidates and will be in touch soon with next steps.\\n\\nBest regards,\\n[Your Name]',\n        'follow_up', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.message_templates (\n        name, subject, content, template_category, user_id\n      )\n      SELECT \n        'Job Offer', 'Job Offer - [Position] at [Company]',\n        'Dear [Name],\\n\\nI am pleased to offer you the position of [Position] at [Company]. Based on your experience and skills, we believe you would be a valuable addition to our team.\\n\\nYour starting salary will be [Salary] per year, and you will be eligible for our benefits package including [Benefits].\\n\\nPlease review the attached offer letter for complete details.\\n\\nBest regards,\\n[Your Name]',\n        'offer', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n    END IF;
\n  END IF;
\n\n  -- Create sample events if the table exists\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'events') THEN\n    -- Only insert if no events exist\n    IF NOT EXISTS (SELECT 1 FROM public.events LIMIT 1) THEN\n      -- Create events for the next 7 days\n      INSERT INTO public.events (\n        title, description, start_time, end_time, location, event_type, \n        priority, category, user_id\n      )\n      SELECT \n        'Technical Interview - Frontend Developer', 'Interview with candidate John Doe for the Frontend Developer position',\n        (CURRENT_DATE + INTERVAL '1 day' + INTERVAL '10 hours')::timestamp with time zone,\n        (CURRENT_DATE + INTERVAL '1 day' + INTERVAL '11 hours')::timestamp with time zone,\n        'Conference Room A', 'interview', 'high', 'recruitment', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.events (\n        title, description, start_time, end_time, meeting_link, event_type, \n        priority, category, user_id\n      )\n      SELECT \n        'Team Standup', 'Daily team standup meeting',\n        (CURRENT_DATE + INTERVAL '2 days' + INTERVAL '9 hours')::timestamp with time zone,\n        (CURRENT_DATE + INTERVAL '2 days' + INTERVAL '9 hours' + INTERVAL '30 minutes')::timestamp with time zone,\n        'https://meet.example.com/standup', 'meeting', 'medium', 'internal', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n      \n      INSERT INTO public.events (\n        title, description, start_time, end_time, location, event_type, \n        priority, category, user_id\n      )\n      SELECT \n        'Product Demo', 'Demo of new recruitment features to the client',\n        (CURRENT_DATE + INTERVAL '3 days' + INTERVAL '14 hours')::timestamp with time zone,\n        (CURRENT_DATE + INTERVAL '3 days' + INTERVAL '15 hours')::timestamp with time zone,\n        'Main Conference Room', 'presentation', 'high', 'client', auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n    END IF;
\n  END IF;
\n\n  -- Create sample workflow configurations if the table exists\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'workflow_configurations') THEN\n    -- Only insert if no workflow configurations exist\n    IF NOT EXISTS (SELECT 1 FROM public.workflow_configurations LIMIT 1) THEN\n      INSERT INTO public.workflow_configurations (\n        name, description, config, is_active, created_by\n      )\n      SELECT \n        'New Candidate Screening', 'Automated workflow for screening new candidates',\n        '{\n          "nodes": [\n            {\n              "id": "new-application-1",\n              "type": "workflowNode",\n              "position": {"x": 100, "y": 100},\n              "data": {\n                "id": "new-application",\n                "originalId": "new-application",\n                "category": "triggers",\n                "type": "trigger",\n                "label": "New Application",\n                "description": "Triggers when a new candidate applies",\n                "config": {\n                  "jobTypes": "all",\n                  "notifyTeam": true\n                }\n              }\n            },\n            {\n              "id": "ai-screen-1",\n              "type": "workflowNode",\n              "position": {"x": 100, "y": 250},\n              "data": {\n                "id": "ai-screen",\n                "originalId": "ai-screen",\n                "category": "actions",\n                "type": "action",\n                "label": "AI Screening",\n                "description": "Screen resume with AI",\n                "config": {\n                  "criteria": "comprehensive",\n                  "minScore": "75"\n                }\n              }\n            },\n            {\n              "id": "skills-match-1",\n              "type": "workflowNode",\n              "position": {"x": 100, "y": 400},\n              "data": {\n                "id": "skills-match",\n                "originalId": "skills-match",\n                "category": "conditions",\n                "type": "condition",\n                "label": "Skills Match",\n                "description": "Check required skills",\n                "config": {\n                  "requiredSkills": "React, TypeScript, Node.js",\n                  "minMatchPercentage": "70",\n                  "considerSimilarSkills": true\n                }\n              }\n            },\n            {\n              "id": "send-email-1",\n              "type": "workflowNode",\n              "position": {"x": 100, "y": 550},\n              "data": {\n                "id": "send-email",\n                "originalId": "send-email",\n                "category": "actions",\n                "type": "action",\n                "label": "Send Email",\n                "description": "Send an automated email",\n                "config": {\n                  "template": "interview",\n                  "customMessage": "We would like to invite you for an interview."\n                }\n              }\n            }\n          ],\n          "edges": [\n            {\n              "id": "edge-1",\n              "source": "new-application-1",\n              "target": "ai-screen-1",\n              "sourceHandle": "source-bottom",\n              "targetHandle": "target-top",\n              "type": "default",\n              "animated": true\n            },\n            {\n              "id": "edge-2",\n              "source": "ai-screen-1",\n              "target": "skills-match-1",\n              "sourceHandle": "source-bottom",\n              "targetHandle": "target-top",\n              "type": "default",\n              "animated": true\n            },\n            {\n              "id": "edge-3",\n              "source": "skills-match-1",\n              "target": "send-email-1",\n              "sourceHandle": "source-bottom",\n              "targetHandle": "target-top",\n              "type": "default",\n              "animated": true\n            }\n          ]\n        }'::jsonb,\n        true, auth.uid()\n      WHERE EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid());
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Create storage bucket for candidate documents if it doesn't exist\n-- Note: This is a placeholder since we can't directly create storage buckets in SQL\n-- The bucket should be created through the Supabase dashboard\nDO $$ \nBEGIN\n  NULL;
\nEND $$;
;
