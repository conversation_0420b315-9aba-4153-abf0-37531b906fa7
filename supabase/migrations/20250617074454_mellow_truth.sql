-- Create workflow_executions table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.workflow_executions (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  workflow_id UUID REFERENCES workflow_configurations(id),\n  status TEXT NOT NULL, -- 'completed', 'failed', 'in_progress'\n  started_at TIMESTAMP WITH TIME ZONE NOT NULL,\n  completed_at TIMESTAMP WITH TIME ZONE,\n  execution_log JSONB,\n  created_by UUID REFERENCES auth.users(id)\n);
\n\n-- Enable RLS on workflow_executions table\nALTER TABLE public.workflow_executions ENABLE ROW LEVEL SECURITY;
\n\n-- Drop existing workflow policies if they exist\nDROP POLICY IF EXISTS "Users can view their own workflow executions" ON public.workflow_executions;
\nDROP POLICY IF EXISTS "Users can create their own workflow executions" ON public.workflow_executions;
\nDROP POLICY IF EXISTS "Users can update their own workflow executions" ON public.workflow_executions;
\nDROP POLICY IF EXISTS "Users can create workflow executions" ON public.workflow_executions;
\nDROP POLICY IF EXISTS "Users can manage their own workflow executions" ON public.workflow_executions;
\n\n-- Create RLS policies for workflow_executions only if they don't exist\nDO $$\nBEGIN\n  IF NOT EXISTS (\n    SELECT 1 FROM pg_policies\n    WHERE tablename = 'workflow_executions'\n    AND policyname = 'Users can view their own workflow executions'\n  ) THEN\n    CREATE POLICY "Users can view their own workflow executions" \n      ON public.workflow_executions \n      FOR SELECT \n      USING (auth.uid() = created_by);
\n  END IF;
\n\n  IF NOT EXISTS (\n    SELECT 1 FROM pg_policies\n    WHERE tablename = 'workflow_executions'\n    AND policyname = 'Users can create workflow executions'\n  ) THEN\n    CREATE POLICY "Users can create workflow executions" \n      ON public.workflow_executions \n      FOR INSERT \n      WITH CHECK (auth.uid() = created_by);
\n  END IF;
\nEND $$;
\n\n-- Create index for better performance (check if not exists)\nDO $$ \nBEGIN\n    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_workflow_executions_workflow_id') THEN\n        CREATE INDEX idx_workflow_executions_workflow_id ON public.workflow_executions(workflow_id);
\n    END IF;
\n    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_workflow_executions_created_by') THEN\n        CREATE INDEX idx_workflow_executions_created_by ON public.workflow_executions(created_by);
\n    END IF;
\nEND $$;
\n\n-- Create workflow_schedules table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.workflow_schedules (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  workflow_id UUID REFERENCES workflow_configurations(id),\n  cron_schedule TEXT NOT NULL,\n  context JSONB,\n  is_active BOOLEAN NOT NULL DEFAULT true,\n  last_run TIMESTAMP WITH TIME ZONE,\n  next_run TIMESTAMP WITH TIME ZONE,\n  created_by UUID REFERENCES auth.users(id),\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Enable RLS on workflow_schedules table\nALTER TABLE public.workflow_schedules ENABLE ROW LEVEL SECURITY;
\n\n-- Drop existing policies first to avoid conflicts\nDROP POLICY IF EXISTS "Users can view their own workflow schedules" ON public.workflow_schedules;
\nDROP POLICY IF EXISTS "Users can create workflow schedules" ON public.workflow_schedules;
\nDROP POLICY IF EXISTS "Users can update their own workflow schedules" ON public.workflow_schedules;
\nDROP POLICY IF EXISTS "Users can delete their own workflow schedules" ON public.workflow_schedules;
\nDROP POLICY IF EXISTS "Users can manage their own workflow_schedules" ON public.workflow_schedules;
\n\n-- Create RLS policies for workflow_schedules only if they don't exist\nDO $$\nBEGIN\n  IF NOT EXISTS (\n    SELECT 1 FROM pg_policies\n    WHERE tablename = 'workflow_schedules'\n    AND policyname = 'Users can view their own workflow schedules'\n  ) THEN\n    CREATE POLICY "Users can view their own workflow schedules" \n      ON public.workflow_schedules \n      FOR SELECT \n      USING (auth.uid() = created_by);
\n  END IF;
\n\n  IF NOT EXISTS (\n    SELECT 1 FROM pg_policies\n    WHERE tablename = 'workflow_schedules'\n    AND policyname = 'Users can create workflow schedules'\n  ) THEN\n    CREATE POLICY "Users can create workflow schedules" \n      ON public.workflow_schedules \n      FOR INSERT \n      WITH CHECK (auth.uid() = created_by);
\n  END IF;
\n\n  IF NOT EXISTS (\n    SELECT 1 FROM pg_policies\n    WHERE tablename = 'workflow_schedules'\n    AND policyname = 'Users can update their own workflow schedules'\n  ) THEN\n    CREATE POLICY "Users can update their own workflow schedules" \n      ON public.workflow_schedules \n      FOR UPDATE \n      USING (auth.uid() = created_by);
\n  END IF;
\n\n  IF NOT EXISTS (\n    SELECT 1 FROM pg_policies\n    WHERE tablename = 'workflow_schedules'\n    AND policyname = 'Users can delete their own workflow schedules'\n  ) THEN\n    CREATE POLICY "Users can delete their own workflow schedules" \n      ON public.workflow_schedules \n      FOR DELETE \n      USING (auth.uid() = created_by);
\n  END IF;
\nEND $$;
\n\n-- Create index for better performance\nCREATE INDEX IF NOT EXISTS idx_workflow_schedules_workflow_id ON public.workflow_schedules(workflow_id);
\nCREATE INDEX IF NOT EXISTS idx_workflow_schedules_created_by ON public.workflow_schedules(created_by);
;
