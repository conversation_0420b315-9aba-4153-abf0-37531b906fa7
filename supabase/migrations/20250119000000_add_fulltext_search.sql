\n-- Add full-text search columns to candidates table (only if not exists)\nALTER TABLE public.candidates \nADD COLUMN IF NOT EXISTS search_vector tsvector;
\n\n-- Add full-text search columns to jobs table (only if not exists) \nALTER TABLE public.jobs\nADD COLUMN IF NOT EXISTS search_vector tsvector;
\n\n-- Create function to update candidates search vector\nCREATE OR REPLACE FUNCTION update_candidates_search_vector()\nRETURNS trigger AS $$\nBEGIN\n  NEW.search_vector := \n    setweight(to_tsvector('english', COALESCE(NEW.name, '')), 'A') ||\n    setweight(to_tsvector('english', COALESCE(NEW.role, '')), 'A') ||\n    setweight(to_tsvector('english', COALESCE(NEW.email, '')), 'B') ||\n    setweight(to_tsvector('english', COALESCE(NEW.location, '')), 'B') ||\n    setweight(to_tsvector('english', COALESCE(NEW.experience, '')), 'C') ||\n    setweight(to_tsvector('english', COALESCE(NEW.industry, '')), 'C') ||\n    setweight(to_tsvector('english', COALESCE(NEW.ai_summary, '')), 'D') ||\n    setweight(to_tsvector('english', COALESCE(array_to_string(NEW.tags, ' '), '')), 'B');
\n  RETURN NEW;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Create function to update jobs search vector\nCREATE OR REPLACE FUNCTION update_jobs_search_vector()\nRETURNS trigger AS $$\nBEGIN\n  NEW.search_vector := \n    setweight(to_tsvector('english', COALESCE(NEW.title, '')), 'A') ||\n    setweight(to_tsvector('english', COALESCE(NEW.department, '')), 'B') ||\n    setweight(to_tsvector('english', COALESCE(NEW.location, '')), 'B') ||\n    setweight(to_tsvector('english', COALESCE(NEW.description, '')), 'C') ||\n    setweight(to_tsvector('english', COALESCE(NEW.job_type, '')), 'C') ||\n    setweight(to_tsvector('english', COALESCE(NEW.experience_required, '')), 'C') ||\n    setweight(to_tsvector('english', COALESCE(array_to_string(NEW.requirements, ' '), '')), 'D') ||\n    setweight(to_tsvector('english', COALESCE(array_to_string(NEW.benefits, ' '), '')), 'D');
\n  RETURN NEW;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Create triggers to automatically update search vectors\nDROP TRIGGER IF EXISTS candidates_search_vector_update ON public.candidates;
\nCREATE TRIGGER candidates_search_vector_update\n  BEFORE INSERT OR UPDATE ON public.candidates\n  FOR EACH ROW EXECUTE FUNCTION update_candidates_search_vector();
\n\nDROP TRIGGER IF EXISTS jobs_search_vector_update ON public.jobs;
\nCREATE TRIGGER jobs_search_vector_update\n  BEFORE INSERT OR UPDATE ON public.jobs\n  FOR EACH ROW EXECUTE FUNCTION update_jobs_search_vector();
\n\n-- Create GIN indexes for fast full-text search\nCREATE INDEX IF NOT EXISTS candidates_search_vector_idx ON public.candidates USING gin(search_vector);
\nCREATE INDEX IF NOT EXISTS jobs_search_vector_idx ON public.jobs USING gin(search_vector);
\n\n-- Update existing records with search vectors\n-- Using DO block to avoid column ambiguity issues\nDO $$\nBEGIN\n  -- Update candidates search vectors\n  UPDATE public.candidates SET search_vector = \n    setweight(to_tsvector('english', COALESCE(name, '')), 'A') ||\n    setweight(to_tsvector('english', COALESCE(role, '')), 'A') ||\n    setweight(to_tsvector('english', COALESCE(email, '')), 'B') ||\n    setweight(to_tsvector('english', COALESCE(location, '')), 'B') ||\n    setweight(to_tsvector('english', COALESCE(experience, '')), 'C') ||\n    setweight(to_tsvector('english', COALESCE(industry, '')), 'C') ||\n    setweight(to_tsvector('english', COALESCE(ai_summary, '')), 'D') ||\n    setweight(to_tsvector('english', COALESCE(array_to_string(tags, ' '), '')), 'B')\n  WHERE search_vector IS NULL;
\n\n  -- Update jobs search vectors\n  UPDATE public.jobs SET search_vector = \n    setweight(to_tsvector('english', COALESCE(title, '')), 'A') ||\n    setweight(to_tsvector('english', COALESCE(department, '')), 'B') ||\n    setweight(to_tsvector('english', COALESCE(location, '')), 'B') ||\n    setweight(to_tsvector('english', COALESCE(description, '')), 'C') ||\n    setweight(to_tsvector('english', COALESCE(job_type, '')), 'C') ||\n    setweight(to_tsvector('english', COALESCE(experience_required, '')), 'C') ||\n    setweight(to_tsvector('english', COALESCE(array_to_string(requirements, ' '), '')), 'D') ||\n    setweight(to_tsvector('english', COALESCE(array_to_string(benefits, ' '), '')), 'D')\n  WHERE search_vector IS NULL;
\nEND $$;
\n;
