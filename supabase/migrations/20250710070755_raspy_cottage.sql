\n\n-- Drop the existing problematic SELECT policy\nDROP POLICY IF EXISTS "Users can read feature flags" ON public.feature_flags;
\n\n-- Create a new SELECT policy that properly checks for authenticated users\nCREATE POLICY "Authenticated users can read feature flags"\n  ON public.feature_flags\n  FOR SELECT\n  TO authenticated\n  USING (true);
\n\n-- Also allow public (unauthenticated) users to read feature flags if needed\n-- This is common for feature flags that control public-facing features\nCREATE POLICY "Public users can read feature flags"\n  ON public.feature_flags\n  FOR SELECT\n  TO public\n  USING (true);
;
