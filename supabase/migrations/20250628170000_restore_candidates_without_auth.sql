\n\n-- First, let's add a user_id column if it doesn't exist and make it nullable temporarily\nDO $$ \nBEGIN\n    -- Check if user_id column exists, if not add it\n    IF NOT EXISTS (\n        SELECT 1 FROM information_schema.columns \n        WHERE table_name = 'candidates' AND column_name = 'user_id'\n    ) THEN\n        ALTER TABLE public.candidates ADD COLUMN user_id UUID;
\n    END IF;
\n    \n    -- Make user_id nullable temporarily for data insertion\n    ALTER TABLE public.candidates ALTER COLUMN user_id DROP NOT NULL;
\nEND $$;
\n\n-- Clear any existing candidates to avoid conflicts\nTRUNCATE TABLE public.candidates;
\n\n-- Insert the comprehensive candidate profiles using only existing columns\nINSERT INTO public.candidates (\n    name, role, email, phone, location, avatar, recruiter_name,\n    tags, github_url, linkedin_url, relationship_score, experience, industry,\n    remote_preference, visa_status, skills, ai_summary\n) VALUES \n(\n    '<PERSON>', 'Senior Frontend Developer', '<EMAIL>', '+****************',\n    'San Francisco, CA', '/placeholder.svg', '<PERSON>',\n    ARRAY['React', 'TypeScript', 'Next.js', 'TailwindCSS'], 'https://github.com/sarahj',\n    'https://linkedin.com/in/sarahj', 85, '8 years', 'Technology',\n    'Hybrid', 'US Citizen', '[\n        {"name": "React", "level": "expert", "years": 6},\n        {"name": "TypeScript", "level": "expert", "years": 5},\n        {"name": "Next.js", "level": "intermediate", "years": 3},\n        {"name": "TailwindCSS", "level": "expert", "years": 4}\n    ]'::jsonb, 'Experienced frontend developer with strong React and TypeScript skills. Shows great potential for leadership roles.'\n),\n(\n    'Michael Chen', 'Full Stack Engineer', '<EMAIL>', '+****************',\n    'New York, NY', '/placeholder.svg', 'Emma Wilson',\n    ARRAY['Node.js', 'React', 'MongoDB', 'AWS'], 'https://github.com/michaelc',\n    'https://linkedin.com/in/michaelc', 75, '5 years', 'Finance',\n    'Remote', 'H1B', '[\n        {"name": "Node.js", "level": "expert", "years": 5},\n        {"name": "React", "level": "intermediate", "years": 3},\n        {"name": "MongoDB", "level": "intermediate", "years": 4},\n        {"name": "AWS", "level": "intermediate", "years": 2}\n    ]'::jsonb, 'Versatile full-stack engineer with strong backend experience in Node.js and cloud technologies.'\n),\n(\n    'Emily Rodriguez', 'UI/UX Designer', '<EMAIL>', '+1 (555) 345-6789',\n    'Austin, TX', '/placeholder.svg', 'David Kim',\n    ARRAY['Figma', 'User Research', 'Prototyping', 'Design Systems'], NULL,\n    'https://linkedin.com/in/emilyr', 90, '6 years', 'Design Agency',\n    'On-site', 'US Citizen', '[\n        {"name": "Figma", "level": "expert", "years": 4},\n        {"name": "User Research", "level": "expert", "years": 6},\n        {"name": "Prototyping", "level": "expert", "years": 5},\n        {"name": "Design Systems", "level": "intermediate", "years": 3}\n    ]'::jsonb, 'Creative UI/UX designer with extensive user research experience and design systems expertise.'\n),\n(\n    'David Wilson', 'DevOps Engineer', '<EMAIL>', '+1 (555) 456-7890',\n    'Seattle, WA', '/placeholder.svg', 'Sophie Lee',\n    ARRAY['Kubernetes', 'Docker', 'AWS', 'CI/CD'], 'https://github.com/davidw',\n    'https://linkedin.com/in/davidw', 78, '7 years', 'Cloud Computing',\n    'Remote', 'US Citizen', '[\n        {"name": "Kubernetes", "level": "expert", "years": 5},\n        {"name": "Docker", "level": "expert", "years": 6},\n        {"name": "AWS", "level": "expert", "years": 7},\n        {"name": "CI/CD", "level": "intermediate", "years": 4}\n    ]'::jsonb, 'Expert DevOps engineer specializing in containerization and cloud infrastructure automation.'\n),\n(\n    'Lisa Zhang', 'Data Scientist', '<EMAIL>', '+1 (555) 567-8901',\n    'Boston, MA', '/placeholder.svg', 'James Brown',\n    ARRAY['Python', 'Machine Learning', 'TensorFlow', 'SQL'], 'https://github.com/lisaz',\n    'https://linkedin.com/in/lisaz', 92, '4 years', 'AI/ML',\n    'Hybrid', 'Green Card', '[\n        {"name": "Python", "level": "expert", "years": 4},\n        {"name": "Machine Learning", "level": "expert", "years": 3},\n        {"name": "TensorFlow", "level": "intermediate", "years": 2},\n        {"name": "SQL", "level": "expert", "years": 4}\n    ]'::jsonb, 'Talented data scientist with expertise in machine learning and predictive analytics.'\n),\n(\n    'Marcus Johnson', 'Backend Developer', '<EMAIL>', '+1 (555) 678-9012',\n    'Chicago, IL', '/placeholder.svg', 'Rachel Green',\n    ARRAY['Java', 'Spring Boot', 'PostgreSQL', 'Microservices'], 'https://github.com/marcusj',\n    'https://linkedin.com/in/marcusj', 83, '6 years', 'E-commerce',\n    'Remote', 'US Citizen', '[\n        {"name": "Java", "level": "expert", "years": 6},\n        {"name": "Spring Boot", "level": "expert", "years": 4},\n        {"name": "PostgreSQL", "level": "intermediate", "years": 5},\n        {"name": "Microservices", "level": "expert", "years": 3}\n    ]'::jsonb, 'Experienced backend developer with strong Java and microservices architecture knowledge.'\n),\n(\n    'Sophia Patel', 'Mobile Developer', '<EMAIL>', '+****************',\n    'Los Angeles, CA', '/placeholder.svg', 'Tom Wilson',\n    ARRAY['React Native', 'iOS', 'Android', 'Mobile Architecture'], 'https://github.com/sophiap',\n    'https://linkedin.com/in/sophiap', 88, '5 years', 'Mobile Apps',\n    'Hybrid', 'H1B', '[\n        {"name": "React Native", "level": "expert", "years": 4},\n        {"name": "iOS", "level": "intermediate", "years": 5},\n        {"name": "Android", "level": "intermediate", "years": 5},\n        {"name": "Mobile Architecture", "level": "expert", "years": 3}\n    ]'::jsonb, 'Skilled mobile developer with cross-platform expertise and strong architecture design skills.'\n),\n(\n    'Alex Rivera', 'Security Engineer', '<EMAIL>', '+****************',\n    'Miami, FL', '/placeholder.svg', 'Linda Chen',\n    ARRAY['Cybersecurity', 'Penetration Testing', 'Security Architecture'], 'https://github.com/alexr',\n    'https://linkedin.com/in/alexr', 86, '7 years', 'Cybersecurity',\n    'Remote', 'US Citizen', '[\n        {"name": "Cybersecurity", "level": "expert", "years": 7},\n        {"name": "Penetration Testing", "level": "expert", "years": 5},\n        {"name": "Security Architecture", "level": "intermediate", "years": 4},\n        {"name": "Cloud Security", "level": "expert", "years": 3}\n    ]'::jsonb, 'Expert security engineer with comprehensive cybersecurity and penetration testing experience.'\n),\n(\n    'Emma Thompson', 'Product Manager', '<EMAIL>', '+****************',\n    'Denver, CO', '/placeholder.svg', 'Mark Davis',\n    ARRAY['Product Strategy', 'Agile', 'User Research', 'Analytics'], NULL,\n    'https://linkedin.com/in/emmat', 91, '8 years', 'Product Management',\n    'Hybrid', 'US Citizen', '[\n        {"name": "Product Strategy", "level": "expert", "years": 8},\n        {"name": "Agile", "level": "expert", "years": 6},\n        {"name": "User Research", "level": "intermediate", "years": 5},\n        {"name": "Analytics", "level": "expert", "years": 4}\n    ]'::jsonb, 'Strategic product manager with strong analytical skills and extensive experience in agile methodologies.'\n),\n(\n    'Alex Johnson', 'Senior Frontend Developer', '<EMAIL>', '+****************',\n    'San Francisco, CA', '/placeholder.svg', 'John Smith',\n    ARRAY['React', 'TypeScript', 'Next.js'], 'https://github.com/alexj',\n    'https://linkedin.com/in/alexj', 85, '7 years', 'Technology',\n    'Remote', 'US Citizen', '[\n        {"name": "React", "level": "expert", "years": 6},\n        {"name": "TypeScript", "level": "expert", "years": 5},\n        {"name": "Next.js", "level": "advanced", "years": 3}\n    ]'::jsonb, 'Experienced frontend developer with strong React and TypeScript skills.'\n),\n(\n    'Maria Garcia', 'Product Manager', '<EMAIL>', '+****************',\n    'New York, NY', '/placeholder.svg', 'Jane Doe',\n    ARRAY['Product Strategy', 'Agile', 'User Research'], NULL,\n    'https://linkedin.com/in/mariag', 92, '5 years', 'SaaS',\n    'Hybrid', 'US Citizen', '[\n        {"name": "Product Strategy", "level": "expert", "years": 5},\n        {"name": "Agile", "level": "expert", "years": 4},\n        {"name": "User Research", "level": "intermediate", "years": 3}\n    ]'::jsonb, 'Strategic product manager with strong analytical background.'\n);
\n\n-- Update RLS policies to allow viewing without authentication for now\nDROP POLICY IF EXISTS "Authenticated users can view candidates" ON public.candidates;
\nDROP POLICY IF EXISTS "Authenticated users can create candidates" ON public.candidates;
\nDROP POLICY IF EXISTS "Authenticated users can update candidates" ON public.candidates;
\nDROP POLICY IF EXISTS "Authenticated users can delete candidates" ON public.candidates;
\n\n-- Create more permissive policies for now\nCREATE POLICY "Anyone can view candidates" \n  ON public.candidates \n  FOR SELECT \n  USING (true);
\n\nCREATE POLICY "Anyone can create candidates" \n  ON public.candidates \n  FOR INSERT \n  WITH CHECK (true);
\n\nCREATE POLICY "Anyone can update candidates" \n  ON public.candidates \n  FOR UPDATE \n  USING (true);
\n\nCREATE POLICY "Anyone can delete candidates" \n  ON public.candidates \n  FOR DELETE \n  USING (true);
\n;
