\n\n-- Create workflow_metrics table\nCREATE TABLE IF NOT EXISTS public.workflow_metrics (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  workflow_id UUID REFERENCES public.workflow_configurations(id) ON DELETE CASCADE NOT NULL,\n  execution_id UUID REFERENCES public.workflow_executions(id) ON DELETE CASCADE NOT NULL,\n  \n  -- Execution metrics\n  execution_duration_ms INTEGER NOT NULL,\n  start_time TIMESTAMP WITH TIME ZONE NOT NULL,\n  end_time TIMESTAMP WITH TIME ZONE NOT NULL,\n  \n  -- Node-level metrics\n  total_nodes_executed INTEGER NOT NULL DEFAULT 0,\n  successful_nodes INTEGER NOT NULL DEFAULT 0,\n  failed_nodes INTEGER NOT NULL DEFAULT 0,\n  skipped_nodes INTEGER NOT NULL DEFAULT 0,\n  \n  -- Node error details (JSONB array)\n  node_errors JSONB DEFAULT '[]'::jsonb,\n  \n  -- Memory and performance metrics\n  peak_memory_mb FLOAT,\n  cpu_usage_percent FLOAT,\n  \n  -- Additional metadata\n  context_data JSONB,\n  error_details TEXT,\n  \n  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create workflow_alerts table for alert configurations\nCREATE TABLE IF NOT EXISTS public.workflow_alerts (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  workflow_id UUID REFERENCES public.workflow_configurations(id) ON DELETE CASCADE NOT NULL,\n  \n  -- Alert channels\n  enable_email BOOLEAN NOT NULL DEFAULT false,\n  email_addresses TEXT[], -- Array of email addresses\n  \n  enable_slack BOOLEAN NOT NULL DEFAULT false,\n  slack_webhook_url TEXT,\n  slack_channel TEXT,\n  \n  -- Alert conditions\n  alert_on_failure BOOLEAN NOT NULL DEFAULT true,\n  alert_on_success BOOLEAN NOT NULL DEFAULT false,\n  alert_on_duration_threshold BOOLEAN NOT NULL DEFAULT false,\n  duration_threshold_ms INTEGER, -- Alert if execution takes longer than this\n  \n  -- Rate limiting\n  max_alerts_per_hour INTEGER NOT NULL DEFAULT 5,\n  \n  is_active BOOLEAN NOT NULL DEFAULT true,\n  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create workflow_alert_history table\nCREATE TABLE IF NOT EXISTS public.workflow_alert_history (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  alert_id UUID REFERENCES public.workflow_alerts(id) ON DELETE CASCADE NOT NULL,\n  execution_id UUID REFERENCES public.workflow_executions(id) ON DELETE CASCADE NOT NULL,\n  \n  alert_type TEXT NOT NULL, -- 'failure', 'success', 'duration_exceeded'\n  alert_channel TEXT NOT NULL, -- 'email', 'slack'\n  recipient TEXT NOT NULL, -- email address or slack channel\n  \n  alert_status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'sent', 'failed'\n  alert_message TEXT NOT NULL,\n  error_details TEXT,\n  \n  sent_at TIMESTAMP WITH TIME ZONE,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Enable RLS on all new tables\nALTER TABLE public.workflow_metrics ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.workflow_alerts ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.workflow_alert_history ENABLE ROW LEVEL SECURITY;
\n\n-- Create RLS policies for workflow_metrics\nCREATE POLICY "Users can view their own workflow metrics"\n  ON public.workflow_metrics\n  FOR SELECT\n  USING (auth.uid() = created_by);
\n\nCREATE POLICY "Users can create their own workflow metrics"\n  ON public.workflow_metrics\n  FOR INSERT\n  WITH CHECK (auth.uid() = created_by);
\n\n-- Create RLS policies for workflow_alerts\nCREATE POLICY "Users can view their own workflow alerts"\n  ON public.workflow_alerts\n  FOR SELECT\n  USING (auth.uid() = created_by);
\n\nCREATE POLICY "Users can create their own workflow alerts"\n  ON public.workflow_alerts\n  FOR INSERT\n  WITH CHECK (auth.uid() = created_by);
\n\nCREATE POLICY "Users can update their own workflow alerts"\n  ON public.workflow_alerts\n  FOR UPDATE\n  USING (auth.uid() = created_by);
\n\nCREATE POLICY "Users can delete their own workflow alerts"\n  ON public.workflow_alerts\n  FOR DELETE\n  USING (auth.uid() = created_by);
\n\n-- Create RLS policies for workflow_alert_history\nCREATE POLICY "Users can view their own alert history"\n  ON public.workflow_alert_history\n  FOR SELECT\n  USING (\n    EXISTS (\n      SELECT 1 FROM public.workflow_alerts\n      WHERE workflow_alerts.id = workflow_alert_history.alert_id\n      AND workflow_alerts.created_by = auth.uid()\n    )\n  );
\n\n-- Create indexes for better performance\nCREATE INDEX IF NOT EXISTS idx_workflow_metrics_workflow_id ON public.workflow_metrics(workflow_id);
\nCREATE INDEX IF NOT EXISTS idx_workflow_metrics_execution_id ON public.workflow_metrics(execution_id);
\nCREATE INDEX IF NOT EXISTS idx_workflow_metrics_created_at ON public.workflow_metrics(created_at);
\nCREATE INDEX IF NOT EXISTS idx_workflow_metrics_created_by ON public.workflow_metrics(created_by);
\n\nCREATE INDEX IF NOT EXISTS idx_workflow_alerts_workflow_id ON public.workflow_alerts(workflow_id);
\nCREATE INDEX IF NOT EXISTS idx_workflow_alerts_created_by ON public.workflow_alerts(created_by);
\n\nCREATE INDEX IF NOT EXISTS idx_workflow_alert_history_alert_id ON public.workflow_alert_history(alert_id);
\nCREATE INDEX IF NOT EXISTS idx_workflow_alert_history_execution_id ON public.workflow_alert_history(execution_id);
\nCREATE INDEX IF NOT EXISTS idx_workflow_alert_history_sent_at ON public.workflow_alert_history(sent_at);
\n\n-- Create trigger to update updated_at timestamp for workflow_alerts\nCREATE TRIGGER update_workflow_alerts_updated_at\n  BEFORE UPDATE ON public.workflow_alerts\n  FOR EACH ROW\n  EXECUTE FUNCTION update_updated_at_column();
\n\n-- Create function to check alert rate limits\nCREATE OR REPLACE FUNCTION check_alert_rate_limit(p_alert_id UUID)\nRETURNS BOOLEAN AS $$\nDECLARE\n  v_max_alerts_per_hour INTEGER;
\n  v_alerts_sent_last_hour INTEGER;
\nBEGIN\n  -- Get the max alerts per hour setting\n  SELECT max_alerts_per_hour INTO v_max_alerts_per_hour\n  FROM public.workflow_alerts\n  WHERE id = p_alert_id;
\n\n  -- Count alerts sent in the last hour\n  SELECT COUNT(*) INTO v_alerts_sent_last_hour\n  FROM public.workflow_alert_history\n  WHERE alert_id = p_alert_id\n    AND alert_status = 'sent'\n    AND sent_at > NOW() - INTERVAL '1 hour';
\n\n  RETURN v_alerts_sent_last_hour < v_max_alerts_per_hour;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Create view for workflow execution statistics\nCREATE OR REPLACE VIEW workflow_execution_stats AS\nSELECT \n  wc.id as workflow_id,\n  wc.name as workflow_name,\n  COUNT(DISTINCT wm.execution_id) as total_executions,\n  COUNT(DISTINCT CASE WHEN we.status = 'completed' THEN we.id END) as successful_executions,\n  COUNT(DISTINCT CASE WHEN we.status = 'failed' THEN we.id END) as failed_executions,\n  AVG(wm.execution_duration_ms) as avg_duration_ms,\n  MIN(wm.execution_duration_ms) as min_duration_ms,\n  MAX(wm.execution_duration_ms) as max_duration_ms,\n  SUM(wm.failed_nodes) as total_failed_nodes,\n  AVG(wm.successful_nodes::float / NULLIF(wm.total_nodes_executed, 0)) as avg_success_rate\nFROM public.workflow_configurations wc\nLEFT JOIN public.workflow_metrics wm ON wc.id = wm.workflow_id\nLEFT JOIN public.workflow_executions we ON wm.execution_id = we.id\nWHERE wc.created_by = auth.uid()\nGROUP BY wc.id, wc.name;
\n;
