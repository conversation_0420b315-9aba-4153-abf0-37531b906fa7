-- Set FULL replica identity for critical tables to optimize real-time performance
-- This ensures all column changes are included in the real-time payload

-- Events table
ALTER TABLE public.events REPLICA IDENTITY FULL;

-- Notifications table  
ALTER TABLE public.notifications R<PERSON><PERSON><PERSON>A IDENTITY FULL;

-- Tasks table
ALTER TABLE public.tasks <PERSON>EP<PERSON><PERSON>A IDENTITY FULL;

-- User feedback table
ALTER TABLE public.user_feedback REP<PERSON><PERSON>A IDENTITY FULL;

-- System health table
ALTER TABLE public.system_health REPLICA IDENTITY FULL;

-- Profiles table
ALTER TABLE public.profiles REPLICA IDENTITY FULL;

-- Candidate timeline table (important for activity tracking)
ALTER TABLE public.candidate_timeline REPLICA IDENTITY FULL;

-- Workflow executions (for real-time workflow monitoring)
ALTER TABLE public.workflow_executions REPLICA IDENTITY FULL;

-- Feature flags (for real-time feature updates)
ALTER TABLE public.feature_flags REPLIC<PERSON> IDENTITY FULL;;
