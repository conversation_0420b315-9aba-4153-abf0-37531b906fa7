\n\n-- Create events table if it doesn't exist\nCREATE TABLE IF NOT EXISTS public.events (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  title TEXT NOT NULL,\n  description TEXT,\n  start_time TIMESTAMP WITH TIME ZONE NOT NULL,\n  end_time TIMESTAMP WITH TIME ZONE NOT NULL,\n  location TEXT,\n  meeting_link TEXT,\n  event_type TEXT NOT NULL DEFAULT 'meeting', -- 'meeting', 'interview', 'call', 'presentation', 'other'\n  priority TEXT NOT NULL DEFAULT 'medium', -- 'low', 'medium', 'high'\n  category TEXT NOT NULL DEFAULT 'general', -- 'general', 'recruitment', 'client', 'internal'\n  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Enable RLS on events table\nALTER TABLE public.events ENABLE ROW LEVEL SECURITY;
\n\n-- Drop existing events policies if they exist\nDROP POLICY IF EXISTS "Users can manage their own events" ON public.events;
\n\n-- Create RLS policies for events\nCREATE POLICY "Users can manage their own events"\n  ON public.events\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\n-- Create indexes for better performance\nCREATE INDEX IF NOT EXISTS idx_events_user_id ON public.events(user_id);
\nCREATE INDEX IF NOT EXISTS idx_events_start_time ON public.events(start_time);
\nCREATE INDEX IF NOT EXISTS idx_events_event_type ON public.events(event_type);
\nCREATE INDEX IF NOT EXISTS idx_events_category ON public.events(category);
;
