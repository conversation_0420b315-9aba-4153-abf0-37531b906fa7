-- Create feature flags table\nCREATE TABLE IF NOT EXISTS feature_flags (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  name TEXT UNIQUE NOT NULL,\n  description TEXT,\n  enabled BOOLEAN DEFAULT false,\n  pilot_enabled BOOLEAN DEFAULT false,\n  pilot_user_ids UUID[] DEFAULT '{}',\n  rollout_percentage INTEGER DEFAULT 0 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),\n  metadata JSONB DEFAULT '{}',\n  created_at TIMESTAMPTZ DEFAULT now(),\n  updated_at TIMESTAMPTZ DEFAULT now()\n);
\n\n-- Create feature flag access logs table for telemetry\nCREATE TABLE IF NOT EXISTS feature_flag_access_logs (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\n  flag_name TEXT NOT NULL,\n  has_access BOOLEAN NOT NULL,\n  is_pilot_user BOOLEAN DEFAULT false,\n  accessed_at TIMESTAMPTZ DEFAULT now()\n);
\n\n-- Create indexes for feature flag access logs\nCREATE INDEX idx_feature_flag_access_user_flag ON feature_flag_access_logs (user_id, flag_name);
\nCREATE INDEX idx_feature_flag_access_time ON feature_flag_access_logs (accessed_at DESC);
\n\n-- Create workflow telemetry table for detailed usage tracking\nCREATE TABLE IF NOT EXISTS workflow_telemetry (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\n  workflow_id UUID REFERENCES workflow_configurations(id) ON DELETE CASCADE,\n  event_type TEXT NOT NULL, -- 'created', 'updated', 'executed', 'shared', 'deleted', etc.\n  event_data JSONB DEFAULT '{}',\n  session_id TEXT,\n  ip_address INET,\n  user_agent TEXT,\n  created_at TIMESTAMPTZ DEFAULT now()\n);
\n\n-- Create indexes for workflow telemetry\nCREATE INDEX idx_workflow_telemetry_user ON workflow_telemetry (user_id);
\nCREATE INDEX idx_workflow_telemetry_workflow ON workflow_telemetry (workflow_id);
\nCREATE INDEX idx_workflow_telemetry_event ON workflow_telemetry (event_type);
\nCREATE INDEX idx_workflow_telemetry_time ON workflow_telemetry (created_at DESC);
\n\n-- Create user feedback table for in-app feedback\nCREATE TABLE IF NOT EXISTS user_feedback (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\n  feature_name TEXT NOT NULL,\n  feedback_type TEXT NOT NULL CHECK (feedback_type IN ('bug', 'feature', 'improvement', 'praise')),\n  rating INTEGER CHECK (rating >= 1 AND rating <= 5),\n  message TEXT NOT NULL,\n  metadata JSONB DEFAULT '{}',\n  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved', 'wontfix')),\n  created_at TIMESTAMPTZ DEFAULT now(),\n  updated_at TIMESTAMPTZ DEFAULT now()\n);
\n\n-- Create indexes for user feedback\nCREATE INDEX idx_user_feedback_user ON user_feedback (user_id);
\nCREATE INDEX idx_user_feedback_feature ON user_feedback (feature_name);
\nCREATE INDEX idx_user_feedback_status ON user_feedback (status);
\n\n-- Create missing node/integration requests table\nCREATE TABLE IF NOT EXISTS feature_requests (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\n  request_type TEXT NOT NULL CHECK (request_type IN ('node', 'integration', 'feature')),\n  name TEXT NOT NULL,\n  description TEXT,\n  use_case TEXT,\n  priority INTEGER DEFAULT 0,\n  vote_count INTEGER DEFAULT 1,\n  status TEXT DEFAULT 'requested' CHECK (status IN ('requested', 'planned', 'in_progress', 'completed', 'declined')),\n  metadata JSONB DEFAULT '{}',\n  created_at TIMESTAMPTZ DEFAULT now(),\n  updated_at TIMESTAMPTZ DEFAULT now()\n);
\n\n-- Create indexes for feature requests\nCREATE INDEX idx_feature_requests_type ON feature_requests (request_type);
\nCREATE INDEX idx_feature_requests_status ON feature_requests (status);
\nCREATE INDEX idx_feature_requests_votes ON feature_requests (vote_count DESC);
\n\n-- Create feature request votes table\nCREATE TABLE IF NOT EXISTS feature_request_votes (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  request_id UUID NOT NULL REFERENCES feature_requests(id) ON DELETE CASCADE,\n  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\n  created_at TIMESTAMPTZ DEFAULT now(),\n  \n  -- Ensure one vote per user per request\n  UNIQUE(request_id, user_id)\n);
\n\n-- Insert initial feature flags\nINSERT INTO feature_flags (name, description, enabled, pilot_enabled, rollout_percentage) VALUES\n  ('ai_workflows', 'AI-powered workflow creation and management', false, true, 0),\n  ('workflow_templates', 'Pre-built workflow templates library', false, true, 0),\n  ('advanced_analytics', 'Advanced analytics and insights dashboard', false, true, 0),\n  ('realtime_collaboration', 'Real-time collaboration on workflows', false, false, 0),\n  ('custom_integrations', 'Custom third-party integrations', false, true, 0),\n  ('workflow_scheduler', 'Advanced workflow scheduling capabilities', false, true, 0),\n  ('ai_suggestions', 'AI-powered workflow suggestions and optimization', false, true, 0),\n  ('performance_insights', 'Detailed performance insights and monitoring', false, true, 0)\nON CONFLICT (name) DO NOTHING;
\n\n-- Create functions for telemetry tracking\nCREATE OR REPLACE FUNCTION track_workflow_event(\n  p_user_id UUID,\n  p_workflow_id UUID,\n  p_event_type TEXT,\n  p_event_data JSONB DEFAULT '{}',\n  p_session_id TEXT DEFAULT NULL\n) RETURNS UUID AS $$\nDECLARE\n  v_telemetry_id UUID;
\nBEGIN\n  INSERT INTO workflow_telemetry (\n    user_id,\n    workflow_id,\n    event_type,\n    event_data,\n    session_id\n  ) VALUES (\n    p_user_id,\n    p_workflow_id,\n    p_event_type,\n    p_event_data,\n    p_session_id\n  ) RETURNING id INTO v_telemetry_id;
\n  \n  RETURN v_telemetry_id;
\nEND;
\n$$ LANGUAGE plpgsql SECURITY DEFINER;
\n\n-- Create function to update feature request votes\nCREATE OR REPLACE FUNCTION update_feature_request_votes() RETURNS TRIGGER AS $$\nBEGIN\n  IF TG_OP = 'INSERT' THEN\n    UPDATE feature_requests\n    SET vote_count = vote_count + 1,\n        updated_at = now()\n    WHERE id = NEW.request_id;
\n  ELSIF TG_OP = 'DELETE' THEN\n    UPDATE feature_requests\n    SET vote_count = GREATEST(0, vote_count - 1),\n        updated_at = now()\n    WHERE id = OLD.request_id;
\n  END IF;
\n  RETURN NULL;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Create trigger for vote count updates\nCREATE TRIGGER update_feature_request_vote_count\n  AFTER INSERT OR DELETE ON feature_request_votes\n  FOR EACH ROW\n  EXECUTE FUNCTION update_feature_request_votes();
\n\n-- Create views for analytics\nCREATE OR REPLACE VIEW feature_usage_summary AS\nSELECT \n  f.name as feature_name,\n  f.description,\n  f.enabled,\n  f.pilot_enabled,\n  f.rollout_percentage,\n  COUNT(DISTINCT l.user_id) as unique_users,\n  COUNT(l.id) as total_accesses,\n  COUNT(CASE WHEN l.is_pilot_user THEN 1 END) as pilot_accesses,\n  COUNT(CASE WHEN l.has_access THEN 1 END) as granted_accesses,\n  MAX(l.accessed_at) as last_accessed\nFROM feature_flags f\nLEFT JOIN feature_flag_access_logs l ON f.name = l.flag_name\n  AND l.accessed_at >= now() - INTERVAL '30 days'\nGROUP BY f.name, f.description, f.enabled, f.pilot_enabled, f.rollout_percentage;
\n\nCREATE OR REPLACE VIEW workflow_usage_summary AS\nSELECT \n  DATE_TRUNC('day', created_at) as date,\n  COUNT(DISTINCT user_id) as active_users,\n  COUNT(DISTINCT workflow_id) as workflows_used,\n  COUNT(CASE WHEN event_type = 'created' THEN 1 END) as workflows_created,\n  COUNT(CASE WHEN event_type = 'executed' THEN 1 END) as workflows_executed,\n  COUNT(CASE WHEN event_type = 'shared' THEN 1 END) as workflows_shared,\n  COUNT(CASE WHEN event_type = 'error' THEN 1 END) as workflow_errors\nFROM workflow_telemetry\nWHERE created_at >= now() - INTERVAL '30 days'\nGROUP BY DATE_TRUNC('day', created_at)\nORDER BY date DESC;
\n\n-- Enable RLS on all tables\nALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;
\nALTER TABLE feature_flag_access_logs ENABLE ROW LEVEL SECURITY;
\nALTER TABLE workflow_telemetry ENABLE ROW LEVEL SECURITY;
\nALTER TABLE user_feedback ENABLE ROW LEVEL SECURITY;
\nALTER TABLE feature_requests ENABLE ROW LEVEL SECURITY;
\nALTER TABLE feature_request_votes ENABLE ROW LEVEL SECURITY;
\n\n-- Create RLS policies\n-- Feature flags are readable by all authenticated users\nCREATE POLICY "Users can read feature flags" ON feature_flags\n  FOR SELECT USING (auth.role() = 'authenticated');
\n\n-- Only admins can modify feature flags\nCREATE POLICY "Admins can manage feature flags" ON feature_flags\n  FOR ALL USING (\n    auth.uid() IN (\n      SELECT id FROM auth.users WHERE raw_user_meta_data->>'role' = 'admin'\n    )\n  );
\n\n-- Users can only see their own access logs\nCREATE POLICY "Users can read own access logs" ON feature_flag_access_logs\n  FOR SELECT USING (auth.uid() = user_id);
\n\n-- System can insert access logs for any user\nCREATE POLICY "System can insert access logs" ON feature_flag_access_logs\n  FOR INSERT WITH CHECK (true);
\n\n-- Users can only see their own telemetry\nCREATE POLICY "Users can read own telemetry" ON workflow_telemetry\n  FOR SELECT USING (auth.uid() = user_id);
\n\n-- System can insert telemetry for any user\nCREATE POLICY "System can insert telemetry" ON workflow_telemetry\n  FOR INSERT WITH CHECK (true);
\n\n-- Users can manage their own feedback\nCREATE POLICY "Users can manage own feedback" ON user_feedback\n  FOR ALL USING (auth.uid() = user_id);
\n\n-- All users can read feature requests\nCREATE POLICY "Users can read feature requests" ON feature_requests\n  FOR SELECT USING (auth.role() = 'authenticated');
\n\n-- Users can create and update their own feature requests\nCREATE POLICY "Users can create feature requests" ON feature_requests\n  FOR INSERT WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can update own feature requests" ON feature_requests\n  FOR UPDATE USING (auth.uid() = user_id);
\n\n-- Users can vote on feature requests\nCREATE POLICY "Users can vote on features" ON feature_request_votes\n  FOR ALL USING (auth.uid() = user_id);
\n\n-- Grant necessary permissions\nGRANT ALL ON feature_flags TO authenticated;
\nGRANT ALL ON feature_flag_access_logs TO authenticated;
\nGRANT ALL ON workflow_telemetry TO authenticated;
\nGRANT ALL ON user_feedback TO authenticated;
\nGRANT ALL ON feature_requests TO authenticated;
\nGRANT ALL ON feature_request_votes TO authenticated;
\nGRANT SELECT ON feature_usage_summary TO authenticated;
\nGRANT SELECT ON workflow_usage_summary TO authenticated;
\nGRANT EXECUTE ON FUNCTION track_workflow_event TO authenticated;
\n;
