\n-- The bucket already exists, so let's just create the storage policies\n-- Create storage policies for candidate documents bucket\nCREATE POLICY "Users can upload documents for their candidates"\nON storage.objects FOR INSERT\nWITH CHECK (\n  bucket_id = 'candidate-documents' AND\n  auth.uid() IS NOT NULL\n);
\n\nCREATE POLICY "Users can view documents for their candidates"\nON storage.objects FOR SELECT\nUSING (\n  bucket_id = 'candidate-documents' AND\n  auth.uid() IS NOT NULL\n);
\n\nCREATE POLICY "Users can delete documents for their candidates"\nON storage.objects FOR DELETE\nUSING (\n  bucket_id = 'candidate-documents' AND\n  auth.uid() IS NOT NULL\n);
\n;
