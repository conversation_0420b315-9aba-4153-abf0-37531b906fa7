\n-- Check if there are any tables that might still reference candidates\nDO $$\nDECLARE\n    table_info RECORD;
\nBEGIN\n    RAISE NOTICE 'Checking for tables with candidate references...';
\n    \n    -- Check candidate_documents table\n    IF EXISTS (SELECT FROM pg_tables WHERE tablename = 'candidate_documents') THEN\n        EXECUTE 'SELECT COUNT(*) FROM candidate_documents' INTO table_info;
\n        RAISE NOTICE 'candidate_documents table exists with % rows', table_info;
\n    END IF;
\n    \n    -- Check candidate_timeline table\n    IF EXISTS (SELECT FROM pg_tables WHERE tablename = 'candidate_timeline') THEN\n        EXECUTE 'SELECT COUNT(*) FROM candidate_timeline' INTO table_info;
\n        RAISE NOTICE 'candidate_timeline table exists with % rows', table_info;
\n    END IF;
\n    \n    -- Check candidate_interviews table  \n    IF EXISTS (SELECT FROM pg_tables WHERE tablename = 'candidate_interviews') THEN\n        EXECUTE 'SELECT COUNT(*) FROM candidate_interviews' INTO table_info;
\n        RAISE NOTICE 'candidate_interviews table exists with % rows', table_info;
\n    END IF;
\n    \n    -- Check candidate_notes table\n    IF EXISTS (SELECT FROM pg_tables WHERE tablename = 'candidate_notes') THEN\n        EXECUTE 'SELECT COUNT(*) FROM candidate_notes' INTO table_info;
\n        RAISE NOTICE 'candidate_notes table exists with % rows', table_info;
\n    END IF;
\n    \n    -- List all tables that might contain candidate data\n    FOR table_info IN \n        SELECT tablename \n        FROM pg_tables \n        WHERE schemaname = 'public' \n        AND tablename LIKE '%candidate%'\n    LOOP\n        RAISE NOTICE 'Found candidate-related table: %', table_info.tablename;
\n    END LOOP;
\n    \nEND $$;
\n;
