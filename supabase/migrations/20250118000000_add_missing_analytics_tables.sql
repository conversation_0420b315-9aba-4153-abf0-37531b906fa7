\n\n-- Create analytics_skills table\nCREATE TABLE IF NOT EXISTS public.analytics_skills (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID NOT NULL,\n  skill TEXT NOT NULL,\n  current_level INTEGER NOT NULL,\n  required_level INTEGER NOT NULL,\n  gap INTEGER NOT NULL,\n  recommendation TEXT,\n  success_impact INTEGER DEFAULT 0,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  CONSTRAINT analytics_skills_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE\n);
\n\n-- Create analytics_salary table\nCREATE TABLE IF NOT EXISTS public.analytics_salary (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  user_id UUID NOT NULL,\n  role TEXT NOT NULL,\n  market_salary NUMERIC NOT NULL,\n  recommended_salary NUMERIC NOT NULL,\n  position TEXT,\n  current_salary NUMERIC,\n  market_rate NUMERIC,\n  confidence INTEGER DEFAULT 0,\n  location TEXT,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  CONSTRAINT analytics_salary_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE\n);
\n\n-- Create indexes\nCREATE INDEX IF NOT EXISTS idx_analytics_skills_user_id ON public.analytics_skills USING btree (user_id);
\nCREATE INDEX IF NOT EXISTS idx_analytics_salary_user_id ON public.analytics_salary USING btree (user_id);
\n\n-- Enable RLS\nALTER TABLE public.analytics_skills ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.analytics_salary ENABLE ROW LEVEL SECURITY;
\n\n-- Drop existing policies if they exist and create new ones\nDROP POLICY IF EXISTS "Users can manage their own analytics_skills" ON public.analytics_skills;
\nDROP POLICY IF EXISTS "Users can manage their own analytics_salary" ON public.analytics_salary;
\n\n-- Create RLS policies\nCREATE POLICY "Users can manage their own analytics_skills"\n  ON public.analytics_skills\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can manage their own analytics_salary"\n  ON public.analytics_salary\n  FOR ALL\n  USING (auth.uid() = user_id)\n  WITH CHECK (auth.uid() = user_id);
\n;
