\n\n-- Create storage policies for avatars bucket\nDO $$ \nBEGIN\n  -- This is a placeholder since we can't directly create storage buckets in SQL\n  -- The bucket should be created through the Supabase dashboard or API\n  \n  -- Create storage policies for avatars\n  BEGIN\n    CREATE POLICY "Users can upload their own avatars"\n      ON storage.objects\n      FOR INSERT\n      TO authenticated\n      WITH CHECK (bucket_id = 'avatars' AND (storage.foldername(name))[1] = auth.uid()::text);
\n  EXCEPTION\n    WHEN duplicate_object THEN\n      NULL;
\n  END;
\n  \n  BEGIN\n    CREATE POLICY "Users can view avatars"\n      ON storage.objects\n      FOR SELECT\n      TO authenticated\n      USING (bucket_id = 'avatars');
\n  EXCEPTION\n    WHEN duplicate_object THEN\n      NULL;
\n  END;
\n  \n  BEGIN\n    CREATE POLICY "Users can update their own avatars"\n      ON storage.objects\n      FOR UPDATE\n      TO authenticated\n      USING (bucket_id = 'avatars' AND (storage.foldername(name))[1] = auth.uid()::text);
\n  EXCEPTION\n    WHEN duplicate_object THEN\n      NULL;
\n  END;
\n  \n  BEGIN\n    CREATE POLICY "Users can delete their own avatars"\n      ON storage.objects\n      FOR DELETE\n      TO authenticated\n      USING (bucket_id = 'avatars' AND (storage.foldername(name))[1] = auth.uid()::text);
\n  EXCEPTION\n    WHEN duplicate_object THEN\n      NULL;
\n  END;
\nEND $$;
;
