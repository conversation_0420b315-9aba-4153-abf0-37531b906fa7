\n-- Create candidate timeline table\nCREATE TABLE IF NOT EXISTS public.candidate_timeline (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  candidate_id UUID NOT NULL,\n  user_id UUID NOT NULL,\n  event_type TEXT NOT NULL,\n  title TEXT NOT NULL,\n  description TEXT,\n  event_date TIMESTAMP WITH TIME ZONE NOT NULL,\n  status TEXT NOT NULL DEFAULT 'completed',\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create candidate notes table\nCREATE TABLE IF NOT EXISTS public.candidate_notes (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  candidate_id UUID NOT NULL,\n  user_id UUID NOT NULL,\n  content TEXT NOT NULL,\n  author_name TEXT NOT NULL,\n  is_important BOOLEAN NOT NULL DEFAULT false,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Create candidate interviews table\nCREATE TABLE IF NOT EXISTS public.candidate_interviews (\n  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n  candidate_id UUID NOT NULL,\n  user_id UUID NOT NULL,\n  interview_type TEXT NOT NULL,\n  scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,\n  duration_minutes INTEGER NOT NULL DEFAULT 60,\n  status TEXT NOT NULL DEFAULT 'scheduled',\n  interviewers TEXT[] DEFAULT '{}',\n  location TEXT,\n  meeting_platform TEXT,\n  meeting_link TEXT,\n  feedback TEXT,\n  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n);
\n\n-- Enable RLS on the new tables only\nALTER TABLE public.candidate_timeline ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.candidate_notes ENABLE ROW LEVEL SECURITY;
\nALTER TABLE public.candidate_interviews ENABLE ROW LEVEL SECURITY;
\n\n-- Create RLS policies for the new tables only\nCREATE POLICY "Users can manage their own candidate timeline"\n  ON public.candidate_timeline\n  FOR ALL\n  USING (auth.uid()::text = user_id::text)\n  WITH CHECK (auth.uid()::text = user_id::text);
\n\nCREATE POLICY "Users can manage their own candidate notes"\n  ON public.candidate_notes\n  FOR ALL\n  USING (auth.uid()::text = user_id::text)\n  WITH CHECK (auth.uid()::text = user_id::text);
\n\nCREATE POLICY "Users can manage their own candidate interviews"\n  ON public.candidate_interviews\n  FOR ALL\n  USING (auth.uid()::text = user_id::text)\n  WITH CHECK (auth.uid()::text = user_id::text);
\n;
