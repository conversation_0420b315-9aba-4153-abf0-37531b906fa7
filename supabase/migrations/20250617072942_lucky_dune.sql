\n\n-- Create storage policies for candidate documents bucket\nDO $$ \nBEGIN\n  -- Check if the bucket exists in storage.buckets\n  IF EXISTS (\n    SELECT 1 FROM storage.buckets \n    WHERE id = 'candidate-documents'\n  ) THEN\n    -- Drop existing policies if they exist\n    BEGIN\n      DROP POLICY IF EXISTS "Users can upload candidate documents" ON storage.objects;
\n      DROP POLICY IF EXISTS "Users can view candidate documents" ON storage.objects;
\n      DROP POLICY IF EXISTS "Users can delete candidate documents" ON storage.objects;
\n    EXCEPTION\n      WHEN OTHERS THEN\n        -- Ignore errors if policies don't exist\n        NULL;
\n    END;
\n\n    -- Create storage policies for candidate documents\n    CREATE POLICY "Users can upload candidate documents"\n      ON storage.objects\n      FOR INSERT\n      TO authenticated\n      WITH CHECK (bucket_id = 'candidate-documents');
\n\n    CREATE POLICY "Users can view candidate documents"\n      ON storage.objects\n      FOR SELECT\n      TO authenticated\n      USING (bucket_id = 'candidate-documents');
\n\n    CREATE POLICY "Users can delete candidate documents"\n      ON storage.objects\n      FOR DELETE\n      TO authenticated\n      USING (bucket_id = 'candidate-documents');
\n  END IF;
\nEND $$;
;
