\n\n-- Function to update next_run time based on cron schedule\nCREATE OR REPLACE FUNCTION update_workflow_next_run()\nRETURNS TRIGGER AS $$\nDECLARE\n  next_run_time TIMESTAMP WITH TIME ZONE;
\nBEGIN\n  -- Simple implementation for common cron patterns\n  -- In a production environment, this would use a more robust cron parser\n  \n  -- Daily at specific time (e.g., "0 9 * * *" for 9 AM daily)\n  IF NEW.cron_schedule ~ '^0 [0-9]{1,2} \\* \\* \\*$' THEN\n    -- Extract hour from cron\n    DECLARE hour INTEGER;
\n    BEGIN\n      hour := (regexp_matches(NEW.cron_schedule, '^0 ([0-9]{1,2}) \\* \\* \\*$'))[1]::INTEGER;
\n      next_run_time := date_trunc('day', now()) + (hour * interval '1 hour');
\n      \n      -- If that time has already passed today, schedule for tomorrow\n      IF next_run_time <= now() THEN\n        next_run_time := next_run_time + interval '1 day';
\n      END IF;
\n    END;
\n  \n  -- Weekly on specific day (e.g., "0 9 * * 1" for Monday at 9 AM)\n  ELSIF NEW.cron_schedule ~ '^0 [0-9]{1,2} \\* \\* [0-6]$' THEN\n    DECLARE \n      hour INTEGER;
\n      day_of_week INTEGER;
\n      days_to_add INTEGER;
\n      current_day INTEGER;
\n    BEGIN\n      hour := (regexp_matches(NEW.cron_schedule, '^0 ([0-9]{1,2}) \\* \\* [0-6]$'))[1]::INTEGER;
\n      day_of_week := (regexp_matches(NEW.cron_schedule, '^0 [0-9]{1,2} \\* \\* ([0-6])$'))[1]::INTEGER;
\n      \n      -- Get current day of week (0-6, Sunday is 0)\n      current_day := EXTRACT(DOW FROM now());
\n      \n      -- Calculate days to add\n      IF current_day <= day_of_week THEN\n        days_to_add := day_of_week - current_day;
\n      ELSE\n        days_to_add := 7 - (current_day - day_of_week);
\n      END IF;
\n      \n      next_run_time := date_trunc('day', now()) + (hour * interval '1 hour') + (days_to_add * interval '1 day');
\n      \n      -- If that time has already passed today and it's the target day, schedule for next week\n      IF days_to_add = 0 AND (date_trunc('day', now()) + (hour * interval '1 hour')) <= now() THEN\n        next_run_time := next_run_time + interval '7 days';
\n      END IF;
\n    END;
\n  \n  -- Monthly on specific day (e.g., "0 9 1 * *" for 1st of month at 9 AM)\n  ELSIF NEW.cron_schedule ~ '^0 [0-9]{1,2} [0-9]{1,2} \\* \\*$' THEN\n    DECLARE \n      hour INTEGER;
\n      day_of_month INTEGER;
\n    BEGIN\n      hour := (regexp_matches(NEW.cron_schedule, '^0 ([0-9]{1,2}) [0-9]{1,2} \\* \\*$'))[1]::INTEGER;
\n      day_of_month := (regexp_matches(NEW.cron_schedule, '^0 [0-9]{1,2} ([0-9]{1,2}) \\* \\*$'))[1]::INTEGER;
\n      \n      -- Set to the specified day of current month\n      next_run_time := date_trunc('month', now()) + ((day_of_month - 1) * interval '1 day') + (hour * interval '1 hour');
\n      \n      -- If that time has already passed this month, schedule for next month\n      IF next_run_time <= now() THEN\n        next_run_time := date_trunc('month', now() + interval '1 month') + ((day_of_month - 1) * interval '1 day') + (hour * interval '1 hour');
\n      END IF;
\n    END;
\n  \n  -- Hourly (e.g., "0 * * * *")\n  ELSIF NEW.cron_schedule = '0 * * * *' THEN\n    next_run_time := date_trunc('hour', now()) + interval '1 hour';
\n  \n  -- Default: schedule for tomorrow at 9 AM\n  ELSE\n    next_run_time := date_trunc('day', now() + interval '1 day') + interval '9 hours';
\n  END IF;
\n  \n  -- Update the next_run field\n  NEW.next_run := next_run_time;
\n  \n  RETURN NEW;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Create trigger to update next_run when a schedule is created or modified\nDROP TRIGGER IF EXISTS trigger_update_workflow_next_run ON workflow_schedules;
\nCREATE TRIGGER trigger_update_workflow_next_run\n  BEFORE INSERT OR UPDATE ON workflow_schedules\n  FOR EACH ROW\n  EXECUTE FUNCTION update_workflow_next_run();
\n\n-- Function to execute due workflow schedules\n-- This would be called by a cron job or Edge Function\nCREATE OR REPLACE FUNCTION execute_due_workflow_schedules()\nRETURNS SETOF workflow_executions AS $$\nDECLARE\n  schedule RECORD;
\n  workflow RECORD;
\n  execution_id UUID;
\nBEGIN\n  -- Find all active schedules that are due to run\n  FOR schedule IN\n    SELECT * FROM workflow_schedules\n    WHERE is_active = true\n      AND next_run <= now()\n  LOOP\n    -- Get the workflow configuration\n    SELECT * INTO workflow\n    FROM workflow_configurations\n    WHERE id = schedule.workflow_id\n      AND is_active = true;
\n    \n    -- If workflow exists and is active, create an execution record\n    IF FOUND THEN\n      -- Insert execution record\n      INSERT INTO workflow_executions (\n        workflow_id,\n        status,\n        started_at,\n        execution_log,\n        created_by\n      ) VALUES (\n        workflow.id,\n        'in_progress',\n        now(),\n        jsonb_build_object(\n          'context', schedule.context,\n          'trigger', 'schedule',\n          'schedule_id', schedule.id,\n          'started', now()\n        ),\n        schedule.created_by\n      )\n      RETURNING id INTO execution_id;
\n      \n      -- Update the schedule's last_run time\n      UPDATE workflow_schedules\n      SET last_run = now()\n      WHERE id = schedule.id;
\n      \n      -- Return the execution record\n      RETURN QUERY SELECT * FROM workflow_executions WHERE id = execution_id;
\n    END IF;
\n  END LOOP;
\n  \n  RETURN;
\nEND;
\n$$ LANGUAGE plpgsql;
;
