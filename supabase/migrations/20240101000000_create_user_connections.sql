-- Create user_connections table for storing OAuth tokens and integration settings
CREATE TABLE IF NOT EXISTS public.user_connections (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  provider TEXT NOT NULL, -- 'gmail', 'outlook', 'google_calendar', 'slack', 'linkedin'
  provider_account_id TEXT, -- External account ID from provider
  provider_account_email TEXT, -- Email associated with the provider account
  access_token TEXT, -- Encrypted access token
  refresh_token TEXT, -- Encrypted refresh token
  token_expires_at TIMESTAMPTZ,
  scopes TEXT[], -- Array of granted OAuth scopes
  settings JSONB DEFAULT '{}', -- Provider-specific settings
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, provider, provider_account_id)
);
-- Create indexes for performance
CREATE INDEX idx_user_connections_user_id ON public.user_connections(user_id);
CREATE INDEX idx_user_connections_provider ON public.user_connections(provider);
CREATE INDEX idx_user_connections_active ON public.user_connections(is_active);
-- Enable RLS
ALTER TABLE public.user_connections ENABLE ROW LEVEL SECURITY;
-- RLS policies
CREATE POLICY "Users can view own connections" ON public.user_connections
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own connections" ON public.user_connections
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own connections" ON public.user_connections
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own connections" ON public.user_connections
  FOR DELETE USING (auth.uid() = user_id);
-- Create updated_at trigger
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
CREATE TRIGGER handle_user_connections_updated_at
  BEFORE UPDATE ON public.user_connections
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();
-- Create usage_quotas table for tracking API usage
CREATE TABLE IF NOT EXISTS public.usage_quotas (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  provider TEXT NOT NULL,
  quota_type TEXT NOT NULL, -- 'daily', 'monthly', 'per_minute'
  quota_limit INTEGER NOT NULL,
  quota_used INTEGER DEFAULT 0,
  reset_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, provider, quota_type)
);
-- Create indexes for usage_quotas
CREATE INDEX idx_usage_quotas_user_provider ON public.usage_quotas(user_id, provider);
CREATE INDEX idx_usage_quotas_reset_at ON public.usage_quotas(reset_at);
-- Enable RLS for usage_quotas
ALTER TABLE public.usage_quotas ENABLE ROW LEVEL SECURITY;
-- RLS policies for usage_quotas
CREATE POLICY "Users can view own quotas" ON public.usage_quotas
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Service can manage quotas" ON public.usage_quotas
  FOR ALL USING (true);
-- Create trigger for usage_quotas updated_at
CREATE TRIGGER handle_usage_quotas_updated_at
  BEFORE UPDATE ON public.usage_quotas
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();
