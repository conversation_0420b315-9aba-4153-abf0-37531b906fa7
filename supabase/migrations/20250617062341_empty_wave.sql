\n\n-- Create messages table if it doesn't exist\nDO $$ \nBEGIN\n  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'messages') THEN\n    CREATE TABLE public.messages (\n      id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n      sender_name TEXT NOT NULL,\n      sender_email TEXT NOT NULL,\n      sender_role TEXT,\n      sender_avatar TEXT,\n      content TEXT NOT NULL,\n      status TEXT NOT NULL DEFAULT 'unread', -- 'read', 'unread', 'archived'\n      is_starred BOOLEAN NOT NULL DEFAULT false,\n      follow_up BOOLEAN NOT NULL DEFAULT false,\n      reminder BOOLEAN NOT NULL DEFAULT false,\n      user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n    );
\n    \n    -- Enable RLS on messages table\n    ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
\n    \n    -- Create RLS policies for messages\n    CREATE POLICY "Users can manage their own messages" \n      ON public.messages \n      FOR ALL \n      USING (auth.uid() = user_id)\n      WITH CHECK (auth.uid() = user_id);
\n      \n    -- Create indexes for better performance on messages\n    CREATE INDEX idx_messages_user_id ON public.messages(user_id);
\n    CREATE INDEX idx_messages_status ON public.messages(status);
\n  END IF;
\nEND $$;
\n\n-- Create message_templates table if it doesn't exist\nDO $$ \nBEGIN\n  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'message_templates') THEN\n    CREATE TABLE public.message_templates (\n      id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n      name TEXT NOT NULL,\n      subject TEXT NOT NULL,\n      content TEXT NOT NULL,\n      template_category TEXT NOT NULL DEFAULT 'general', -- 'interview', 'rejection', 'follow-up', etc.\n      user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n    );
\n    \n    -- Enable RLS on message_templates table\n    ALTER TABLE public.message_templates ENABLE ROW LEVEL SECURITY;
\n    \n    -- Create RLS policies for message templates\n    CREATE POLICY "Users can manage their own message templates" \n      ON public.message_templates \n      FOR ALL \n      USING (auth.uid() = user_id)\n      WITH CHECK (auth.uid() = user_id);
\n      \n    -- Create indexes for better performance on message templates\n    CREATE INDEX idx_message_templates_user_id ON public.message_templates(user_id);
\n  END IF;
\nEND $$;
\n\n-- Create events table if it doesn't exist\nDO $$ \nBEGIN\n  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'events') THEN\n    CREATE TABLE public.events (\n      id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n      title TEXT NOT NULL,\n      description TEXT,\n      start_time TIMESTAMP WITH TIME ZONE NOT NULL,\n      end_time TIMESTAMP WITH TIME ZONE NOT NULL,\n      location TEXT,\n      meeting_link TEXT,\n      event_type TEXT NOT NULL DEFAULT 'meeting', -- 'meeting', 'interview', 'call', 'presentation', 'other'\n      priority TEXT NOT NULL DEFAULT 'medium', -- 'low', 'medium', 'high'\n      category TEXT NOT NULL DEFAULT 'general', -- 'general', 'recruitment', 'client', 'internal'\n      user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n    );
\n    \n    -- Enable RLS on events table\n    ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;
\n    \n    -- Create RLS policies for events\n    CREATE POLICY "Users can manage their own events" \n      ON public.events \n      FOR ALL \n      USING (auth.uid() = user_id)\n      WITH CHECK (auth.uid() = user_id);
\n      \n    -- Create indexes for better performance on events\n    CREATE INDEX idx_events_user_id ON public.events(user_id);
\n    CREATE INDEX idx_events_start_time ON public.events(start_time);
\n  END IF;
\nEND $$;
\n\n-- Create workflow_configurations table if it doesn't exist\nDO $$ \nBEGIN\n  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'workflow_configurations') THEN\n    CREATE TABLE public.workflow_configurations (\n      id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,\n      name TEXT NOT NULL,\n      description TEXT,\n      config JSONB NOT NULL,\n      is_active BOOLEAN NOT NULL DEFAULT true,\n      created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,\n      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),\n      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()\n    );
\n    \n    -- Enable RLS on workflow_configurations table\n    ALTER TABLE public.workflow_configurations ENABLE ROW LEVEL SECURITY;
\n    \n    -- Create RLS policies for workflow_configurations\n    CREATE POLICY "Users can view their own workflows" \n      ON public.workflow_configurations \n      FOR SELECT \n      USING (auth.uid() = created_by);
\n\n    CREATE POLICY "Users can create workflows" \n      ON public.workflow_configurations \n      FOR INSERT \n      WITH CHECK (auth.uid() = created_by);
\n\n    CREATE POLICY "Users can update their own workflows" \n      ON public.workflow_configurations \n      FOR UPDATE \n      USING (auth.uid() = created_by);
\n\n    CREATE POLICY "Users can delete their own workflows" \n      ON public.workflow_configurations \n      FOR DELETE \n      USING (auth.uid() = created_by);
\n      \n    -- Create index for better performance\n    CREATE INDEX idx_workflow_configurations_created_by ON public.workflow_configurations(created_by);
\n  END IF;
\nEND $$;
;
