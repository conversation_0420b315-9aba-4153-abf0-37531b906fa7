\n\n-- Insert sample data for analytics_metrics\nDO $$ \nBEGIN\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'analytics_metrics') THEN\n    -- Only insert if no data exists for the current user\n    IF NOT EXISTS (\n      SELECT 1 FROM public.analytics_metrics \n      WHERE user_id = auth.uid() \n      LIMIT 1\n    ) AND EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid()) THEN\n      INSERT INTO public.analytics_metrics (user_id, metric_name, metric_value, change_percentage, period)\n      VALUES \n        (auth.uid(), 'total_applications', 2543, 12, 'month'),\n        (auth.uid(), 'active_jobs', 48, 4, 'week'),\n        (auth.uid(), 'time_to_hire', 23, -3, 'month');
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Insert sample data for analytics_applications\nDO $$ \nBEGIN\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'analytics_applications') THEN\n    -- Only insert if no data exists for the current user\n    IF NOT EXISTS (\n      SELECT 1 FROM public.analytics_applications \n      WHERE user_id = auth.uid() \n      LIMIT 1\n    ) AND EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid()) THEN\n      INSERT INTO public.analytics_applications (user_id, period, applications, interviews)\n      VALUES \n        (auth.uid(), 'Jan', 400, 240),\n        (auth.uid(), 'Feb', 300, 180),\n        (auth.uid(), 'Mar', 600, 320),\n        (auth.uid(), 'Apr', 800, 400),\n        (auth.uid(), 'May', 500, 280),\n        (auth.uid(), 'Jun', 700, 350);
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Insert sample data for analytics_skills\nDO $$ \nBEGIN\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'analytics_skills') THEN\n    -- Only insert if no data exists for the current user\n    IF NOT EXISTS (\n      SELECT 1 FROM public.analytics_skills \n      WHERE user_id = auth.uid() \n      LIMIT 1\n    ) AND EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid()) THEN\n      INSERT INTO public.analytics_skills (user_id, skill, current_level, required_level, gap, recommendation, success_impact)\n      VALUES \n        (auth.uid(), 'Technical', 75, 85, 10, 'Focus on advanced React patterns and TypeScript', 85),\n        (auth.uid(), 'Leadership', 65, 80, 15, 'Consider leadership training or mentorship programs', 70),\n        (auth.uid(), 'Communication', 85, 90, 5, 'Minor improvements needed in technical documentation', 90),\n        (auth.uid(), 'Problem Solving', 70, 75, 5, 'Practice system design and architecture', 80);
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Insert sample data for analytics_sources\nDO $$ \nBEGIN\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'analytics_sources') THEN\n    -- Only insert if no data exists for the current user\n    IF NOT EXISTS (\n      SELECT 1 FROM public.analytics_sources \n      WHERE user_id = auth.uid() \n      LIMIT 1\n    ) AND EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid()) THEN\n      INSERT INTO public.analytics_sources (user_id, source, effectiveness, hires)\n      VALUES \n        (auth.uid(), 'LinkedIn', 85, 28),\n        (auth.uid(), 'Internal Referrals', 92, 35),\n        (auth.uid(), 'Job Boards', 65, 18),\n        (auth.uid(), 'Company Website', 72, 22);
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Insert sample data for analytics_diversity\nDO $$ \nBEGIN\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'analytics_diversity') THEN\n    -- Only insert if no data exists for the current user\n    IF NOT EXISTS (\n      SELECT 1 FROM public.analytics_diversity \n      WHERE user_id = auth.uid() \n      LIMIT 1\n    ) AND EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid()) THEN\n      INSERT INTO public.analytics_diversity (user_id, category, value, total)\n      VALUES \n        (auth.uid(), 'Gender', 42, 100),\n        (auth.uid(), 'Ethnicity', 38, 100),\n        (auth.uid(), 'Age Groups', 45, 100),\n        (auth.uid(), 'Veterans', 15, 100);
\n    END IF;
\n  END IF;
\nEND $$;
\n\n-- Insert sample data for analytics_salary\nDO $$ \nBEGIN\n  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'analytics_salary') THEN\n    -- Only insert if no data exists for the current user\n    IF NOT EXISTS (\n      SELECT 1 FROM public.analytics_salary \n      WHERE user_id = auth.uid() \n      LIMIT 1\n    ) AND EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid()) THEN\n      INSERT INTO public.analytics_salary (user_id, role, market_salary, recommended_salary)\n      VALUES \n        (auth.uid(), 'Software Engineer', 120000, 132000),\n        (auth.uid(), 'Product Manager', 130000, 143000),\n        (auth.uid(), 'Data Scientist', 115000, 126500),\n        (auth.uid(), 'UX Designer', 95000, 104500);
\n    END IF;
\n  END IF;
\nEND $$;
;
