\n-- Add screening column to candidates table\nALTER TABLE public.candidates \nADD COLUMN screening JSONB DEFAULT NULL;
\n\n-- Create index for screening data queries\nCREATE INDEX IF NOT EXISTS idx_candidates_screening_status \nON public.candidates USING gin ((screening->'status')) \nWHERE screening IS NOT NULL;
\n\n-- Add comment for documentation\nCOMMENT ON COLUMN public.candidates.screening IS 'JSONB column storing candidate screening data including notes, evaluations, requirements, and questions';
\n;
