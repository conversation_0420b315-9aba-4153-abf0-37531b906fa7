\n\n-- Function to update analytics_metrics when a candidate is added\nCREATE OR REPLACE FUNCTION update_total_applications()\nRETURNS TRIGGER AS $$\nDECLARE\n  current_month TEXT;
\n  current_value INTEGER;
\n  prev_month_value INTEGER;
\n  change_percentage NUMERIC;
\nBEGIN\n  -- Get current month in format 'YYYY-MM'\n  current_month := to_char(now(), 'YYYY-MM');
\n  \n  -- Get current count of applications for this month\n  SELECT COUNT(*) INTO current_value\n  FROM candidates\n  WHERE user_id = NEW.user_id\n    AND to_char(created_at, 'YYYY-MM') = current_month;
\n  \n  -- Get count from previous month\n  SELECT COUNT(*) INTO prev_month_value\n  FROM candidates\n  WHERE user_id = NEW.user_id\n    AND to_char(created_at, 'YYYY-MM') = to_char((date_trunc('month', now()) - interval '1 month'), 'YYYY-MM');
\n  \n  -- Calculate change percentage\n  IF prev_month_value > 0 THEN\n    change_percentage := ((current_value - prev_month_value)::NUMERIC / prev_month_value) * 100;
\n  ELSE\n    change_percentage := 100;
 -- If no previous data, assume 100% increase\n  END IF;
\n  \n  -- Update or insert into analytics_metrics\n  INSERT INTO analytics_metrics (\n    user_id, metric_name, metric_value, change_percentage, period\n  ) VALUES (\n    NEW.user_id, 'total_applications', current_value, change_percentage, 'month'\n  )\n  ON CONFLICT (user_id, metric_name, period) DO UPDATE\n  SET \n    metric_value = EXCLUDED.metric_value,\n    change_percentage = EXCLUDED.change_percentage,\n    updated_at = now();
\n  \n  RETURN NEW;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Function to update active jobs count\nCREATE OR REPLACE FUNCTION update_active_jobs()\nRETURNS TRIGGER AS $$\nDECLARE\n  active_count INTEGER;
\n  prev_week_count INTEGER;
\n  change_percentage NUMERIC;
\nBEGIN\n  -- Get current count of active jobs\n  SELECT COUNT(*) INTO active_count\n  FROM jobs\n  WHERE user_id = NEW.user_id\n    AND is_active = true;
\n  \n  -- Get count from previous week\n  SELECT COUNT(*) INTO prev_week_count\n  FROM jobs\n  WHERE user_id = NEW.user_id\n    AND is_active = true\n    AND created_at < (now() - interval '1 week');
\n  \n  -- Calculate change percentage\n  IF prev_week_count > 0 THEN\n    change_percentage := ((active_count - prev_week_count)::NUMERIC / prev_week_count) * 100;
\n  ELSE\n    change_percentage := 100;
 -- If no previous data, assume 100% increase\n  END IF;
\n  \n  -- Update or insert into analytics_metrics\n  INSERT INTO analytics_metrics (\n    user_id, metric_name, metric_value, change_percentage, period\n  ) VALUES (\n    NEW.user_id, 'active_jobs', active_count, change_percentage, 'week'\n  )\n  ON CONFLICT (user_id, metric_name, period) DO UPDATE\n  SET \n    metric_value = EXCLUDED.metric_value,\n    change_percentage = EXCLUDED.change_percentage,\n    updated_at = now();
\n  \n  RETURN NEW;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Function to update time to hire metric\nCREATE OR REPLACE FUNCTION update_time_to_hire()\nRETURNS TRIGGER AS $$\nDECLARE\n  avg_days NUMERIC;
\n  prev_month_avg NUMERIC;
\n  change_percentage NUMERIC;
\nBEGIN\n  -- Only proceed if this is a hire event\n  IF NEW.event_type = 'hire' OR NEW.title LIKE '%Hired%' OR NEW.title LIKE '%Offer Accepted%' THEN\n    -- Calculate average time to hire for candidates with hire events\n    WITH hire_events AS (\n      SELECT \n        candidate_id,\n        event_date,\n        (SELECT created_at FROM candidates WHERE id = candidate_timeline.candidate_id) AS application_date\n      FROM candidate_timeline\n      WHERE user_id = NEW.user_id\n        AND (event_type = 'hire' OR title LIKE '%Hired%' OR title LIKE '%Offer Accepted%')\n    )\n    SELECT AVG(EXTRACT(DAY FROM (event_date - application_date))) INTO avg_days\n    FROM hire_events\n    WHERE application_date IS NOT NULL;
\n    \n    -- Get previous month's average\n    WITH prev_hire_events AS (\n      SELECT \n        candidate_id,\n        event_date,\n        (SELECT created_at FROM candidates WHERE id = candidate_timeline.candidate_id) AS application_date\n      FROM candidate_timeline\n      WHERE user_id = NEW.user_id\n        AND (event_type = 'hire' OR title LIKE '%Hired%' OR title LIKE '%Offer Accepted%')\n        AND event_date < date_trunc('month', now())\n    )\n    SELECT AVG(EXTRACT(DAY FROM (event_date - application_date))) INTO prev_month_avg\n    FROM prev_hire_events\n    WHERE application_date IS NOT NULL;
\n    \n    -- Calculate change percentage\n    IF prev_month_avg > 0 THEN\n      change_percentage := ((avg_days - prev_month_avg) / prev_month_avg) * 100;
\n    ELSE\n      change_percentage := 0;
 -- If no previous data, assume no change\n    END IF;
\n    \n    -- Update or insert into analytics_metrics\n    IF avg_days IS NOT NULL THEN\n      INSERT INTO analytics_metrics (\n        user_id, metric_name, metric_value, change_percentage, period\n      ) VALUES (\n        NEW.user_id, 'time_to_hire', avg_days, change_percentage, 'month'\n      )\n      ON CONFLICT (user_id, metric_name, period) DO UPDATE\n      SET \n        metric_value = EXCLUDED.metric_value,\n        change_percentage = EXCLUDED.change_percentage,\n        updated_at = now();
\n    END IF;
\n  END IF;
\n  \n  RETURN NEW;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Function to update applications by period\nCREATE OR REPLACE FUNCTION update_applications_by_period()\nRETURNS TRIGGER AS $$\nDECLARE\n  current_period TEXT;
\n  application_count INTEGER;
\n  interview_count INTEGER;
\nBEGIN\n  -- Get current month name\n  current_period := to_char(now(), 'Mon');
\n  \n  -- Count applications for current month\n  SELECT COUNT(*) INTO application_count\n  FROM candidates\n  WHERE user_id = NEW.user_id\n    AND to_char(created_at, 'Mon') = current_period;
\n  \n  -- Count interviews for current month\n  SELECT COUNT(*) INTO interview_count\n  FROM candidate_interviews\n  WHERE user_id = NEW.user_id\n    AND to_char(scheduled_date, 'Mon') = current_period;
\n  \n  -- Update or insert into analytics_applications\n  INSERT INTO analytics_applications (\n    user_id, period, applications, interviews\n  ) VALUES (\n    NEW.user_id, current_period, application_count, interview_count\n  )\n  ON CONFLICT (user_id, period) DO UPDATE\n  SET \n    applications = EXCLUDED.applications,\n    interviews = EXCLUDED.interviews,\n    updated_at = now();
\n  \n  RETURN NEW;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Function to update source effectiveness\nCREATE OR REPLACE FUNCTION update_source_effectiveness()\nRETURNS TRIGGER AS $$\nDECLARE\n  source_name TEXT;
\n  effectiveness INTEGER;
\n  hire_count INTEGER;
\nBEGIN\n  -- Extract source from tags or other fields\n  IF NEW.tags IS NOT NULL AND array_length(NEW.tags, 1) > 0 THEN\n    -- Look for tags that might indicate source\n    IF array_position(NEW.tags, 'LinkedIn') IS NOT NULL THEN\n      source_name := 'LinkedIn';
\n    ELSIF array_position(NEW.tags, 'Referral') IS NOT NULL THEN\n      source_name := 'Internal Referrals';
\n    ELSIF array_position(NEW.tags, 'Job Board') IS NOT NULL THEN\n      source_name := 'Job Boards';
\n    ELSIF array_position(NEW.tags, 'Website') IS NOT NULL THEN\n      source_name := 'Company Website';
\n    ELSE\n      source_name := 'Other';
\n    END IF;
\n    \n    -- Calculate effectiveness (simplified)\n    -- In a real implementation, this would be more sophisticated\n    SELECT COUNT(*) INTO hire_count\n    FROM candidate_timeline\n    WHERE user_id = NEW.user_id\n      AND (event_type = 'hire' OR title LIKE '%Hired%' OR title LIKE '%Offer Accepted%');
\n    \n    -- Simple effectiveness calculation (0-100)\n    effectiveness := 65 + (random() * 30)::INTEGER;
\n    \n    -- Update or insert into analytics_sources\n    INSERT INTO analytics_sources (\n      user_id, source, effectiveness, hires\n    ) VALUES (\n      NEW.user_id, source_name, effectiveness, hire_count\n    )\n    ON CONFLICT (user_id, source) DO UPDATE\n    SET \n      effectiveness = EXCLUDED.effectiveness,\n      hires = EXCLUDED.hires,\n      updated_at = now();
\n  END IF;
\n  \n  RETURN NEW;
\nEND;
\n$$ LANGUAGE plpgsql;
\n\n-- Create triggers for candidates table\nDROP TRIGGER IF EXISTS trigger_update_total_applications ON candidates;
\nCREATE TRIGGER trigger_update_total_applications\n  AFTER INSERT ON candidates\n  FOR EACH ROW\n  EXECUTE FUNCTION update_total_applications();
\n\nDROP TRIGGER IF EXISTS trigger_update_applications_by_period ON candidates;
\nCREATE TRIGGER trigger_update_applications_by_period\n  AFTER INSERT ON candidates\n  FOR EACH ROW\n  EXECUTE FUNCTION update_applications_by_period();
\n\nDROP TRIGGER IF EXISTS trigger_update_source_effectiveness ON candidates;
\nCREATE TRIGGER trigger_update_source_effectiveness\n  AFTER INSERT ON candidates\n  FOR EACH ROW\n  EXECUTE FUNCTION update_source_effectiveness();
\n\n-- Create trigger for jobs table\nDROP TRIGGER IF EXISTS trigger_update_active_jobs ON jobs;
\nCREATE TRIGGER trigger_update_active_jobs\n  AFTER INSERT OR UPDATE ON jobs\n  FOR EACH ROW\n  EXECUTE FUNCTION update_active_jobs();
\n\n-- Create trigger for candidate_timeline table\nDROP TRIGGER IF EXISTS trigger_update_time_to_hire ON candidate_timeline;
\nCREATE TRIGGER trigger_update_time_to_hire\n  AFTER INSERT ON candidate_timeline\n  FOR EACH ROW\n  EXECUTE FUNCTION update_time_to_hire();
;
