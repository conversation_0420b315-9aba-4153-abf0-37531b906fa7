\n\n-- Add missing fields to analytics_salary table\nALTER TABLE public.analytics_salary \nADD COLUMN IF NOT EXISTS experience_level TEXT,\nADD COLUMN IF NOT EXISTS location TEXT,\nADD COLUMN IF NOT EXISTS industry TEXT,\nADD COLUMN IF NOT EXISTS position TEXT,\nADD COLUMN IF NOT EXISTS current_salary NUMERIC,\nADD COLUMN IF NOT EXISTS market_rate NUMERIC,\nADD COLUMN IF NOT EXISTS confidence INTEGER DEFAULT 0;
\n\n-- Insert sample data with the new fields if no data exists for current user\nINSERT INTO public.analytics_salary (user_id, role, market_salary, recommended_salary, experience_level, location, industry)\nSELECT \n  auth.uid(),\n  'Software Engineer',\n  120000,\n  132000,\n  '3-5 years',\n  'San Francisco',\n  'Technology'\nWHERE auth.uid() IS NOT NULL \n  AND NOT EXISTS (\n    SELECT 1 FROM public.analytics_salary \n    WHERE user_id = auth.uid() \n    AND role = 'Software Engineer'\n  );
\n\nINSERT INTO public.analytics_salary (user_id, role, market_salary, recommended_salary, experience_level, location, industry)\nSELECT \n  auth.uid(),\n  'Product Manager',\n  130000,\n  143000,\n  '5-8 years',\n  'New York',\n  'Technology'\nWHERE auth.uid() IS NOT NULL \n  AND NOT EXISTS (\n    SELECT 1 FROM public.analytics_salary \n    WHERE user_id = auth.uid() \n    AND role = 'Product Manager'\n  );
\n\nINSERT INTO public.analytics_salary (user_id, role, market_salary, recommended_salary, experience_level, location, industry)\nSELECT \n  auth.uid(),\n  'Data Scientist',\n  115000,\n  126500,\n  '2-4 years',\n  'Seattle',\n  'Technology'\nWHERE auth.uid() IS NOT NULL \n  AND NOT EXISTS (\n    SELECT 1 FROM public.analytics_salary \n    WHERE user_id = auth.uid() \n    AND role = 'Data Scientist'\n  );
\n\nINSERT INTO public.analytics_salary (user_id, role, market_salary, recommended_salary, experience_level, location, industry)\nSELECT \n  auth.uid(),\n  'UX Designer',\n  95000,\n  104500,\n  '1-3 years',\n  'Austin',\n  'Technology'\nWHERE auth.uid() IS NOT NULL \n  AND NOT EXISTS (\n    SELECT 1 FROM public.analytics_salary \n    WHERE user_id = auth.uid() \n    AND role = 'UX Designer'\n  );
\n;
