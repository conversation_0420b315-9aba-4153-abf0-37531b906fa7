\n\n-- Insert sample events\nDO $$ \nBEGIN\n  -- Only insert if no data exists for the current user\n  IF NOT EXISTS (\n    SELECT 1 FROM public.events \n    WHERE user_id = auth.uid() \n    LIMIT 1\n  ) AND EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid()) THEN\n    -- Create events for the next 7 days\n    INSERT INTO public.events (\n      title, description, start_time, end_time, location, event_type, \n      priority, category, user_id\n    )\n    VALUES \n      (\n        'Technical Interview - Frontend Developer', \n        'Interview with candidate <PERSON> for the Frontend Developer position',\n        (CURRENT_DATE + INTERVAL '1 day' + INTERVAL '10 hours')::timestamp with time zone,\n        (CURRENT_DATE + INTERVAL '1 day' + INTERVAL '11 hours')::timestamp with time zone,\n        'Conference Room A', \n        'interview', \n        'high', \n        'recruitment', \n        auth.uid()\n      ),\n      (\n        'Team Standup', \n        'Daily team standup meeting',\n        (CURRENT_DATE + INTERVAL '2 days' + INTERVAL '9 hours')::timestamp with time zone,\n        (CURRENT_DATE + INTERVAL '2 days' + INTERVAL '9 hours' + INTERVAL '30 minutes')::timestamp with time zone,\n        NULL, \n        'meeting', \n        'medium', \n        'internal', \n        auth.uid()\n      ),\n      (\n        'Product Demo', \n        'Demo of new recruitment features to the client',\n        (CURRENT_DATE + INTERVAL '3 days' + INTERVAL '14 hours')::timestamp with time zone,\n        (CURRENT_DATE + INTERVAL '3 days' + INTERVAL '15 hours')::timestamp with time zone,\n        'Main Conference Room', \n        'presentation', \n        'high', \n        'client', \n        auth.uid()\n      ),\n      (\n        'Candidate Follow-up Call', \n        'Follow up with Sarah Miller about the Product Manager position',\n        (CURRENT_DATE + INTERVAL '4 days' + INTERVAL '11 hours')::timestamp with time zone,\n        (CURRENT_DATE + INTERVAL '4 days' + INTERVAL '11 hours' + INTERVAL '30 minutes')::timestamp with time zone,\n        NULL, \n        'call', \n        'medium', \n        'recruitment', \n        auth.uid()\n      ),\n      (\n        'Weekly Planning', \n        'Team planning session for upcoming recruitment activities',\n        (CURRENT_DATE + INTERVAL '5 days' + INTERVAL '13 hours')::timestamp with time zone,\n        (CURRENT_DATE + INTERVAL '5 days' + INTERVAL '14 hours')::timestamp with time zone,\n        'Meeting Room B', \n        'meeting', \n        'medium', \n        'internal', \n        auth.uid()\n      ),\n      (\n        'Technical Interview - Backend Developer', \n        'Interview with candidate Emily Chen for the Backend Developer position',\n        (CURRENT_DATE + INTERVAL '2 days' + INTERVAL '13 hours')::timestamp with time zone,\n        (CURRENT_DATE + INTERVAL '2 days' + INTERVAL '14 hours')::timestamp with time zone,\n        'Conference Room B', \n        'interview', \n        'high', \n        'recruitment', \n        auth.uid()\n      ),\n      (\n        'Hiring Committee Meeting', \n        'Review candidates for the Senior Designer role',\n        (CURRENT_DATE + INTERVAL '3 days' + INTERVAL '10 hours')::timestamp with time zone,\n        (CURRENT_DATE + INTERVAL '3 days' + INTERVAL '11 hours')::timestamp with time zone,\n        'Board Room', \n        'meeting', \n        'high', \n        'recruitment', \n        auth.uid()\n      );
\n  END IF;
\nEND $$;
;
