-- Fix function search_path security warnings by setting search_path parameter
-- This prevents function hijacking by ensuring functions use the intended schema

-- Update all functions with mutable search_path to have a fixed search_path
ALTER FUNCTION public.update_candidates_search_vector() SET search_path = 'public';
ALTER FUNCTION public.handle_updated_at() SET search_path = 'public';
ALTER FUNCTION public.track_report_access(uuid) SET search_path = 'public';
ALTER FUNCTION public.get_report_access_url(uuid) SET search_path = 'public';
ALTER FUNCTION public.schedule_report(text, uuid, text, jsonb, jsonb, text) SET search_path = 'public';
ALTER FUNCTION public.generate_report(uuid, jsonb, text) SET search_path = 'public';
ALTER FUNCTION public.cleanup_expired_reports() SET search_path = 'public';
ALTER FUNCTION public.execute_due_workflow_schedules() SET search_path = 'public';
ALTER FUNCTION public.populate_user_analytics_data(uuid) SET search_path = 'public';
ALTER FUNCTION public.trigger_populate_analytics() SET search_path = 'public';
ALTER FUNCTION public.generate_webhook_url() SET search_path = 'public';
ALTER FUNCTION public.has_ai_workflow_access(uuid) SET search_path = 'public';
ALTER FUNCTION public.check_alert_rate_limit(uuid) SET search_path = 'public';
ALTER FUNCTION public.update_feature_request_votes() SET search_path = 'public';
ALTER FUNCTION public.track_workflow_event(uuid, uuid, text, jsonb, text) SET search_path = 'public';
ALTER FUNCTION public.update_active_jobs() SET search_path = 'public';
ALTER FUNCTION public.update_jobs_search_vector() SET search_path = 'public';
ALTER FUNCTION public.update_total_applications() SET search_path = 'public';
ALTER FUNCTION public.update_time_to_hire() SET search_path = 'public';
ALTER FUNCTION public.update_applications_by_period() SET search_path = 'public';
ALTER FUNCTION public.update_source_effectiveness() SET search_path = 'public';
ALTER FUNCTION public.update_workflow_next_run() SET search_path = 'public';
ALTER FUNCTION public.update_updated_at_column() SET search_path = 'public';;
