-- Update updated_at timestamp function\nCREATE OR REPLACE FUNCTION update_updated_at_column()\nRETURNS TRIGGER AS $$\nBEGIN\n    NEW.updated_at = CURRENT_TIMESTAMP;
\n    RETURN NEW;
\nEND;
\n$$ language 'plpgsql';
\n\n-- Drop existing candidates table and recreate with proper schema\nDROP TABLE IF EXISTS candidates CASCADE;
\n\nCREATE TABLE candidates (\n  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,\n  name text NOT NULL,\n  email text NOT NULL,\n  role text NOT NULL,\n  phone text,\n  location text,\n  experience text,\n  skills jsonb,\n  linkedin_url text,\n  github_url text,\n  twitter_url text,\n  avatar text,\n  tags text[] DEFAULT '{}',\n  industry text,\n  visa_status text,\n  remote_preference text,\n  ai_summary text,\n  relationship_score integer,\n  recruiter_id text,\n  recruiter_name text,\n  recruiter_avatar text,\n  search_vector tsvector,\n  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,\n  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,\n  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL\n);
\n\n-- Create indexes for candidates\nCREATE INDEX idx_candidates_user_id ON candidates(user_id);
\nCREATE INDEX idx_candidates_name ON candidates(name);
\nCREATE INDEX idx_candidates_role ON candidates(role);
\nCREATE INDEX idx_candidates_email ON candidates(email);
\nCREATE INDEX candidates_search_vector_idx ON candidates USING gin(search_vector);
\n\n-- Enable RLS for candidates\nALTER TABLE candidates ENABLE ROW LEVEL SECURITY;
\n\n-- Create RLS policies for candidates\nCREATE POLICY "Users can view their own candidates" ON candidates\n  FOR SELECT USING (auth.uid() = user_id);
\n\nCREATE POLICY "Users can insert their own candidates" ON candidates\n  FOR INSERT WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can update their own candidates" ON candidates\n  FOR UPDATE USING (auth.uid() = user_id);
\n\nCREATE POLICY "Users can delete their own candidates" ON candidates\n  FOR DELETE USING (auth.uid() = user_id);
\n\n-- Create trigger to update updated_at timestamp for candidates\nCREATE TRIGGER update_candidates_updated_at BEFORE UPDATE ON candidates\n    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
\n\n-- Ensure jobs table exists with the exact schema you have\n-- Note: We won't drop the jobs table since it already exists with the correct schema\n-- Just ensure the RLS policies exist\n\n-- Ensure jobs table has proper structure and constraints\n-- First, let's make sure the jobs table exists with the right schema\nDO $$ \nBEGIN\n  -- Check if jobs table exists, if not create it\n  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'jobs') THEN\n    CREATE TABLE jobs (\n      id uuid DEFAULT gen_random_uuid() PRIMARY KEY,\n      title text NOT NULL,\n      department text NOT NULL,\n      location text NOT NULL,\n      job_type text NOT NULL,\n      salary_range text,\n      experience_required text,\n      description text NOT NULL,\n      requirements text[] DEFAULT '{}',\n      benefits text[] DEFAULT '{}',\n      is_urgent boolean DEFAULT false,\n      is_active boolean DEFAULT true,\n      applicant_count integer DEFAULT 0,\n      user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,\n      created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,\n      updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL\n    );
\n    \n    -- Create indexes\n    CREATE INDEX idx_jobs_user_id ON jobs(user_id);
\n    CREATE INDEX idx_jobs_title ON jobs(title);
\n    CREATE INDEX idx_jobs_department ON jobs(department);
\n    CREATE INDEX idx_jobs_location ON jobs(location);
\n    CREATE INDEX idx_jobs_is_active ON jobs(is_active);
\n  END IF;
\nEND $$;
\n\n-- Drop any problematic triggers that might be causing ON CONFLICT issues\nDROP TRIGGER IF EXISTS update_jobs_updated_at ON jobs;
\nDROP TRIGGER IF EXISTS jobs_applicant_count_trigger ON jobs;
\n\n-- Enable RLS for jobs (if not already enabled)\nALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
\n\n-- Drop existing policies if they exist and recreate them\nDROP POLICY IF EXISTS "Users can view their own jobs" ON jobs;
\nDROP POLICY IF EXISTS "Users can insert their own jobs" ON jobs;
\nDROP POLICY IF EXISTS "Users can update their own jobs" ON jobs;
\nDROP POLICY IF EXISTS "Users can delete their own jobs" ON jobs;
\n\n-- Create RLS policies for jobs\nCREATE POLICY "Users can view their own jobs" ON jobs\n  FOR SELECT USING (auth.uid() = user_id);
\n\nCREATE POLICY "Users can insert their own jobs" ON jobs\n  FOR INSERT WITH CHECK (auth.uid() = user_id);
\n\nCREATE POLICY "Users can update their own jobs" ON jobs\n  FOR UPDATE USING (auth.uid() = user_id);
\n\nCREATE POLICY "Users can delete their own jobs" ON jobs\n  FOR DELETE USING (auth.uid() = user_id);
\n\n-- Create a simple trigger for updated_at without ON CONFLICT\nCREATE TRIGGER update_jobs_updated_at BEFORE UPDATE ON jobs\n    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
;
