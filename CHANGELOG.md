# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- GitHub Actions CI/CD pipeline for automated testing and deployment
- Automated version bumping and changelog generation workflow
- Database seed script with sample workflows and data
- Comprehensive CI/CD documentation
- Support for Supabase Edge Functions deployment
- Database migration automation via `supabase db push`
- E2E testing with Cypress integration
- Code coverage reporting with Codecov integration

### Changed
- Updated @sentry/react to v9.36.0 for React 19 compatibility
- Enhanced package.json with database management scripts

### Fixed
- Resolved React 19 dependency conflicts

## [0.0.0] - 2025-07-10

### Added
- Initial project setup with Vite, React, TypeScript, and Tailwind CSS
- Supabase integration for backend services
- Workflow automation system foundation
- Email and calendar functionality
- Message templates system
- Authentication with Supabase Auth
- Real-time updates capability
- Responsive UI with shadcn/ui components
