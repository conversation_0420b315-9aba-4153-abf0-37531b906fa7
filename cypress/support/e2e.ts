// ***********************************************************
// This file is processed and loaded automatically before your test files.
// You can change the location of this file or turn off processing
// of support files with the 'supportFile' configuration option.
// ***********************************************************

import './commands';

// Mock Supabase authentication
Cypress.Commands.add('login', (email = '<EMAIL>', password = 'password123') => {
  cy.window().then((win) => {
    // Mock the auth state
    win.localStorage.setItem('supabase.auth.token', JSON.stringify({
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
      expires_at: Date.now() + 3600000, // 1 hour from now
      user: {
        id: 'test-user-123',
        email: email,
        user_metadata: {
          name: 'Test User',
        },
      },
    }));
  });
});

Cypress.Commands.add('logout', () => {
  cy.window().then((win) => {
    win.localStorage.removeItem('supabase.auth.token');
  });
});

// Intercept Supabase API calls
Cypress.Commands.add('interceptSupabase', () => {
  // Intercept auth calls
  cy.intercept('POST', '**/auth/v1/token*', {
    statusCode: 200,
    body: {
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
      expires_in: 3600,
      user: {
        id: 'test-user-123',
        email: '<EMAIL>',
      },
    },
  }).as('auth');

  // Intercept workflow configurations
  cy.intercept('GET', '**/rest/v1/workflow_configurations*', {
    statusCode: 200,
    body: [
      {
        id: 'workflow-1',
        name: 'Candidate Screening Workflow',
        description: 'Automated screening for new candidates',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: 'test-user-123',
        configuration: {
          nodes: [],
          edges: [],
        },
      },
    ],
  }).as('getWorkflows');

  // Intercept workflow creation
  cy.intercept('POST', '**/rest/v1/workflow_configurations', (req) => {
    req.reply({
      statusCode: 201,
      body: {
        id: 'new-workflow-id',
        ...req.body,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    });
  }).as('createWorkflow');

  // Intercept workflow execution
  cy.intercept('POST', '**/functions/v1/workflow-executor', {
    statusCode: 200,
    body: {
      success: true,
      executionId: 'execution-123',
      logs: [],
    },
  }).as('executeWorkflow');
});

// Drag and drop support for workflow canvas
Cypress.Commands.add('dragNode', (nodeType, targetX, targetY) => {
  // Find the node in the toolbar
  cy.get(`[data-node-type="${nodeType}"]`).trigger('mousedown', { button: 0 });
  
  // Drag to canvas
  cy.get('.workflow-canvas').trigger('mousemove', targetX, targetY);
  cy.get('.workflow-canvas').trigger('mouseup', { force: true });
});

// Connect two nodes
Cypress.Commands.add('connectNodes', (sourceId, targetId) => {
  // Click on source node handle
  cy.get(`[data-node-id="${sourceId}"] .react-flow__handle-source`).click();
  
  // Click on target node handle
  cy.get(`[data-node-id="${targetId}"] .react-flow__handle-target`).click();
});

// Configure node
Cypress.Commands.add('configureNode', (nodeId, config) => {
  // Double-click to open config
  cy.get(`[data-node-id="${nodeId}"]`).dblclick();
  
  // Fill in configuration
  Object.entries(config).forEach(([key, value]) => {
    cy.get(`[name="${key}"]`).clear().type(value);
  });
  
  // Save configuration
  cy.get('[data-testid="save-config"]').click();
});

// Declare custom commands
declare global {
  namespace Cypress {
    interface Chainable {
      login(email?: string, password?: string): Chainable<void>;
      logout(): Chainable<void>;
      interceptSupabase(): Chainable<void>;
      dragNode(nodeType: string, targetX: number, targetY: number): Chainable<void>;
      connectNodes(sourceId: string, targetId: string): Chainable<void>;
      configureNode(nodeId: string, config: Record<string, any>): Chainable<void>;
    }
  }
}
