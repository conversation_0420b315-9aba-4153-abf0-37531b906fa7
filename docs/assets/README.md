# Documentation Assets

This directory contains screenshots and images for the documentation.

## Screenshot Guidelines

When creating screenshots for the documentation:

1. **Dimensions**: Use consistent dimensions (1200x800px recommended)
2. **Format**: PNG for screenshots, SVG for diagrams
3. **Naming**: Use descriptive kebab-case names matching the documentation references
4. **Annotations**: Add arrows, highlights, or callouts where helpful

## Required Screenshots

For the "Creating Your First Workflow" tutorial:

- `workflow-builder-nav.png` - Main navigation showing AI & Workflows menu
- `create-new-workflow.png` - New workflow creation dialog
- `add-trigger-node.png` - Trigger node configuration panel
- `add-action-node.png` - Action node being added to canvas
- `connect-nodes.png` - Connecting nodes with edge
- `configure-email-action.png` - Email action configuration panel
- `test-workflow.png` - Workflow test dialog
- `test-results.png` - Test execution results
- `activate-workflow.png` - Workflow activation toggle
- `workflow-monitoring.png` - Workflow history/monitoring view
- `conditional-logic.png` - Condition node example
- `multiple-actions.png` - Workflow with multiple actions

## Creating Placeholder Images

For development, you can create placeholder images:

```bash
# Using ImageMagick (if installed)
convert -size 1200x800 xc:gray -pointsize 48 -draw "text 400,400 'Workflow Builder Navigation'" workflow-builder-nav.png

# Or using a simple HTML file
# Create an HTML file with the placeholder text and take a screenshot
```

## Diagram Sources

Architecture diagrams can be created using:
- draw.io (diagrams.net)
- Mermaid diagrams
- Excalidraw
- PlantUML

Save both the source files and exported images for future updates.
