# Workflow Execution Engine Refactoring

## Overview

The Workflow Execution Engine has been refactored to support extensibility, async handling, parallel execution, and real-time event streaming. This document describes the key changes and new features.

## Key Features

### 1. Plug-in Executor Registry

All node executors are now registered in a central registry, making it easy to add new node types:

```typescript
// Register a new executor
import { executorRegistry } from '@/engine/ExecutorRegistry';
import { MyCustomExecutor } from './executors/my-custom';

executorRegistry.register(new MyCustomExecutor());
```

### 2. Async/Await Support with Retries and Timeouts

Every node executor supports:
- **Async execution** with proper error handling
- **Configurable timeouts** per node (default: 30s)
- **Automatic retries** with exponential backoff
- **Retry count tracking** for debugging

Example configuration:
```typescript
{
  nodeData: {
    config: {
      timeout: 10000,      // 10 second timeout
      retries: 3,          // Retry up to 3 times
      retryDelay: 1000     // 1 second base delay
    }
  }
}
```

### 3. Parallel Edge Execution

Nodes can execute multiple outgoing edges in parallel:

```typescript
// Node configuration for parallel execution
{
  config: {
    parallel: true,
    executionStrategy: { type: 'parallel' }
  }
}
```

The engine respects `maxParallelNodes` configuration to prevent resource exhaustion.

### 4. Wait Aggregation Strategies

The new `parallel-aggregator` node type supports various aggregation strategies:

- **all-success**: All parallel paths must succeed
- **any-success**: At least one path must succeed
- **majority-success**: Configurable percentage must succeed
- **collect-all**: Collect all results regardless of success

### 5. Real-time Event Streaming

All workflow execution events are streamed to Supabase Realtime channels:

```typescript
// Event types emitted
type ExecutionEvent = {
  type: 'started' | 'success' | 'error' | 'progress' | 'retry';
  nodeId: string;
  nodeName: string;
  timestamp: Date;
  data?: any;
  error?: Error;
}
```

Subscribe to events:
```typescript
const channel = supabase.channel(`workflow_progress_${workflowId}`);
channel.on('broadcast', { event: 'workflow_events' }, (payload) => {
  // Handle events
});
```

## New Executors

### AsyncApiCallExecutor
Makes HTTP API calls with full async support:
- Configurable timeouts
- Automatic retries on failure
- Request/response logging
- Error handling

### ParallelAggregatorExecutor
Aggregates results from parallel execution paths:
- Multiple aggregation strategies
- Configurable wait timeouts
- Result collection and analysis

## Architecture Changes

### BaseExecutor Class
All executors extend `BaseExecutor` which provides:
- Retry logic with exponential backoff
- Timeout enforcement
- Standard validation interface
- Execution context handling

### ExecutorRegistry
Central registry for all node executors:
- Dynamic executor discovery
- Category-based organization
- Runtime executor validation

### RealtimeEventPublisher
Singleton service for event streaming:
- Batch event publishing for efficiency
- Channel management
- Event queuing with retry
- Statistics and monitoring

## Usage Examples

### Basic Workflow Execution
```typescript
const engine = new WorkflowExecutionEngine(
  workflowId,
  userId,
  nodes,
  edges,
  { candidateId }
);

const result = await engine.execute();
```

### Monitoring Execution Progress
```typescript
// Subscribe to real-time events
const channel = supabase.channel(`workflow_progress_${workflowId}`);
channel.on('broadcast', { event: 'workflow_events' }, (payload) => {
  const events = payload.payload.events;
  events.forEach(event => {
    console.log(`${event.nodeName}: ${event.type}`);
  });
});

await channel.subscribe();
```

### Custom Node Executor
```typescript
export class MyCustomExecutor extends BaseExecutor {
  id = 'my-custom-node';
  name = 'My Custom Node';
  category = 'action' as const;

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    // Your async logic here
    return { success: true, data: result };
  }
}
```

## Configuration Options

```typescript
interface WorkflowExecutionConfig {
  enableRealtime?: boolean;        // Enable real-time events
  realtimeChannel?: string;        // Channel name prefix
  maxParallelNodes?: number;       // Max concurrent executions
  globalTimeout?: number;          // Overall workflow timeout
  defaultNodeTimeout?: number;     // Default node timeout
  defaultRetries?: number;         // Default retry count
  defaultRetryDelay?: number;      // Default retry delay
}
```

## Migration Guide

1. **Update node configurations** to include timeout and retry settings
2. **Register custom executors** with the ExecutorRegistry
3. **Subscribe to real-time channels** for progress monitoring
4. **Update UI components** to handle real-time events
5. **Test parallel execution paths** thoroughly

## Best Practices

1. **Set appropriate timeouts** based on expected execution time
2. **Use retry logic** for unreliable operations (API calls, etc.)
3. **Monitor real-time events** for debugging and user feedback
4. **Implement proper error handling** in custom executors
5. **Test aggregation strategies** for parallel workflows

## Performance Considerations

- Event batching reduces Supabase Realtime overhead
- Parallel execution is limited by `maxParallelNodes`
- Retry delays use exponential backoff to prevent overwhelming services
- Timeout enforcement prevents hanging workflows

## Future Enhancements

- [ ] Circuit breaker pattern for failing nodes
- [ ] Dynamic retry strategies based on error types
- [ ] Workflow versioning and migration
- [ ] Advanced monitoring and metrics
- [ ] Workflow templates and reusable components
