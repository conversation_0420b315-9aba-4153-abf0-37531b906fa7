# RLS / Index Verification Checklist for `workflow_*` Tables

## Tables

- [x] `workflow_configurations`
  - [x] RLS Enabled
  - Policies:
    - [x] View own workflows
    - [x] Create own workflows
    - [x] Update own workflows
    - [x] Delete own workflows
  - Indexes:
    - [x] `idx_workflow_configurations_created_by`

- [x] `workflow_executions`
  - [x] RLS Enabled
  - Policies:
    - [x] View own workflow executions
    - [x] Create workflow executions
    - [x] Update own workflow executions
  - Indexes:
    - [x] `idx_workflow_executions_workflow_id`
    - [x] `idx_workflow_executions_created_by`
    - [x] `idx_workflow_executions_status`

- [x] `workflow_schedules`
  - [x] RLS Enabled
  - Policies:
    - [x] View own workflow schedules
    - [x] Create workflow schedules
    - [x] Update own workflow schedules
    - [x] Delete own workflow schedules
  - Indexes:
    - [x] `idx_workflow_schedules_workflow_id`
    - [x] `idx_workflow_schedules_created_by`

- [x] `candidates`
  - [x] RLS Enabled
  - Policies:
    - [x] View own candidates
    - [x] Insert own candidates
    - [x] Update own candidates
    - [x] Delete own candidates
  - Indexes:
    - [x] `idx_candidates_user_id`
    - [x] `idx_candidates_name`
    - [x] `idx_candidates_role`
    - [x] `idx_candidates_email`
    - [x] `candidates_search_vector_idx`

- [x] `messages`
  - [x] RLS Enabled
  - Policies:
    - [x] Manage own messages
  - Indexes:
    - [x] `idx_messages_user_id`
    - [x] `idx_messages_status`

- [x] `message_templates`
  - [x] RLS Enabled
  - Policies:
    - [x] Manage own message templates
  - Indexes:
    - [x] `idx_message_templates_user_id`

- [x] `events`
  - [x] RLS Enabled
  - Policies:
    - [x] Manage own events
  - Indexes:
    - [x] `idx_events_user_id`
    - [x] `idx_events_start_time`

- [x] `candidate_timeline`
  - [x] RLS Enabled
  - Policies:
    - [x] Manage own candidate timeline
  - Indexes:
    - [x] Index on `candidate_id` (migration created)
    - [x] Index on `user_id` (migration created)

- [x] `candidate_interviews`
  - [x] RLS Enabled
  - Policies:
    - [x] Manage own candidate interviews
  - Indexes:
    - [x] Index on `candidate_id` (migration created)
    - [x] Index on `user_id` (migration created)
    - [x] Index on `scheduled_date` (migration created)

- [x] `candidate_notes`
  - [x] RLS Enabled
  - Policies:
    - [x] Manage own candidate notes
  - Indexes:
    - [x] Index on `candidate_id` (migration created)
    - [x] Index on `user_id` (migration created)

- [x] `jobs`
  - [x] RLS Enabled
  - Policies:
    - [x] View own jobs
    - [x] Insert own jobs
    - [x] Update own jobs
    - [x] Delete own jobs
  - Indexes:
    - [x] `idx_jobs_user_id`
    - [x] `idx_jobs_active`
    - [x] `idx_jobs_title`
    - [x] `idx_jobs_department`
    - [x] `idx_jobs_location`
    - [x] `idx_jobs_is_active`

## Recommendations

### Missing Indexes (Performance Optimization)
✅ All missing indexes have been addressed:
1. **candidate_timeline**: Migration created for indexes on `candidate_id` and `user_id`
2. **candidate_interviews**: Migration created for indexes on `candidate_id`, `user_id`, and `scheduled_date`
3. **candidate_notes**: Migration created for indexes on `candidate_id` and `user_id`

**Migration file**: `supabase/migrations/20240115_add_missing_candidate_indexes.sql`

### RLS Policy Consistency
All workflow-related tables have proper RLS policies in place. The policies follow a consistent pattern:
- Users can only manage their own data (auth.uid() = user_id or created_by)
- No public access is allowed
- All operations (SELECT, INSERT, UPDATE, DELETE) are properly restricted

### Security Best Practices Implemented
- ✅ All tables have RLS enabled
- ✅ No tables allow anonymous access
- ✅ User isolation is enforced at the database level
- ✅ Soft deletes implemented for workflow_configurations (is_active flag)
- ✅ Audit trail maintained through created_at/updated_at timestamps
