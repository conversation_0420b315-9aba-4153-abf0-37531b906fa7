Category,Node ID,Node Type,Label,Description,Implementation Status,Implementation Details
Triggers,new-application,trigger,New Application,Triggers when a new candidate applies,Real - Migrated,"Executor created, returns triggered status with timestamp and context"
Triggers,application-status,trigger,Status Change,Triggers when application status changes,Real - Migrated,"Executor created, validates status transitions, returns triggered status with context"
Triggers,scheduled-trigger,trigger,Schedule,Triggers at scheduled times,Real - Migrated,"Executor created with schedule validation, supports interval/cron/daily/weekly schedules"
Triggers,document-upload,trigger,Document Upload,Triggers when a document is uploaded,Mock,Returns default success response
Triggers,webhook-trigger,trigger,Webhook,Triggers from external webhook calls,Mock,Returns default success response
Triggers,database-trigger,trigger,Database Change,Triggers on database record changes,Mock,Returns default success response
Actions,send-email,action,Send Email,Send an automated email,Real - Migrated,"Executor created with retry/timeout support, creates message record, fetches templates, logs to database"
Actions,schedule-interview,action,Schedule Interview,Automatically schedule interviews,Real - Migrated,"Executor created with retry/timeout support, creates interview record, timeline entry, optional calendar event"
Actions,ai-screen,action,AI Screening,Screen resume with AI,Real - Migrated,"Executor created with retry/timeout support, performs AI screening with Gemini API, creates timeline entry"
Actions,send-assessment,action,Send Assessment,Send skills assessment test,Real - Migrated,"Executor created with full implementation, creates assessment records, timeline entry, sends notification"
Actions,notify-team,action,Notify Team,Send notification to team,Real - Migrated,"Executor created with retry/timeout support, creates message record in database, supports multiple channels"
Actions,data-enrichment,action,Data Enrichment,Enrich candidate data from external sources,Mock,Returns default success response
Actions,create-task,action,Create Task,Create a task for team members,Mock,Returns default success response
Actions,add-tag,action,Add Tag,Add tags to a candidate,Mock,Returns default success response
Actions,score-candidate,action,Score Candidate,Assign a score to a candidate,Mock,Returns default success response
Conditions,experience-check,condition,Experience Check,Check years of experience,Real - Migrated,"Executor created, extracts years from database/context, proper condition evaluation"
Conditions,education-check,condition,Education Check,Verify education requirements,Real - Migrated,"Executor created, checks degree levels from database/AI summary"
Conditions,skills-match,condition,Skills Match,Check required skills,Real - Migrated,"Executor created with retry/timeout support, evaluates skills match percentage, supports candidate context"
Conditions,location-check,condition,Location Check,Verify location requirements,Real - Migrated,"Executor created, checks location/remote preferences from database"
Conditions,salary-check,condition,Salary Check,Check salary requirements,Partial,Simulates salary range check
Conditions,availability-check,condition,Availability Check,Check candidate availability,Partial,Simulates availability check
Conditions,visa-check,condition,Visa Check,Check visa/work authorization status,Partial,Simulates visa status check
Conditions,filter-condition,condition,Filter Condition,Filter based on custom criteria,Mock,Returns default success response
Conditions,data-comparison,condition,Data Comparison,Compare data values,Mock,Returns default success response
Outputs,update-status,output,Update Status,Update application status,Real - Migrated,"Executor created with retry/timeout support, creates timeline entry, updates candidate status"
Outputs,add-to-pool,output,Add to Talent Pool,Add to specific talent pool,Real - Migrated,"Executor created with retry/timeout support, updates candidate tags, creates timeline entry"
Outputs,send-message,output,Send Message,Send message to candidate,Real - Migrated,"Executor created with retry/timeout support, creates message record, supports templates"
Outputs,export-data,output,Export Data,Export data to external system,Mock,Returns default success response
Outputs,archive-candidate,output,Archive Candidate,Archive a candidate record,Mock,Returns default success response
Outputs,generate-report,output,Generate Report,Generate a report from workflow data,Mock,Returns default success response
Transformations,data-transform,transformation,Data Transform,Transform data between steps,Real - Migrated,"Executor created with extract/format/merge/split/custom transforms"
Transformations,data-filter,transformation,Data Filter,Filter data between steps,Real - Migrated,"Executor created with include/exclude/range/custom filters, supports complex operators"
Transformations,data-merge,transformation,Data Merge,Merge data from multiple sources,Mock,Returns default success response
Transformations,data-loop,transformation,Loop,Loop through a collection of items,Mock,Returns default success response
Transformations,delay,transformation,Delay,Add a delay between steps,Real - Migrated,"Executor created with configurable delays up to 5 minutes, supports seconds/minutes"
Transformations,schedule,transformation,Schedule,Schedule a future action,Mock,Returns default success response
Integrations,linkedin-integration,integration,LinkedIn,Integrate with LinkedIn,Mock,Returns default success response
Integrations,job-board-integration,integration,Job Boards,Post to external job boards,Mock,Returns default success response
Integrations,ats-integration,integration,ATS Integration,Integrate with external ATS,Mock,Returns default success response
Integrations,calendar-integration,integration,Calendar,Integrate with calendar systems,Mock,Returns default success response
