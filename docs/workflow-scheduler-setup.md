# Workflow Scheduler Setup Guide

This guide explains how to set up the workflow scheduling infrastructure using Supabase Edge Functions and CRON jobs.

## Overview

The workflow scheduling system consists of:
1. **Webhook Handler** - Receives external webhook triggers
2. **CRON Scheduler** - Processes scheduled workflows
3. **Workflow Executor** - Executes the actual workflow logic

## Database Tables

The following tables are used:
- `workflow_configurations` - Stores workflow definitions
- `workflow_executions` - Tracks execution history
- `workflow_schedules` - Stores CRON schedules
- `workflow_webhooks` - Stores webhook configurations

## Edge Functions

### 1. Webhook Handler (`/webhooks/{id}`)

**Location**: `supabase/functions/webhooks/index.ts`

**Purpose**: Receives external POST requests, validates signatures, and enqueues workflow execution.

**Endpoint**: `https://your-project.supabase.co/functions/v1/webhooks/{webhook_id}`

**Headers Required**:
- `x-webhook-signature`: HMAC-SHA256 signature of the request body

**Example Usage**:
```bash
# Generate signature
SIGNATURE=$(echo -n "$BODY" | openssl dgst -sha256 -hmac "$WEBHOOK_SECRET" | cut -d' ' -f2)

# Send webhook
curl -X POST https://your-project.supabase.co/functions/v1/webhooks/webhook_abc123 \
  -H "Content-Type: application/json" \
  -H "x-webhook-signature: $SIGNATURE" \
  -d '{"event": "user.created", "data": {"id": "123", "email": "<EMAIL>"}}'
```

### 2. Workflow Scheduler (`workflow-scheduler`)

**Location**: `supabase/functions/workflow-scheduler/index.ts`

**Purpose**: Runs as a CRON job to process scheduled workflows.

**Setup CRON Job**:
1. Go to Supabase Dashboard > Edge Functions
2. Find the `workflow-scheduler` function
3. Add a CRON trigger:
   - Schedule: `*/5 * * * *` (every 5 minutes)
   - Or customize based on your needs

### 3. Workflow Executor (`workflow-executor`)

**Location**: `supabase/functions/workflow-executor/index.ts`

**Purpose**: Executes the actual workflow logic.

## Deployment

### Deploy Edge Functions

```bash
# Deploy all functions
supabase functions deploy

# Or deploy individually
supabase functions deploy webhooks
supabase functions deploy workflow-scheduler
supabase functions deploy workflow-executor
```

### Set Environment Variables

In Supabase Dashboard > Settings > Edge Functions:
- `SUPABASE_URL`: Your project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Service role key (for server-side operations)

## Client-Side Usage

### Creating a Webhook

```typescript
import { useCreateWorkflowWebhook } from '@/hooks/useWorkflowWebhooks';

const createWebhook = useCreateWorkflowWebhook();

// Create webhook
await createWebhook.mutateAsync({
  workflow_id: 'your-workflow-id',
  description: 'GitHub webhook for PR events'
});
```

### Creating a Schedule

```typescript
import { useCreateWorkflowSchedule } from '@/hooks/useWorkflowSchedules';

const createSchedule = useCreateWorkflowSchedule();

// Create daily schedule at 9 AM
await createSchedule.mutateAsync({
  workflow_id: 'your-workflow-id',
  cron_schedule: '0 9 * * *',
  context: {
    notification_type: 'daily_summary'
  }
});
```

### Manual Workflow Execution

```typescript
import { WorkflowExecutionService } from '@/services/WorkflowExecutionService';

// Execute workflow manually
const result = await WorkflowExecutionService.executeWorkflow({
  workflowId: 'your-workflow-id',
  userId: 'user-id',
  context: {
    candidate_id: '123',
    action: 'review'
  }
});

// Subscribe to execution updates
const unsubscribe = WorkflowExecutionService.subscribeToExecution(
  result.executionId,
  (status, logs) => {
    console.log('Execution status:', status);
    console.log('Logs:', logs);
  }
);
```

## CRON Expression Examples

- `*/15 * * * *` - Every 15 minutes
- `0 * * * *` - Every hour
- `0 9 * * *` - Daily at 9 AM
- `0 9 * * 1` - Every Monday at 9 AM
- `0 9,17 * * *` - Daily at 9 AM and 5 PM
- `0 0 1 * *` - First day of every month

## Security Considerations

1. **Webhook Signatures**: Always validate webhook signatures using HMAC-SHA256
2. **API Keys**: Store service role keys securely in environment variables
3. **RLS Policies**: Ensure proper Row Level Security policies are in place
4. **Rate Limiting**: Consider implementing rate limiting for webhook endpoints

## Monitoring

1. Check execution logs in Supabase Dashboard > Logs
2. Monitor the `workflow_executions` table for failed executions
3. Set up alerts for failed workflows using Supabase webhooks or external monitoring

## Troubleshooting

### Common Issues

1. **Webhook not triggering**:
   - Check if webhook is active (`is_active = true`)
   - Verify signature is correct
   - Check Edge Function logs

2. **Schedule not running**:
   - Verify CRON expression is valid
   - Check if schedule is active
   - Ensure `next_run` is properly set

3. **Workflow execution failing**:
   - Check workflow configuration is valid
   - Verify all required nodes are present
   - Check Edge Function logs for errors

### Debug Commands

```bash
# View Edge Function logs
supabase functions logs webhooks
supabase functions logs workflow-scheduler
supabase functions logs workflow-executor

# Test webhook locally
supabase functions serve webhooks --env-file .env.local

# Check database
SELECT * FROM workflow_schedules WHERE is_active = true;
SELECT * FROM workflow_executions ORDER BY created_at DESC LIMIT 10;
```
