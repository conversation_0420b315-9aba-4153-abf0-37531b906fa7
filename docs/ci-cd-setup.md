# CI/CD Setup Documentation

## Overview

This project uses GitHub Actions for continuous integration and deployment. The CI/CD pipeline includes:

- Code linting and TypeScript validation
- Unit and E2E testing
- Automated builds
- Supabase Edge Functions deployment
- Database migrations
- Automated version bumping and changelog generation

## GitHub Actions Workflows

### 1. CI/CD Pipeline (`.github/workflows/ci-cd.yml`)

Triggers on:
- Push to `main` or `develop` branches
- Pull requests to `main`
- Manual workflow dispatch

#### Jobs:

1. **Lint**
   - Runs ESLint
   - Checks TypeScript compilation

2. **Test**
   - Runs unit tests with coverage
   - Uploads coverage reports to Codecov

3. **E2E Test**
   - Runs Cypress E2E tests
   - Tests against preview build

4. **Build**
   - Builds production application
   - Uploads build artifacts

5. **Deploy Edge Functions** (main branch only)
   - Deploys all Supabase Edge Functions:
     - `send-email`
     - `webhooks`
     - `workflow-executor`
     - `workflow-scheduler`

6. **Deploy Migrations** (main branch only)
   - Runs database migrations via `supabase db push`
   - Updates TypeScript types
   - Runs seed script for non-production environments

7. **Deploy Hosting**
   - Downloads build artifacts
   - Deploys to hosting provider (configure as needed)

### 2. Release Management (`.github/workflows/release.yml`)

Triggers on:
- Push to `main` branch
- Manual workflow dispatch with release type selection

Features:
- Automatic version bumping based on commit messages:
  - `feat:` or `feature:` → minor version
  - `fix:` → patch version
  - `BREAKING CHANGE` → major version
- Changelog generation
- GitHub release creation
- Git tag creation

## Required GitHub Secrets

Configure these secrets in your GitHub repository settings:

```yaml
# Supabase
SUPABASE_ACCESS_TOKEN    # Supabase CLI access token
SUPABASE_PROJECT_ID      # Your Supabase project ID
SUPABASE_DB_PASSWORD     # Database password

# Application
VITE_SUPABASE_URL        # Supabase project URL
VITE_SUPABASE_ANON_KEY   # Supabase anonymous key
VITE_GEMINI_API_KEY      # Google Gemini API key (if using AI features)

# Testing
CYPRESS_SUPABASE_URL     # Same as VITE_SUPABASE_URL
CYPRESS_SUPABASE_ANON_KEY # Same as VITE_SUPABASE_ANON_KEY

# Optional
CODECOV_TOKEN            # For coverage reports
VERCEL_TOKEN             # If deploying to Vercel
VERCEL_ORG_ID           # Vercel organization ID
VERCEL_PROJECT_ID       # Vercel project ID
```

## Local Development Scripts

```bash
# Database operations
npm run db:seed          # Run seed script
npm run db:reset         # Reset database
npm run db:push          # Push migrations
npm run db:types         # Generate TypeScript types

# Testing
npm run test             # Run unit tests
npm run test:coverage    # Run tests with coverage
npm run test:e2e         # Run E2E tests
npm run test:e2e:open    # Open Cypress UI

# Development
npm run dev              # Start development server
npm run build            # Build for production
npm run preview          # Preview production build
npm run lint             # Run ESLint
```

## Database Seeding

The `supabase/seed.sql` file contains sample data including:

1. **Workflow Configurations**
   - Interview Scheduling Workflow
   - Candidate Follow-up Workflow
   - Job Posting Distribution Workflow
   - Onboarding Workflow

2. **Message Templates**
   - Interview invitations
   - Follow-up emails
   - Job offers
   - Application confirmations

3. **Sample Events**
   - Interviews
   - Team meetings
   - Client meetings

Seeds are automatically run in non-production environments during deployment.

## Commit Message Convention

For automatic version bumping, use conventional commit messages:

```
feat(scope): Add new feature       # Minor version bump
fix(scope): Fix bug               # Patch version bump
chore(scope): Update dependencies # No version bump
docs(scope): Update documentation # No version bump

BREAKING CHANGE: Description      # Major version bump
```

## Deployment Flow

1. **Development**
   - Push to `develop` branch
   - CI/CD runs tests and builds
   - No deployment occurs

2. **Production**
   - Merge to `main` branch
   - CI/CD runs full pipeline
   - Deploys Edge Functions and migrations
   - Creates new release with changelog
   - Tags version in Git

## Troubleshooting

### Common Issues

1. **Supabase CLI Authentication**
   - Ensure `SUPABASE_ACCESS_TOKEN` is set correctly
   - Token can be generated at: https://app.supabase.com/account/tokens

2. **Type Generation Failures**
   - Ensure migrations are applied before generating types
   - Check database connection settings

3. **E2E Test Failures**
   - Verify preview server is running
   - Check Cypress environment variables

4. **Deployment Failures**
   - Verify all secrets are configured
   - Check Supabase project status
   - Review Edge Function logs

## Best Practices

1. Always write tests for new features
2. Use conventional commit messages
3. Keep workflows DRY - use composite actions for repeated steps
4. Monitor workflow run times and optimize as needed
5. Review and update dependencies regularly
6. Test workflows in feature branches before merging
