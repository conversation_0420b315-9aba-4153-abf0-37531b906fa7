# API Reference

## Table of Contents
- [Workflow Node Schema](#workflow-node-schema)
- [Edge Function Endpoints](#edge-function-endpoints)
- [Database Schema](#database-schema)
- [Real-time Events](#real-time-events)

## Workflow Node Schema

### Base Node Structure

All workflow nodes follow this base structure:

```typescript
interface WorkflowNode {
  id: string;
  type: 'trigger' | 'action' | 'condition' | 'transformation' | 'output';
  position: {
    x: number;
    y: number;
  };
  data: {
    label?: string;
    config: NodeConfig; // Type-specific configuration
  };
}
```

### Node Types

#### 1. Trigger Nodes

Trigger nodes initiate workflow execution.

```typescript
interface TriggerNode extends WorkflowNode {
  type: 'trigger';
  data: {
    label?: string;
    config: {
      triggerType: 'manual' | 'schedule' | 'webhook' | 'event';
      schedule?: string; // Cron expression for scheduled triggers
      event?: string; // Event name for event triggers
      webhookId?: string; // Webhook ID for webhook triggers
    };
  };
}
```

**Example:**
```json
{
  "id": "trigger-1",
  "type": "trigger",
  "position": { "x": 100, "y": 100 },
  "data": {
    "label": "Daily Morning Trigger",
    "config": {
      "triggerType": "schedule",
      "schedule": "0 9 * * *"
    }
  }
}
```

#### 2. Action Nodes

Action nodes perform operations like sending emails, updating records, or calling APIs.

```typescript
interface ActionNode extends WorkflowNode {
  type: 'action';
  data: {
    label?: string;
    config: {
      actionType: string;
      parameters: Record<string, any>;
    };
  };
}
```

**Available Action Types:**
- `send_email`: Send email notifications
- `update_status`: Update candidate or job status
- `send_notification`: Send in-app notifications
- `schedule_interview`: Schedule an interview
- `add_to_pool`: Add candidate to talent pool
- `send_assessment`: Send assessment to candidate

**Example:**
```json
{
  "id": "action-1",
  "type": "action",
  "position": { "x": 300, "y": 100 },
  "data": {
    "label": "Send Welcome Email",
    "config": {
      "actionType": "send_email",
      "parameters": {
        "to": "{{candidate.email}}",
        "template": "welcome",
        "subject": "Welcome to Our Process",
        "variables": {
          "name": "{{candidate.name}}",
          "position": "{{job.title}}"
        }
      }
    }
  }
}
```

#### 3. Condition Nodes

Condition nodes create branching logic in workflows.

```typescript
interface ConditionNode extends WorkflowNode {
  type: 'condition';
  data: {
    label?: string;
    config: {
      field: string;
      operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
      value: any;
    };
  };
}
```

**Example:**
```json
{
  "id": "condition-1",
  "type": "condition",
  "position": { "x": 500, "y": 100 },
  "data": {
    "label": "Check Experience",
    "config": {
      "field": "candidate.years_experience",
      "operator": "greater_than",
      "value": 5
    }
  }
}
```

#### 4. Transformation Nodes

Transformation nodes modify data as it flows through the workflow.

```typescript
interface TransformationNode extends WorkflowNode {
  type: 'transformation';
  data: {
    label?: string;
    config: {
      transformationType: 'map_fields' | 'filter' | 'aggregate' | 'format';
      mapping?: Record<string, string>;
      filter?: any;
      format?: string;
    };
  };
}
```

**Example:**
```json
{
  "id": "transform-1",
  "type": "transformation",
  "position": { "x": 700, "y": 100 },
  "data": {
    "label": "Format Candidate Data",
    "config": {
      "transformationType": "map_fields",
      "mapping": {
        "candidate.email": "contact_email",
        "candidate.phone": "contact_phone",
        "candidate.name": "full_name"
      }
    }
  }
}
```

### Edge Schema

Edges connect nodes and define the workflow execution path.

```typescript
interface WorkflowEdge {
  id: string;
  source: string; // Source node ID
  target: string; // Target node ID
  sourceHandle?: string; // For condition nodes: 'true' or 'false'
  type?: 'default' | 'smoothstep';
  animated?: boolean;
  label?: string;
}
```

## Edge Function Endpoints

### 1. Workflow Executor

**Endpoint:** `POST /workflow-executor`

Executes a workflow instance.

**Request Body:**
```json
{
  "execution_id": "string",
  "workflow_id": "string",
  "user_id": "string",
  "context": {
    // Initial workflow context data
  }
}
```

**Response:**
```json
{
  "success": true,
  "execution_id": "string",
  "logs": [
    {
      "nodeId": "string",
      "status": "started" | "completed" | "error",
      "timestamp": "ISO 8601",
      "message": "string",
      "data": {}
    }
  ],
  "message": "Workflow executed successfully"
}
```

**Error Response:**
```json
{
  "error": "string",
  "details": {}
}
```

### 2. Workflow Scheduler

**Endpoint:** `POST /workflow-scheduler`

Processes scheduled workflows. This is typically called by a cron job.

**Headers:**
```
Authorization: Bearer <service_role_key>
```

**Response:**
```json
{
  "success": true,
  "processed": 5,
  "errors": 0,
  "message": "Processed 5 scheduled workflows",
  "timestamp": "ISO 8601"
}
```

### 3. Webhook Handler

**Endpoint:** `POST /webhooks/{webhook_id}`

Receives webhook events and triggers associated workflows.

**Headers:**
```
x-webhook-signature: <hmac_signature>
Content-Type: application/json
```

**Request Body:**
Any JSON payload from the external service.

**Response:**
```json
{
  "success": true,
  "execution_id": "string",
  "message": "Workflow execution queued successfully"
}
```

### 4. Send Email Function

**Endpoint:** `POST /send-email`

Sends emails using configured email service.

**Request Body:**
```json
{
  "to": ["<EMAIL>"],
  "subject": "string",
  "html": "string",
  "text": "string",
  "from": "<EMAIL>",
  "replyTo": "<EMAIL>",
  "attachments": [
    {
      "filename": "string",
      "content": "base64_string",
      "contentType": "string"
    }
  ]
}
```

## Database Schema

### Core Tables

#### workflow_configurations
```sql
CREATE TABLE workflow_configurations (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  config JSONB NOT NULL, -- Contains nodes and edges
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### workflow_executions
```sql
CREATE TABLE workflow_executions (
  id UUID PRIMARY KEY,
  workflow_id UUID REFERENCES workflow_configurations(id),
  status TEXT CHECK (status IN ('pending', 'running', 'completed', 'failed')),
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  logs JSONB[],
  execution_data JSONB,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### workflow_schedules
```sql
CREATE TABLE workflow_schedules (
  id UUID PRIMARY KEY,
  workflow_id UUID REFERENCES workflow_configurations(id),
  cron_schedule TEXT NOT NULL,
  timezone TEXT DEFAULT 'UTC',
  is_active BOOLEAN DEFAULT true,
  last_run TIMESTAMPTZ,
  next_run TIMESTAMPTZ,
  context JSONB,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### workflow_webhooks
```sql
CREATE TABLE workflow_webhooks (
  id UUID PRIMARY KEY,
  workflow_id UUID REFERENCES workflow_configurations(id),
  webhook_url TEXT UNIQUE NOT NULL,
  webhook_secret TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  last_triggered TIMESTAMPTZ,
  trigger_count INTEGER DEFAULT 0,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Real-time Events

### Workflow Execution Events

Subscribe to workflow execution updates:

```javascript
const channel = supabase
  .channel('workflow-executions')
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'workflow_executions'
    },
    (payload) => {
      console.log('Workflow execution update:', payload);
    }
  )
  .subscribe();
```

### Workflow Execution Queue

Internal queue for workflow execution:

```javascript
const channel = supabase
  .channel('workflow-execution-queue')
  .on(
    'broadcast',
    { event: 'execute-workflow' },
    (payload) => {
      // Handle workflow execution
    }
  )
  .subscribe();
```

## Authentication & Security

### API Authentication

All Edge Function endpoints require authentication:

```javascript
// Using Supabase client
const { data, error } = await supabase.functions.invoke('workflow-executor', {
  body: { /* request data */ }
});

// Using fetch with auth header
const response = await fetch('https://your-project.supabase.co/functions/v1/workflow-executor', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${supabase.auth.session().access_token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ /* request data */ })
});
```

### Webhook Security

Webhooks are secured using HMAC-SHA256 signatures:

```javascript
// Verify webhook signature
const signature = request.headers.get('x-webhook-signature');
const isValid = await verifyWebhookSignature(payload, signature, secret);
```

## Error Handling

### Standard Error Format

All API errors follow this format:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      // Additional error context
    }
  }
}
```

### Common Error Codes

- `WORKFLOW_NOT_FOUND`: Workflow configuration doesn't exist
- `INVALID_NODE_CONFIG`: Node configuration is invalid
- `EXECUTION_FAILED`: Workflow execution failed
- `UNAUTHORIZED`: Missing or invalid authentication
- `INVALID_WEBHOOK_SIGNATURE`: Webhook signature verification failed
- `RATE_LIMIT_EXCEEDED`: Too many requests

## Rate Limiting

- **Workflow Executions**: 100 per minute per user
- **Webhook Calls**: 1000 per hour per webhook
- **Email Sending**: 50 per minute per organization

## Examples

### Creating a Simple Workflow

```javascript
const workflow = {
  name: "New Candidate Welcome",
  description: "Send welcome email to new candidates",
  config: {
    nodes: [
      {
        id: "trigger-1",
        type: "trigger",
        position: { x: 100, y: 100 },
        data: {
          label: "New Candidate",
          config: {
            triggerType: "event",
            event: "candidate.created"
          }
        }
      },
      {
        id: "action-1",
        type: "action",
        position: { x: 300, y: 100 },
        data: {
          label: "Send Welcome Email",
          config: {
            actionType: "send_email",
            parameters: {
              to: "{{candidate.email}}",
              template: "candidate_welcome"
            }
          }
        }
      }
    ],
    edges: [
      {
        id: "edge-1",
        source: "trigger-1",
        target: "action-1",
        type: "smoothstep"
      }
    ]
  }
};

const { data, error } = await supabase
  .from('workflow_configurations')
  .insert(workflow);
```

### Executing a Workflow Manually

```javascript
const { data: execution } = await supabase
  .from('workflow_executions')
  .insert({
    workflow_id: 'workflow-uuid',
    status: 'pending',
    execution_data: {
      trigger: 'manual',
      context: {
        candidate_id: 'candidate-uuid'
      }
    }
  })
  .select()
  .single();

// Trigger execution
await supabase.functions.invoke('workflow-executor', {
  body: {
    execution_id: execution.id,
    workflow_id: execution.workflow_id,
    user_id: user.id,
    context: execution.execution_data.context
  }
});
```
