# Workflow Observability & Error Reporting

This document describes the observability and error reporting system implemented for the workflow execution engine.

## Overview

The system provides comprehensive monitoring, logging, metrics collection, and alerting capabilities for workflow executions. It includes:

1. **Central Logger** - Writes to both `workflow_executions` table and Sentry
2. **Metrics Collection** - Tracks execution duration, success rates, and per-node error counts
3. **Alert System** - Sends notifications via Slack and Email when configured

## Components

### 1. Central Logger (`WorkflowLogger`)

Located in `src/services/WorkflowLogger.ts`, this service provides:

- **Multi-level logging**: DEBUG, INFO, WARN, ERROR, FATAL
- **Buffered writes**: Logs are buffered and flushed every 30 seconds or immediately for errors
- **Sentry integration**: Automatic error tracking with Sentry
- **Structured logging**: All logs include workflow ID, execution ID, node ID, and metadata

#### Usage:

```typescript
import { workflowLogger, LogLevel } from '@/services/WorkflowLogger';

// Log an info message
await workflowLogger.log({
  level: LogLevel.INFO,
  message: 'Workflow started',
  workflowId: 'xxx',
  executionId: 'yyy',
  metadata: { customData: 'value' }
});

// Log an error
await workflowLogger.log({
  level: LogLevel.ERROR,
  message: 'Node execution failed',
  workflowId: 'xxx',
  nodeId: 'zzz',
  error: new Error('Connection timeout')
});
```

### 2. Metrics System

Metrics are automatically collected during workflow execution and stored in the `workflow_metrics` table:

- **Execution Duration**: Total time from start to finish
- **Node Counts**: Total, successful, failed, and skipped nodes
- **Error Details**: Detailed error information for each failed node
- **Performance Metrics**: Memory usage and CPU utilization (when available)

#### Database Schema:

```sql
workflow_metrics
├── id (UUID)
├── workflow_id (UUID)
├── execution_id (UUID)
├── execution_duration_ms (INTEGER)
├── start_time (TIMESTAMP)
├── end_time (TIMESTAMP)
├── total_nodes_executed (INTEGER)
├── successful_nodes (INTEGER)
├── failed_nodes (INTEGER)
├── skipped_nodes (INTEGER)
├── node_errors (JSONB)
├── peak_memory_mb (FLOAT)
├── cpu_usage_percent (FLOAT)
├── context_data (JSONB)
├── error_details (TEXT)
└── created_at (TIMESTAMP)
```

### 3. Alert Service (`WorkflowAlertService`)

Located in `src/services/WorkflowAlertService.ts`, this service handles:

- **Email Alerts**: Via Supabase Edge Function
- **Slack Alerts**: Via webhook integration
- **Alert Conditions**: Failure, success, or duration threshold exceeded
- **Rate Limiting**: Prevents alert spam with configurable limits

#### Configuration:

Alerts are configured per workflow in the `workflow_alerts` table:

```sql
workflow_alerts
├── id (UUID)
├── workflow_id (UUID)
├── enable_email (BOOLEAN)
├── email_addresses (TEXT[])
├── enable_slack (BOOLEAN)
├── slack_webhook_url (TEXT)
├── slack_channel (TEXT)
├── alert_on_failure (BOOLEAN)
├── alert_on_success (BOOLEAN)
├── alert_on_duration_threshold (BOOLEAN)
├── duration_threshold_ms (INTEGER)
├── max_alerts_per_hour (INTEGER)
└── is_active (BOOLEAN)
```

## UI Components

### 1. WorkflowAlertSettings

Located in `src/components/ai/workflow/WorkflowAlertSettings.tsx`

Provides a user interface for configuring alerts:
- Enable/disable alerts
- Configure email recipients
- Set up Slack webhooks
- Define alert conditions
- Set rate limits

### 2. WorkflowMetrics

Located in `src/components/ai/workflow/WorkflowMetrics.tsx`

Displays comprehensive metrics dashboard:
- Summary cards (total executions, success rate, avg duration, failed nodes)
- Execution trend charts
- Success/failure distribution
- Per-workflow statistics

## Setup Instructions

### 1. Environment Variables

Add these to your `.env` file:

```bash
# Sentry Configuration (optional but recommended)
VITE_SENTRY_DSN=your-sentry-dsn
VITE_ENVIRONMENT=development

# Email Service (for Edge Function)
EMAIL_SERVICE_URL=https://api.sendgrid.com/v3/mail/send
EMAIL_SERVICE_API_KEY=your-sendgrid-api-key
```

### 2. Database Migration

Run the migration to create the required tables:

```bash
supabase migration up
```

### 3. Deploy Edge Function

Deploy the email sending function:

```bash
supabase functions deploy send-email
```

### 4. Slack Webhook Setup

1. Go to your Slack workspace settings
2. Create an incoming webhook
3. Copy the webhook URL
4. Add it to the workflow alert settings

## Usage Example

### Setting Up Alerts for a Workflow

```typescript
// In your workflow management UI
<WorkflowAlertSettings 
  workflowId={workflow.id} 
  workflowName={workflow.name} 
/>
```

### Viewing Metrics

```typescript
// Display metrics for a specific workflow
<WorkflowMetrics workflowId={workflow.id} />

// Display metrics for all workflows
<WorkflowMetrics showAllWorkflows={true} />
```

### Integration with WorkflowExecutionEngine

The logging and alerting is automatically integrated into the `WorkflowExecutionEngine`:

1. Logs are written at key execution points
2. Metrics are collected after each execution
3. Alerts are sent based on configured conditions

## Monitoring Best Practices

1. **Set Appropriate Alert Thresholds**
   - Don't alert on every failure if workflows are expected to fail occasionally
   - Set duration thresholds based on normal execution times

2. **Use Rate Limiting**
   - Prevent alert fatigue by limiting alerts per hour
   - Default is 5 alerts per hour per workflow

3. **Monitor Key Metrics**
   - Watch success rates trend over time
   - Identify workflows with consistently high failure rates
   - Track performance degradation

4. **Regular Review**
   - Check the metrics dashboard weekly
   - Review and update alert configurations
   - Analyze failure patterns

## Troubleshooting

### Alerts Not Being Sent

1. Check alert configuration is active
2. Verify rate limits haven't been exceeded
3. Check Supabase Edge Function logs
4. Verify Slack webhook URL is correct
5. Ensure email addresses are valid

### Missing Metrics

1. Check workflow execution completed
2. Verify user has proper permissions
3. Check browser console for errors
4. Ensure database migrations are up to date

### Sentry Not Capturing Errors

1. Verify `VITE_SENTRY_DSN` is set
2. Check Sentry project settings
3. Ensure errors are being thrown (not just logged)

## Future Enhancements

1. **Additional Alert Channels**
   - SMS notifications
   - Microsoft Teams integration
   - PagerDuty integration

2. **Advanced Metrics**
   - Resource usage tracking
   - Cost estimation
   - Predictive failure analysis

3. **Custom Dashboards**
   - Exportable reports
   - Custom metric aggregations
   - Real-time monitoring dashboard
