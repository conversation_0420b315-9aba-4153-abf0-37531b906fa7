# Workflow System Data Flow Diagram

## Component → Hook → DB Table Interactions

```mermaid
graph TB
    %% UI Components
    subgraph "UI Components"
        AIWorkflows[AIWorkflows Page]
        WorkflowCanvas[WorkflowCanvas]
        WorkflowManager[WorkflowManager]
        WorkflowExecutor[WorkflowExecutor]
        WorkflowScheduler[WorkflowScheduler]
        WorkflowAnalytics[WorkflowAnalytics]
        WorkflowHistory[WorkflowHistory]
        RunWorkflowDialog[RunWorkflowDialog]
    end

    %% Custom Hooks
    subgraph "Custom Hooks"
        useWorkflowConfigurations[useWorkflowConfigurations]
        useWorkflowExecution[useWorkflowExecution]
        useWorkflowSchedules[useWorkflowSchedules]
    end

    %% Execution Engine
    subgraph "Execution Layer"
        WorkflowExecutionEngine[WorkflowExecutionEngine]
        WorkflowValidation[WorkflowValidation]
    end

    %% Database Tables
    subgraph "Database Tables"
        workflow_configurations[workflow_configurations]
        workflow_executions[workflow_executions]
        workflow_schedules[workflow_schedules]
        candidates[candidates]
        candidate_timeline[candidate_timeline]
        candidate_interviews[candidate_interviews]
        candidate_notes[candidate_notes]
        messages[messages]
        message_templates[message_templates]
        events[events]
        jobs[jobs]
    end

    %% Component to Hook connections
    AIWorkflows --> useWorkflowConfigurations
    WorkflowCanvas --> useWorkflowConfigurations
    WorkflowManager --> useWorkflowConfigurations
    WorkflowManager --> useWorkflowSchedules
    WorkflowExecutor --> useWorkflowExecution
    WorkflowScheduler --> useWorkflowSchedules
    WorkflowAnalytics --> useWorkflowConfigurations
    WorkflowAnalytics --> useWorkflowExecution
    WorkflowHistory --> useWorkflowExecution
    RunWorkflowDialog --> useWorkflowExecution

    %% Hook to Database connections
    useWorkflowConfigurations --> workflow_configurations
    useWorkflowExecution --> workflow_executions
    useWorkflowExecution --> workflow_configurations
    useWorkflowSchedules --> workflow_schedules

    %% Execution Engine connections
    useWorkflowExecution --> WorkflowExecutionEngine
    WorkflowExecutionEngine --> WorkflowValidation
    WorkflowExecutionEngine --> workflow_executions

    %% Workflow actions to database connections
    WorkflowValidation --> candidates
    WorkflowValidation --> candidate_timeline
    WorkflowValidation --> candidate_interviews
    WorkflowValidation --> candidate_notes
    WorkflowValidation --> messages
    WorkflowValidation --> message_templates
    WorkflowValidation --> events

    %% Component styles
    classDef component fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef hook fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef engine fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef database fill:#e8f5e9,stroke:#1b5e20,stroke-width:2px

    class AIWorkflows,WorkflowCanvas,WorkflowManager,WorkflowExecutor,WorkflowScheduler,WorkflowAnalytics,WorkflowHistory,RunWorkflowDialog component
    class useWorkflowConfigurations,useWorkflowExecution,useWorkflowSchedules hook
    class WorkflowExecutionEngine,WorkflowValidation engine
    class workflow_configurations,workflow_executions,workflow_schedules,candidates,candidate_timeline,candidate_interviews,candidate_notes,messages,message_templates,events,jobs database
```

## Data Flow Description

### 1. **Workflow Configuration Flow**
- **Components**: `AIWorkflows`, `WorkflowCanvas`, `WorkflowManager`
- **Hook**: `useWorkflowConfigurations`
- **Table**: `workflow_configurations`
- **Operations**: CREATE, READ, UPDATE, DELETE (soft delete via is_active flag)

### 2. **Workflow Execution Flow**
- **Components**: `WorkflowExecutor`, `RunWorkflowDialog`, `WorkflowHistory`
- **Hook**: `useWorkflowExecution`
- **Engine**: `WorkflowExecutionEngine`
- **Tables**: `workflow_executions`, `workflow_configurations`
- **Operations**: CREATE execution logs, READ workflow config, UPDATE execution status

### 3. **Workflow Scheduling Flow**
- **Components**: `WorkflowScheduler`, `WorkflowManager`
- **Hook**: `useWorkflowSchedules`
- **Table**: `workflow_schedules`
- **Operations**: CREATE schedules, UPDATE schedule status, DELETE schedules

### 4. **Workflow Actions Data Flow**
The `WorkflowValidation` module executes various node actions that interact with:
- **Candidate Management**: `candidates`, `candidate_timeline`, `candidate_interviews`, `candidate_notes`
- **Communication**: `messages`, `message_templates`
- **Calendar**: `events`
- **Jobs**: `jobs` (for job-related workflows)

## Key Integration Points

1. **Supabase Client**: All hooks use the Supabase client for database operations
2. **Auth Context**: User authentication is required for all operations (RLS policies)
3. **Real-time Updates**: React Query handles data synchronization
4. **Error Handling**: Toast notifications for user feedback

## Security Considerations

- All tables have Row Level Security (RLS) enabled
- Users can only access their own data (auth.uid() = user_id/created_by)
- Soft deletes for workflow configurations (is_active flag)
