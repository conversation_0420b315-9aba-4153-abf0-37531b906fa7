modules = ["nodejs-20", "web"]
run = "npm run dev"

[nix]
channel = "stable-24_05"
packages = ["supabase-cli"]

[deployment]
run = ["sh", "-c", "npm run dev"]

[workflows]
runButton = "Run"

[[workflows.workflow]]
name = "Run"
author = 38428337
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"

[[ports]]
localPort = 8080
externalPort = 80

[[ports]]
localPort = 8081
externalPort = 8081
