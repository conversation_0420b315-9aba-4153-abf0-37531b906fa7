# RMS-Refresh: Modern Recruitment Management System

A comprehensive recruitment management platform with advanced workflow automation, AI-powered candidate screening, and real-time collaboration features.

## 🏗 Architecture Overview

### System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                         Frontend (React)                         │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌──────────────┐  ┌───────────────────────┐  │
│  │   UI Layer  │  │  Workflow     │  │   State Management    │  │
│  │  Components │  │  Canvas       │  │   (Context + Hooks)   │  │
│  │  (shadcn)   │  │  (React Flow) │  │                       │  │
│  └─────────────┘  └──────────────┘  └───────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Backend (Supabase)                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌──────────────┐  ┌───────────────────────┐  │
│  │  Database   │  │    Edge      │  │     Real-time         │  │
│  │ (PostgreSQL)│  │  Functions   │  │    Subscriptions      │  │
│  │             │  │              │  │                       │  │
│  └─────────────┘  └──────────────┘  └───────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### Key Components

#### 1. **Frontend Architecture**
- **React + TypeScript**: Type-safe component development
- **Vite**: Fast build tooling and hot module replacement
- **React Flow**: Visual workflow builder with custom nodes
- **shadcn/ui**: Modern, accessible UI components
- **Tailwind CSS**: Utility-first styling

#### 2. **Backend Services**
- **Supabase Database**: PostgreSQL with row-level security
- **Edge Functions**: Serverless workflow execution
- **Real-time Subscriptions**: Live updates across the platform
- **Storage**: Secure document and file management

#### 3. **Workflow Engine**
- **Visual Builder**: Drag-and-drop workflow creation
- **Node Types**: Triggers, Actions, Conditions, Transformations
- **Execution Engine**: Serverless workflow processing
- **Scheduler**: Cron-based workflow scheduling
- **Webhooks**: External integration endpoints

### Data Flow

```
User Action → Frontend → API Call → Edge Function → Database
     ↑                                    │
     └────── Real-time Update ←───────────┘
```

## 🚀 Developer Setup

### Prerequisites

- Node.js 18+ and npm
- Supabase CLI
- Git

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/rms-refresh.git
   cd rms-refresh
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Update `.env.local` with your Supabase credentials:
   ```env
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Start Supabase locally (optional)**
   ```bash
   supabase start
   ```

5. **Run database migrations**
   ```bash
   supabase db push
   ```

6. **Deploy Edge Functions**
   ```bash
   supabase functions deploy
   ```

7. **Start the development server**
   ```bash
   npm run dev
   ```

### Project Structure

```
rms-refresh/
├── src/
│   ├── components/       # React components
│   │   ├── ai/          # AI & workflow components
│   │   ├── candidate/   # Candidate management
│   │   ├── job/         # Job posting components
│   │   └── ui/          # Shared UI components
│   ├── engine/          # Workflow execution engine
│   ├── hooks/           # Custom React hooks
│   ├── pages/           # Route components
│   ├── services/        # Business logic services
│   └── utils/           # Helper functions
├── supabase/
│   ├── functions/       # Edge Functions
│   └── migrations/      # Database migrations
├── docs/                # Documentation
└── public/             # Static assets
```

## 🔧 Development Workflow

### Code Style

- ESLint and Prettier are configured for code consistency
- Run `npm run lint` to check for issues
- Run `npm run format` to auto-format code

### Testing

```bash
# Run unit tests
npm run test

# Run integration tests
npm run test:integration

# Run all tests with coverage
npm run test:coverage
```

### Building for Production

```bash
# Build the application
npm run build

# Preview production build
npm run preview
```

### Database Management

```bash
# Create a new migration
supabase migration new <migration_name>

# Apply migrations
supabase db push

# Reset database
supabase db reset
```

## 📚 Core Features

### 1. Workflow Automation
- Visual workflow builder with drag-and-drop interface
- Pre-built workflow templates
- Custom node types for various automation tasks
- Real-time execution monitoring

### 2. Candidate Management
- Comprehensive candidate profiles
- Document management and parsing
- Interview scheduling and tracking
- AI-powered candidate screening

### 3. Job Management
- Multi-channel job posting
- Applicant tracking system (ATS)
- Pipeline visualization
- Analytics and reporting

### 4. Communication Hub
- Email templates and automation
- In-app messaging
- Calendar integration
- Team collaboration tools

### 5. Analytics Dashboard
- Recruitment metrics and KPIs
- Source effectiveness tracking
- Diversity and inclusion metrics
- Custom report generation

## 🔒 Security

- Row-level security (RLS) on all database tables
- JWT-based authentication
- Secure file storage with access controls
- CORS configuration for API endpoints
- Environment variable protection

## 🚀 Deployment

### Supabase Platform

1. Create a new Supabase project
2. Run migrations in the Supabase dashboard
3. Deploy Edge Functions
4. Update environment variables
5. Deploy frontend to your preferred hosting service

### Vercel Deployment

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel
```

## 📖 Additional Resources

- [API Documentation](./docs/api-reference.md)
- [Workflow Node Reference](./docs/workflows/node-spec.md)
- [Creating Your First Workflow](./docs/tutorials/first-workflow.md)
- [Contributing Guide](./CONTRIBUTING.md)

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.
