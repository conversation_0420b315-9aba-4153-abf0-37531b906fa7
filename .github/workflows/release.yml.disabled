name: Release & Version Management

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Release type (patch, minor, major)'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major

permissions:
  contents: write
  pull-requests: write
  issues: write

jobs:
  release:
    name: Create Release
    runs-on: ubuntu-latest
    if: "!contains(github.event.head_commit.message, 'chore(release)')"
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Configure Git
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      - name: Determine version bump type
        id: version_type
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "type=${{ github.event.inputs.release_type }}" >> $GITHUB_OUTPUT
          else
            # Analyze commit messages to determine version bump
            if git log -1 --pretty=%B | grep -qiE "^(feat|feature)(\(.+\))?: .+"; then
              echo "type=minor" >> $GITHUB_OUTPUT
            elif git log -1 --pretty=%B | grep -qiE "^fix(\(.+\))?: .+"; then
              echo "type=patch" >> $GITHUB_OUTPUT
            elif git log -1 --pretty=%B | grep -qiE "BREAKING CHANGE"; then
              echo "type=major" >> $GITHUB_OUTPUT
            else
              echo "type=patch" >> $GITHUB_OUTPUT
            fi
          fi

      - name: Bump version
        id: version
        run: |
          npm version ${{ steps.version_type.outputs.type }} --no-git-tag-version
          VERSION=$(node -p "require('./package.json').version")
          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Generate changelog
        id: changelog
        uses: TriPSs/conventional-changelog-action@v5
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          skip-version-file: true
          output-file: false
          pre-release: false
          release-count: 1

      - name: Update CHANGELOG.md
        run: |
          if [ ! -f CHANGELOG.md ]; then
            echo "# Changelog" > CHANGELOG.md
            echo "" >> CHANGELOG.md
            echo "All notable changes to this project will be documented in this file." >> CHANGELOG.md
            echo "" >> CHANGELOG.md
          fi
          
          # Create temporary file with new entry
          echo "## [${{ steps.version.outputs.version }}] - $(date +%Y-%m-%d)" > temp_changelog.md
          echo "" >> temp_changelog.md
          echo "${{ steps.changelog.outputs.clean_changelog }}" >> temp_changelog.md
          echo "" >> temp_changelog.md
          
          # Append existing changelog
          tail -n +2 CHANGELOG.md >> temp_changelog.md
          
          # Replace with new changelog
          echo "# Changelog" > CHANGELOG.md
          echo "" >> CHANGELOG.md
          echo "All notable changes to this project will be documented in this file." >> CHANGELOG.md
          echo "" >> CHANGELOG.md
          cat temp_changelog.md >> CHANGELOG.md
          rm temp_changelog.md

      - name: Commit changes
        run: |
          git add package.json package-lock.json CHANGELOG.md
          git commit -m "chore(release): v${{ steps.version.outputs.version }}"
          git tag -a "v${{ steps.version.outputs.version }}" -m "Release v${{ steps.version.outputs.version }}"

      - name: Push changes
        run: |
          git push origin main
          git push origin "v${{ steps.version.outputs.version }}"

      - name: Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ steps.version.outputs.version }}
          release_name: Release v${{ steps.version.outputs.version }}
          body: ${{ steps.changelog.outputs.clean_changelog }}
          draft: false
          prerelease: false

      - name: Update release notes
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ steps.version.outputs.version }}
          body: |
            ## What's Changed
            ${{ steps.changelog.outputs.clean_changelog }}
            
            **Full Changelog**: https://github.com/${{ github.repository }}/compare/v${{ steps.changelog.outputs.previous_version }}...v${{ steps.version.outputs.version }}
          generate_release_notes: true
