version: 2
updates:
  # Enable version updates for npm
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    reviewers:
      - "beehtrix"
    labels:
      - "dependencies"
      - "npm"
    commit-message:
      prefix: "chore"
      include: "scope"
    groups:
      radix-ui:
        patterns:
          - "@radix-ui/*"
      testing:
        patterns:
          - "@testing-library/*"
          - "vitest*"
          - "@vitest/*"
          - "cypress"
      react:
        patterns:
          - "react"
          - "react-dom"
          - "@types/react*"
      supabase:
        patterns:
          - "@supabase/*"

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    reviewers:
      - "beehtrix"
    labels:
      - "dependencies"
      - "github-actions"
    commit-message:
      prefix: "chore"
      include: "scope"
