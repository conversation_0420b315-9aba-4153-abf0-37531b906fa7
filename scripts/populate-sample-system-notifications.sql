-- Sample data for system_health and notifications tables
-- Replace 'your-user-id-here' with your actual user ID: 66e968bb-aa2e-4b57-aabb-6a1bb7f90466

-- Insert sample system health data
INSERT INTO public.system_health (user_id, name, status, uptime, response_time) VALUES
('66e968bb-aa2e-4b57-aabb-6a1bb7f90466', 'Web Application', 'operational', 99.9, 120),
('66e968bb-aa2e-4b57-aabb-6a1bb7f90466', 'Database', 'operational', 99.8, 45),
('66e968bb-aa2e-4b57-aabb-6a1bb7f90466', 'API Server', 'operational', 99.7, 85),
('66e968bb-aa2e-4b57-aabb-6a1bb7f90466', 'Email Service', 'degraded', 98.5, 450);

-- Insert sample notifications data  
INSERT INTO public.notifications (user_id, type, title, message, read, metadata) VALUES
('66e968bb-aa2e-4b57-aabb-6a1bb7f90466', 'interview', 'Upcoming Interview', 'Interview with <PERSON>e scheduled for today at 2:00 PM', false, '{"candidateId": "sample-candidate-1"}'),
('66e968bb-aa2e-4b57-aabb-6a1bb7f90466', 'message', 'New Message', 'Sarah <PERSON> sent you a message about the Frontend position', false, '{"senderId": "sample-user-1"}'),
('66e968bb-aa2e-4b57-aabb-6a1bb7f90466', 'candidate', 'New Application', 'Michael Chen applied for Senior React Developer position', true, '{"candidateId": "sample-candidate-2", "jobId": "sample-job-1"}'),
('66e968bb-aa2e-4b57-aabb-6a1bb7f90466', 'deadline', 'Application Deadline', 'Frontend Developer position closes in 3 days', false, '{"jobId": "sample-job-2"}'),
('66e968bb-aa2e-4b57-aabb-6a1bb7f90466', 'system', 'System Health Alert', 'Email service is experiencing degraded performance', false, '{"service": "email", "status": "degraded"}'); 